package chatbot

import (
	"encoding/gob"
	"fmt"
	"math"
	"os"
	"regexp"
	"strings"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/spf13/viper"
	"gonum.org/v1/gonum/mat"
)

type LogisticRegressionModel struct {
	cfg     *config.Config
	dict    *WordDict
	Vocabs  []string
	Classes []string
	Thetas  *mat.Dense
	DF      map[string]float64
	NDoc    float64
}

func NewLGModel(cfg *config.Config, dict *WordDict) (*LogisticRegressionModel, error) {
	// Check model file exists
	lgModel := &LogisticRegressionModel{
		dict: dict,
		cfg:  cfg,
	}
	modelPath := fmt.Sprintf("%s/lgonevsrest.model", cfg.StoragePath.Model)
	if _, err := os.Stat(modelPath); os.IsNotExist(err) {
		// Train model
		err = lgModel.Train(cfg.ChatScopes, 0.5)
		if err != nil {
			return nil, err
		}
		return lgModel, nil
	}
	// Load model
	f, err := os.Open(modelPath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	decoder := gob.NewDecoder(f)
	err = decoder.Decode(lgModel)
	if err != nil {
		return nil, err
	}
	return lgModel, nil
}

func (lg *LogisticRegressionModel) Predict(sentence string, threshold float64) (string, error) {
	// Predict
	predictClass, score, err := lg.PredictWithScore(sentence)
	if err != nil {
		return "", err
	}
	// fmt.Printf("Argmax Class: %s -> %f Cosine similarity: %f\n", argMax, predict[argMax], cosine)
	if score > threshold {
		return predictClass, nil
	}
	return "", nil
}

func (lg *LogisticRegressionModel) PredictWithScore(sentence string) (string, float64, error) {
	tokens := lg.Tokenizer(sentence)
	X := mat.NewVecDense(len(lg.Vocabs)+1, nil)
	for _, token := range tokens {
		for i, vocab := range lg.Vocabs {
			if vocab == token {
				X.SetVec(i+1, X.AtVec(i)+1)
			}
		}
	}
	// Add bias
	X.SetVec(0, 1)

	h := new(mat.Dense)
	h.Mul(lg.Thetas, X)
	h.Apply(sigmoid, h)

	max := 0.0
	argMax := ""
	for i, v := range h.RawMatrix().Data {
		if max < v {
			max = v
			argMax = lg.Classes[i]
		}
	}
	score, err := lg.CosineSimilarity(argMax, tokens)
	if err != nil {
		return "", 0.0, err
	}
	return argMax, score, nil
	// fh := mat.Formatted(h, mat.Prefix("    "), mat.Squeeze())
	// fmt.Printf("Y = %v\n", fh)
}

func (lg *LogisticRegressionModel) Train(scopes []string, alpha float64) error {
	r := regexp.MustCompile(`(\((?P<text>[^\)]+)\)\[(?P<variable>[^\]]+)\])`)
	uniqueVocabs := make(map[string]float64)
	// Ndoc = number of documents
	lg.NDoc = 0.0
	lg.DF = make(map[string]float64)

	type ClassDoc struct {
		Class  string
		Tokens []string
	}
	var classDocs []ClassDoc
	countwc := make(map[string]float64)
	for _, scope := range scopes {
		viper.SetConfigName(scope)
		viper.AddConfigPath(lg.cfg.TemplatePath.ChatPath)
		viper.SetConfigType("yaml")
		if err := viper.ReadInConfig(); err != nil {
			return err
		}
		var cb *model.Chatbot
		err := viper.Unmarshal(&cb)
		if err != nil {
			return err
		}

		// Counting
		for _, wf := range cb.Workflows {
			// namespace
			class := fmt.Sprintf("%s/%s", scope, wf.Topic)
			if class == "scheduler/default" {
				continue
			}
			lg.Classes = append(lg.Classes, class)
			for _, kw := range wf.Keywords {
				lg.NDoc++
				processedText := r.ReplaceAllString(kw, "${text}")
				tokens := lg.Tokenizer(processedText)
				classDocs = append(classDocs, ClassDoc{class, tokens})

				for _, token := range tokens {
					uniqueVocabs[token]++
					wtoclass := fmt.Sprintf("%s-%s", token, class)
					countwc[wtoclass]++
					lg.DF[token]++
				}
			}
		}
	}

	// Vectorize vocabs follow one vs all
	// Ordering
	for vocab := range uniqueVocabs {
		lg.Vocabs = append(lg.Vocabs, vocab)
	}
	// TF-IDF
	tf := make(map[string]float64)
	idf := make(map[string]float64)
	for _, token := range lg.Vocabs {
		for _, class := range lg.Classes {
			wtoclass := fmt.Sprintf("%s-%s", token, class)
			if countwc[wtoclass] > 0 {
				tf[wtoclass] = 1 + math.Log(countwc[wtoclass])
			}
		}
		idf[token] = math.Log(lg.NDoc / lg.DF[token])
	}
	X := mat.NewDense(len(classDocs), len(lg.Vocabs)+1, nil)
	for i, classDoc := range classDocs {
		for j, vocab := range lg.Vocabs {
			for _, token := range classDoc.Tokens {
				if token == vocab {
					wslashtoclass := fmt.Sprintf("%s-%s", token, classDoc.Class)
					// X.Set(i, j+1, X.At(i, j+1)+1)
					X.Set(i, j+1, tf[wslashtoclass]*idf[token])
				}
			}
		}
	}
	// Add bias
	for i := 0; i < len(classDocs); i++ {
		X.Set(i, 0, 1)
	}

	// One vs all
	Yset := make(map[string]*mat.VecDense)
	for _, class := range lg.Classes {
		Yset[class] = mat.NewVecDense(len(classDocs), nil)
		for i, classDoc := range classDocs {
			if classDoc.Class == class {
				Yset[class].SetVec(i, 1)
			}
		}
		// fv := mat.Formatted(Yset[class], mat.Prefix("    "), mat.Squeeze())
		// fmt.Printf("Y = %v\n", fv)
	}

	// Print the result using the formatter.
	// fv := mat.Formatted(X, mat.Prefix("    "), mat.Squeeze())
	// fmt.Printf("X = %v\n", fv)

	// Initial thetas
	lg.Thetas = mat.NewDense(len(lg.Classes), len(lg.Vocabs)+1, nil)

	// 0.001 <= alpha <= 10
	for i, class := range lg.Classes {
		epouch := 0
		for {
			grad := gradientDescent(alpha, lg.Thetas.RowView(i), X, Yset[class])
			lg.Thetas.SetRow(i, grad.RawVector().Data)
			// ftheta := mat.Formatted(thetas[class], mat.Prefix("    "), mat.Squeeze())
			// fmt.Printf("T = %v\n", ftheta)
			loss := lossFunction(grad, X, Yset[class])
			epouch++
			if int(loss*100) == 0 {
				fmt.Println("Class:", class, "Epouchs:", epouch, "Error:", loss)
				break
			}
		}
	}

	// Save gradient descent model, classes and vocabs
	modelPath := fmt.Sprintf("%s/lgonevsrest.model", lg.cfg.StoragePath.Model)
	f, err := os.Create(modelPath)
	if err != nil {
		return err
	}
	defer f.Close()
	encoder := gob.NewEncoder(f)
	err = encoder.Encode(lg)
	if err != nil {
		return err
	}
	return nil
}

func (lg *LogisticRegressionModel) CosineSimilarity(classPredicted string, tokens []string) (float64, error) {
	r := regexp.MustCompile(`(\((?P<text>[^\)]+)\)\[(?P<variable>[^\]]+)\])`)
	// Cosine similarity
	// Bag of words
	namespaces := strings.Split(classPredicted, "/")
	viper.SetConfigName(namespaces[0])
	viper.AddConfigPath(lg.cfg.TemplatePath.ChatPath)
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		return 0, err
	}
	var cb *model.Chatbot
	err := viper.Unmarshal(&cb)
	if err != nil {
		return 0, err
	}
	vocabs := make(map[string]float64)
	for _, wf := range cb.Workflows {
		if wf.Topic == namespaces[1] {
			for _, kw := range wf.Keywords {
				processedText := r.ReplaceAllString(kw, "${text}")
				tokens := lg.Tokenizer(processedText)
				for _, token := range tokens {
					vocabs[token]++
				}
			}
			break
		}
	}
	// Vector bow
	// TF-IDF
	df := make(map[string]float64)
	for token, count := range lg.DF {
		df[token] = count
	}
	vectorTokens := make(map[string]float64)
	for token := range vocabs {
		vectorTokens[token]++
	}
	bow := make(map[string]float64)
	var orderedTokens []string
	countedTokens := make(map[string]float64)
	for _, token := range tokens {
		if _, ok := countedTokens[token]; !ok {
			df[token]++
		}
		bow[token]++
		vectorTokens[token]++
	}
	for token := range vectorTokens {
		orderedTokens = append(orderedTokens, token)
	}
	// tf
	ptf := make(map[string]float64)
	dtf := make(map[string]float64)
	idf := make(map[string]float64)
	nDoc := lg.NDoc + 1
	for _, token := range orderedTokens {
		if _, ok := bow[token]; ok {
			ptf[token] = 1 + math.Log(bow[token])
		}
		if _, ok := vocabs[token]; ok {
			dtf[token] = 1 + math.Log(vocabs[token])
		}
		if _, ok := df[token]; ok {
			idf[token] = math.Log(nDoc / df[token])
		}
	}
	predictVec := mat.NewVecDense(len(vectorTokens), nil)
	docVec := mat.NewVecDense(len(vectorTokens), nil)
	for i, token := range orderedTokens {
		predictVec.SetVec(i, ptf[token]*idf[token])
		docVec.SetVec(i, dtf[token]*idf[token])
	}

	cosine := mat.Dot(predictVec, docVec) / (mat.Norm(predictVec, 2) * mat.Norm(docVec, 2))
	// Cosine lie between 0 and 1
	return cosine, nil
}

func gradientDescent(alpha float64, theta mat.Vector, X mat.Matrix, Y *mat.VecDense) *mat.VecDense {
	// theta = theta - alpha * 1/m * sum((hTheta(X) - Y) * X)
	m := float64(Y.Len())

	HthetaX := hypothesis(theta, X)
	HtheraXSubY := new(mat.Dense)
	HtheraXSubY.Sub(HthetaX, Y)
	HtheraXSubYX := new(mat.Dense)
	HtheraXSubYX.Mul(X.T(), HtheraXSubY)

	HtheraXSubYX.Apply(func(i, j int, v float64) float64 {
		return alpha * (v / m)
	}, HtheraXSubYX)

	grad := new(mat.VecDense)
	grad.SubVec(theta, HtheraXSubYX.ColView(0))
	return grad
}

func lossFunction(theta mat.Vector, X mat.Matrix, Y mat.Vector) float64 {
	// J(theta) = 1/m * sum(-Y*log(hTheta(X)) - (1-Y)log(1-hTheta(X)))

	HthetaX := hypothesis(theta, X)
	//-Y*log(hTheta(X))
	logH := log(HthetaX)
	nY := new(mat.Dense)
	nY.Apply(func(i, j int, v float64) float64 {
		return -1 * v
	}, Y)
	dotYlogH := new(mat.Dense)
	dotYlogH.MulElem(nY, logH)

	// (1-Y)log(1-hTheta(X))
	nHthetaX := new(mat.Dense)
	nHthetaX.Apply(func(i, j int, v float64) float64 {
		return 1 - v
	}, HthetaX)
	lognH := log(nHthetaX)

	oneNY := new(mat.Dense)
	oneNY.Apply(func(i, j int, v float64) float64 {
		return 1 - v
	}, Y)

	dotYnLognH := new(mat.Dense)
	dotYnLognH.MulElem(oneNY, lognH)

	JTheta := new(mat.Dense)
	JTheta.Sub(dotYlogH, dotYnLognH)
	sum := 0.0
	m := float64(Y.Len())
	for _, v := range JTheta.RawMatrix().Data {
		sum += v
	}

	return sum / m
}

func hypothesis(theta mat.Vector, X mat.Matrix) mat.Matrix {
	hX := new(mat.Dense)
	hX.Mul(X, theta)
	hX.Apply(sigmoid, hX)
	return hX
}

func log(M mat.Matrix) mat.Matrix {
	logM := new(mat.Dense)
	logM.Apply(func(i, j int, v float64) float64 {
		return math.Log(v)
	}, M)
	return logM
}

func sigmoid(r, c int, z float64) float64 {
	return 1 / (1 + math.Exp(-z))
}

func (lg *LogisticRegressionModel) Tokenizer(sentence string) []string {
	// Lower case
	sentence = strings.ToLower(sentence)
	// Strip punctuation
	punctuationReg := regexp.MustCompile(`[\@\$\/\#\.\-\:\&\*\+\=\[\]\?\!\(\)\{\}\,\''\"\>\_\<\;\%]+`)
	sentence = punctuationReg.ReplaceAllString(sentence, " ")
	// Handle number
	numberReg := regexp.MustCompile(`[0-9]+`)
	sentence = numberReg.ReplaceAllString(sentence, constants.GenericFeatures.NUMBER)
	// Handle URLs
	urlReg := regexp.MustCompile(`(http|https)://[^\s]*`)
	sentence = urlReg.ReplaceAllString(sentence, constants.GenericFeatures.HTTPADDR)
	// Handle email address
	emailReg := regexp.MustCompile(`[^\s]+@[^\s]+`)
	sentence = emailReg.ReplaceAllString(sentence, constants.GenericFeatures.EMAILADDR)
	// Handle $ sign
	dollarReg := regexp.MustCompile(`[$]+`)
	sentence = dollarReg.ReplaceAllString(sentence, constants.GenericFeatures.DOLLARSIGN)
	// Trim space
	spaceReg := regexp.MustCompile(`\s+`)
	sentence = spaceReg.ReplaceAllString(sentence, " ")
	sentence = strings.Trim(sentence, " ")

	sWords := strings.Split(sentence, " ")
	nGram := lg.dict.MaxGram
	var tokens []string
	lenSW := len(sWords)
	i := 0
	for i < lenSW {
		word := sWords[i]
		// max ngram
		maxGram := nGram
		if maxGram > (lenSW - i) {
			maxGram = lenSW - i
		}

		// Match longest ngram
		token := word
		indexMatch := i + 1
		for j := maxGram; j > 0; j-- {
			bow := sWords[i : i+j]
			dictW := strings.Join(bow, " ")
			// Check if word is personal pronouns
			_, okPerPronouns := lg.dict.PersonalFeatures[dictW]
			if okPerPronouns {
				token = constants.GenericFeatures.PERSONALPRONOUN
				indexMatch = i + j
				break
			}
			_, okWord := lg.dict.Words[dictW]
			if okWord {
				token = dictW
				indexMatch = i + j
				break
			} else if j == 1 {
				// Single word not in dictionary check stock features
				_, okStock := lg.dict.StockFeatures[dictW]
				if okStock {
					token = constants.GenericFeatures.STOCK
					indexMatch = i + j
					break
				}
			}
		}
		tokens = append(tokens, token)
		i = indexMatch // jump to next match
	}
	return tokens
}
