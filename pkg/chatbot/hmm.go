package chatbot

import (
	"encoding/gob"
	"errors"
	"fmt"
	"math"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/looplab/fsm"
	"github.com/spf13/viper"
	"gonum.org/v1/gonum/mat"
)

type HMMModel struct {
	Emission         *mat.Dense
	Transition       *mat.Dense
	Tokens           []string
	Tags             []string
	LongestGramVocab int
	dict             *WordDict
}

type WordTag struct {
	Word string
	Tag  string
}

type TokenOrigin struct {
	Token  string
	Origin string
}

func NewHMM(cfg *config.Config, class string, dict *WordDict) (*HMMModel, error) {
	hmm := &HMMModel{
		dict: dict,
	}
	r := regexp.MustCompile(`(\((?P<text>[^\)]+)\)\[(?P<variable>[^\]]+)\])`)
	modelPath := fmt.Sprintf("%s/%s.model", cfg.StoragePath.Model, class)
	if _, err := os.Stat(modelPath); os.IsNotExist(err) {
		// Train model
		namespaces := strings.Split(class, "/")
		scope := namespaces[0]
		topic := namespaces[1]
		viper.SetConfigName(scope)
		viper.AddConfigPath(cfg.TemplatePath.ChatPath)
		viper.SetConfigType("yaml")
		if err := viper.ReadInConfig(); err != nil {
			return nil, err
		}
		var cb *model.Chatbot
		err := viper.Unmarshal(&cb)
		if err != nil {
			panic(err)
		}
		for _, wf := range cb.Workflows {
			if wf.Topic == topic {
				var texts []string
				for _, kw := range wf.Keywords {
					if r.MatchString(kw) {
						// train hmm for class
						texts = append(texts, kw)
					}
				}
				if len(texts) > 0 {
					err := hmm.TrainHMMModel(class, texts, cfg)
					if err != nil {
						return nil, err
					}
				} else {
					return nil, errors.New("No keywords found")
				}
				break
			}
		}
		return hmm, nil
	}
	// Load model
	f, err := os.Open(modelPath)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	decoder := gob.NewDecoder(f)
	err = decoder.Decode(hmm)
	if err != nil {
		return nil, err
	}
	return hmm, nil
}

func TrainAllScopes(cfg *config.Config, dict *WordDict) error {
	r := regexp.MustCompile(`(\((?P<text>[^\)]+)\)\[(?P<variable>[^\]]+)\])`)
	for _, scope := range cfg.ChatScopes {
		viper.SetConfigName(scope)
		viper.AddConfigPath(cfg.TemplatePath.ChatPath)
		viper.SetConfigType("yaml")
		if err := viper.ReadInConfig(); err != nil {
			return err
		}
		var cb *model.Chatbot
		err := viper.Unmarshal(&cb)
		if err != nil {
			return err
		}
		for _, wf := range cb.Workflows {
			var texts []string
			for _, kw := range wf.Keywords {
				if r.MatchString(kw) {
					// train hmm for class
					texts = append(texts, kw)
				}
			}
			if len(texts) > 0 {
				classPredicted := fmt.Sprintf("%s/%s", scope, wf.Topic)
				_, err := NewHMM(cfg, classPredicted, dict)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func (hmm *HMMModel) Entities(sentence string) []map[string]string {
	tokenOrigins := hmm.TokenizeOrigin(sentence)
	pTags := hmm.Viterbi(tokenOrigins)
	lenTokens := len(tokenOrigins)
	var wordTagged []*WordTag
	for i := 0; i < lenTokens; i++ {
		wordTagged = append(wordTagged, &WordTag{
			Word: tokenOrigins[i].Origin,
			Tag:  pTags[i],
		})
	}
	// NER
	var collapseResult []map[string]string
	collapsed := make(map[string]string)
	var currTag string
	regBI := regexp.MustCompile(`^(B\-|I\-)`)
	for _, wt := range wordTagged {
		if strings.Contains(wt.Tag, "B-") {
			if len(collapsed) > 0 {
				collapseResult = append(collapseResult, collapsed)
				collapsed = make(map[string]string)
			}
			currTag = regBI.ReplaceAllString(wt.Tag, "")
			collapsed[currTag] = wt.Word
		} else if strings.Contains(wt.Tag, "I-") && fmt.Sprintf("I-%s", currTag) == wt.Tag {
			if strings.Contains(wt.Word, constants.GenericFeatures.EMPTYSPACE) {
				word := strings.ReplaceAll(wt.Word, constants.GenericFeatures.EMPTYSPACE, "")
				collapsed[currTag] = fmt.Sprintf("%s%s", collapsed[currTag], word)
			} else {
				collapsed[currTag] = fmt.Sprintf("%s %s", collapsed[currTag], wt.Word)
			}
		} else {
			if len(collapsed) > 0 {
				collapseResult = append(collapseResult, collapsed)
			}
			collapsed = make(map[string]string)
			currTag = regBI.ReplaceAllString(wt.Tag, "")
		}
	}
	// Check remain collapsed
	if len(collapsed) > 0 {
		collapseResult = append(collapseResult, collapsed)
	}
	return collapseResult
}

func (hmm *HMMModel) Viterbi(tokens []*TokenOrigin) []string {
	tokens = append([]*TokenOrigin{
		{
			Token:  "<sol>",
			Origin: "<sol>",
		},
	}, tokens...)
	numberWord := len(tokens)
	numberTag := len(hmm.Tags)
	// Viterbi
	// initialization
	viterbi := mat.NewDense(numberTag, numberWord, nil)
	backpointer := mat.NewDense(numberTag, numberWord, nil)

	startTagIdx := 0
	for i, tag := range hmm.Tags {
		if tag == "<s>" {
			startTagIdx = i
			break
		}
	}

	wordIndices := make(map[string]int)
	for i, word := range hmm.Tokens {
		wordIndices[word] = i
	}
	for i := 0; i < numberTag; i++ {
		idx, ok := wordIndices[tokens[0].Token]
		pTW := 0.0
		if ok {
			pTW = hmm.Emission.At(i, idx)
		}
		p := hmm.Transition.At(startTagIdx, i) + pTW
		viterbi.Set(i, 0, p)
	}

	// recursion step
	// bestProbs[j,i] = \max_{k}bestProbs_{k, i-1} * transitionMatrix_{k,j} * emissionMatrix_{j, {index of word i}}
	for i := 1; i < numberWord; i++ {
		for j := 0; j < numberTag; j++ {
			bestProbToGetToWordIFromTagJ := math.Inf(-1)
			bestPathToWordI := 0
			for k := 0; k < numberTag; k++ {
				idx, ok := wordIndices[tokens[i].Token]
				pTW := math.Log(1.0)
				if ok {
					pTW = hmm.Emission.At(k, idx)
				}
				tmpProb := viterbi.At(k, i-1) + hmm.Transition.At(k, j) + pTW
				if tmpProb > bestProbToGetToWordIFromTagJ {
					bestProbToGetToWordIFromTagJ = tmpProb
					bestPathToWordI = k
				}
			}
			viterbi.Set(j, i, bestProbToGetToWordIFromTagJ)
			backpointer.Set(j, i, float64(bestPathToWordI))
		}
	}
	// Print the result using the formatter.
	// fv := mat.Formatted(viterbi, mat.Prefix("    "), mat.Squeeze())
	// fmt.Printf("v = %v\n", fv)
	// Print the result using the formatter.
	// fb := mat.Formatted(backpointer, mat.Prefix("    "), mat.Squeeze())
	// fmt.Printf("b = %v\n", fb)

	var bestProbForLastWord = math.Inf(-1)
	var posPredictions []string
	tags := make(map[int]string)
	z := make([]int, numberWord)

	for i, tag := range hmm.Tags {
		tags[i] = tag
	}

	// finding the index of the cell with the highest probability in the last column of the bestProbs
	for i := 0; i < numberTag; i++ {
		if viterbi.At(i, numberWord-1) > bestProbForLastWord {
			bestProbForLastWord = viterbi.At(i, numberWord-1)
			z[numberWord-1] = i
		}
	}
	posPredictions = append([]string{tags[z[numberWord-1]]}, posPredictions...)

	// traversing the bestPaths backwards.
	// each current cell contains the row index of the cell to go to in the next column
	for i := numberWord - 1; i > 0; i-- {
		tagForWordI := backpointer.At(z[i], i)
		z[i-1] = int(tagForWordI)
		posPredictions = append([]string{tags[z[i-1]]}, posPredictions...)
	}
	return posPredictions
}

func (hmm *HMMModel) TrainHMMModel(class string, texts []string, cfg *config.Config) error {
	uniqueTag := make(map[string]float64)
	transitionTag := make(map[string]float64)
	vocabs := make(map[string]float64)
	emissionWord := make(map[string]float64)

	for _, text := range texts {
		// processedText := r.ReplaceAllString(text, "$2")
		// fmt.Println(processedText)
		tokens := hmm.TokenizernTag(text)
		startTag := &WordTag{
			Word: "<sol>",
			Tag:  "<s>",
		}
		tokens = append([]*WordTag{startTag}, tokens...)
		for i, token := range tokens {
			uniqueTag[token.Tag]++
			vocabs[token.Word]++
			lenW := len(strings.Split(token.Word, " "))
			if hmm.LongestGramVocab < lenW {
				hmm.LongestGramVocab = lenW
			}
			if i > 0 {
				prevToken := tokens[i-1]
				tTagStr := fmt.Sprintf("%s-%s", prevToken.Tag, token.Tag)
				transitionTag[tTagStr]++
			}
			wordTag := fmt.Sprintf("%s-%s", token.Tag, token.Word)
			emissionWord[wordTag]++
			// fmt.Printf("%s %s\n", token.Word, token.Tag)
		}
	}

	// transition table
	alpha := 1.0
	numberTag := len(uniqueTag)
	hmm.Transition = mat.NewDense(numberTag, numberTag, nil)
	// calculate transition probability P(t_i | t_{i-1}) = C(t_{i-1}, t_{i}) + alpha / C(t_{i-1}) + alpha * N}
	for tag := range uniqueTag {
		hmm.Tags = append(hmm.Tags, tag)
	}
	// fmt.Println(tagOrder)
	for i := 0; i < numberTag; i++ {
		for j := 0; j < numberTag; j++ {
			tTagStr := fmt.Sprintf("%s-%s", hmm.Tags[i], hmm.Tags[j])
			p := (transitionTag[tTagStr] + alpha) / (uniqueTag[hmm.Tags[i]] + alpha*float64(numberTag))
			hmm.Transition.Set(i, j, math.Log(p))
		}
	}

	// Print the result using the formatter.
	// ft := mat.Formatted(t, mat.Prefix("    "), mat.Squeeze())
	// fmt.Printf("t = %v\n", ft)

	// emission table
	numberWord := len(vocabs)
	hmm.Emission = mat.NewDense(numberTag, numberWord, nil)

	// calculate emission probability P(w_i | t_i) = \frac{C(t_i, word_i)+ \alpha}{C(t_{i}) +\alpha * N}
	for word := range vocabs {
		hmm.Tokens = append(hmm.Tokens, word)
	}
	// fmt.Println(wordOrder)
	for i := 0; i < numberTag; i++ {
		for j := 0; j < numberWord; j++ {
			eWordTagStr := fmt.Sprintf("%s-%s", hmm.Tags[i], hmm.Tokens[j])
			p := (emissionWord[eWordTagStr] + alpha) / (uniqueTag[hmm.Tags[i]] + alpha*float64(numberWord))
			hmm.Emission.Set(i, j, math.Log(p))
		}
	}
	// Save emission & transition & tokens & tags into class file model
	modelPath := fmt.Sprintf("%s/%s.model", cfg.StoragePath.Model, class)
	dirModelOnly := filepath.Dir(modelPath)
	if _, err := os.Stat(dirModelOnly); errors.Is(err, os.ErrNotExist) {
		err := os.MkdirAll(dirModelOnly, os.ModePerm)
		if err != nil {
			return err
		}
	}
	f, err := os.Create(modelPath)
	if err != nil {
		return err
	}
	defer f.Close()
	encoder := gob.NewEncoder(f)
	err = encoder.Encode(hmm)
	if err != nil {
		return err
	}
	return nil
}

func (hmm *HMMModel) TokenizernTag(sentence string) []*WordTag {
	// Lower case
	sentence = strings.ToLower(sentence)
	// Strip punctuation
	punctuationReg := regexp.MustCompile(`[\@\$\/\#\.\-\:\&\*\+\=\?\!\{\}\,\''\"\>\<\;\%]+`)
	sentence = punctuationReg.ReplaceAllString(sentence, " ")
	// Handle number
	numberReg := regexp.MustCompile(`[0-9]+`)
	sentence = numberReg.ReplaceAllString(sentence, constants.GenericFeatures.NUMBER)
	// Handle URLs
	urlReg := regexp.MustCompile(`(http|https)://[^\s]*`)
	sentence = urlReg.ReplaceAllString(sentence, constants.GenericFeatures.HTTPADDR)
	// Handle email address
	emailReg := regexp.MustCompile(`[^\s]+@[^\s]+`)
	sentence = emailReg.ReplaceAllString(sentence, constants.GenericFeatures.EMAILADDR)
	// Handle $ sign
	dollarReg := regexp.MustCompile(`[$]+`)
	sentence = dollarReg.ReplaceAllString(sentence, constants.GenericFeatures.DOLLARSIGN)

	rTag := regexp.MustCompile(`\)\[([^\]]+)\]`)

	sWords := strings.Split(sentence, " ")
	nGram := hmm.dict.MaxGram
	var tokens []*WordTag
	lenSW := len(sWords)
	i := 0
	inside := ""
	// Finite state machine
	fsm := fsm.NewFSM(
		"closed",
		fsm.Events{
			{Name: "open", Src: []string{"closed"}, Dst: "open"},
			{Name: "close", Src: []string{"open"}, Dst: "closed"},
		},
		fsm.Callbacks{},
	)
	for i < lenSW {
		word := sWords[i]
		// max ngram
		maxGram := nGram
		if maxGram > (lenSW - i) {
			maxGram = lenSW - i
		}

		// Match longest ngram
		if strings.Contains(word, "(") {
			fsm.Event("open")
		}
		if strings.Contains(word, ")") {
			fsm.Event("close")
		}
		wordStripped := strings.ReplaceAll(word, "(", "")
		wordStripped = rTag.ReplaceAllString(wordStripped, "")
		token := &WordTag{
			Word: wordStripped,
			Tag:  "O",
		}

		indexMatch := i + 1
		dictW := word
		for j := maxGram; j > 0; j-- {
			bow := sWords[i : i+j]
			dictW = strings.Join(bow, " ")
			dictWStripped := strings.ReplaceAll(dictW, "(", "")
			dictWStripped = rTag.ReplaceAllString(dictWStripped, "")
			// Check if word is personal pronouns
			_, okPerPronouns := hmm.dict.PersonalFeatures[dictWStripped]
			if okPerPronouns {
				token.Word = constants.GenericFeatures.PERSONALPRONOUN
				indexMatch = i + j
				break
			}
			_, okWord := hmm.dict.Words[dictWStripped]
			if okWord {
				token.Word = dictWStripped
				indexMatch = i + j
				break
			} else if j == 1 {
				// Single word not in dictionary check stock features
				_, okStock := hmm.dict.StockFeatures[dictWStripped]
				if okStock {
					token.Word = constants.GenericFeatures.STOCK
					indexMatch = i + j
					break
				}
			}
		}
		if strings.Contains(dictW, "(") {
			fsm.Event("open")
		}
		if strings.Contains(dictW, ")") {
			fsm.Event("close")
		}
		isOpenthenClose := strings.Contains(dictW, "(") && strings.Contains(dictW, ")")
		// Tagging
		if (fsm.Is("open") || isOpenthenClose) && inside == "" {
			// seek label
			for k := i; k < lenSW; k++ {
				nextWord := sWords[k]
				if strings.Contains(nextWord, ")") {
					rs := rTag.FindStringSubmatch(nextWord)
					if len(rs) > 1 {
						inside = rs[1]
						break
					}
				}
			}
			token.Tag = fmt.Sprintf("B-%s", inside)
			if isOpenthenClose {
				inside = ""
			}
		} else if fsm.Is("open") && inside != "" {
			token.Tag = fmt.Sprintf("I-%s", inside)
		} else if fsm.Is("closed") && inside != "" {
			token.Tag = fmt.Sprintf("I-%s", inside)
			inside = ""
		}
		tokens = append(tokens, token)
		i = indexMatch // jump to next match
	}
	return tokens
}

func (hmm *HMMModel) TokenizeOrigin(sentence string) []*TokenOrigin {
	// Lower case
	sentence = strings.ToLower(sentence)
	// Strip punctuation
	punctuationReg := regexp.MustCompile(`([\@\$\/\#\.\-\:\&\*\+\=\[\]\?\!\(\)\{\}\,\''\"\>\_\<\;\%]+)`)
	sentence = punctuationReg.ReplaceAllString(sentence, fmt.Sprintf(" %s$1", constants.GenericFeatures.EMPTYSPACE))
	// Handle number
	numberReg := regexp.MustCompile(`[0-9]+`)
	// Handle URLs
	urlReg := regexp.MustCompile(`(http|https)://[^\s]*`)
	// Handle email address
	emailReg := regexp.MustCompile(`[^\s]+@[^\s]+`)
	// Handle $ sign
	dollarReg := regexp.MustCompile(`[$]+`)

	sWords := strings.Split(sentence, " ")

	vocabs := make(map[string]int)
	for _, word := range hmm.Tokens {
		vocabs[word]++
	}
	nGram := hmm.LongestGramVocab
	var tokens []*TokenOrigin
	lenSW := len(sWords)
	i := 0
	for i < lenSW {
		word := sWords[i]
		// max ngram
		maxGram := nGram
		if maxGram > (lenSW - i) {
			maxGram = lenSW - i
		}

		stripped := strings.ReplaceAll(word, constants.GenericFeatures.EMPTYSPACE, "")
		stripped = punctuationReg.ReplaceAllString(stripped, "")
		stripped = numberReg.ReplaceAllString(stripped, constants.GenericFeatures.NUMBER)
		stripped = urlReg.ReplaceAllString(stripped, constants.GenericFeatures.HTTPADDR)
		stripped = emailReg.ReplaceAllString(stripped, constants.GenericFeatures.EMAILADDR)
		stripped = dollarReg.ReplaceAllString(stripped, constants.GenericFeatures.DOLLARSIGN)
		stripped = strings.Trim(stripped, " ")
		token := &TokenOrigin{
			Token:  stripped,
			Origin: word,
		}
		// Match longest ngram
		indexMatch := i + 1
		for j := maxGram; j > 0; j-- {
			bow := sWords[i : i+j]
			dictW := strings.Join(bow, " ")
			dictStripped := strings.ReplaceAll(dictW, constants.GenericFeatures.EMPTYSPACE, "")
			dictStripped = punctuationReg.ReplaceAllString(dictStripped, "")
			dictStripped = numberReg.ReplaceAllString(dictStripped, constants.GenericFeatures.NUMBER)
			dictStripped = urlReg.ReplaceAllString(dictStripped, constants.GenericFeatures.HTTPADDR)
			dictStripped = emailReg.ReplaceAllString(dictStripped, constants.GenericFeatures.EMAILADDR)
			dictStripped = dollarReg.ReplaceAllString(dictStripped, constants.GenericFeatures.DOLLARSIGN)
			// Check if word is personal pronouns
			_, okPerPronouns := hmm.dict.PersonalFeatures[dictStripped]
			if okPerPronouns {
				token.Token = constants.GenericFeatures.PERSONALPRONOUN
				token.Origin = dictW
				indexMatch = i + j
				break
			}
			_, okWord := vocabs[dictStripped]
			if okWord {
				token.Token = dictStripped
				token.Origin = dictW
				indexMatch = i + j
				break
			} else if j == 1 {
				// Single word not in dictionary check stock features
				_, okStock := hmm.dict.StockFeatures[dictW]
				if okStock {
					token.Token = constants.GenericFeatures.STOCK
					token.Origin = dictW
					indexMatch = i + j
					break
				}
			}
		}
		tokens = append(tokens, token)
		i = indexMatch // jump to next match
	}
	return tokens
}
