package chatbot

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/stretchr/testify/assert"
)

func setupTestFiles(t *testing.T) (string, func()) {
	// Create a temporary directory for test files
	tempDir, err := os.MkdirTemp("", "chatbot_test")
	assert.NoError(t, err)

	// Create test files with sample content
	files := map[string]string{
		"Viet74K.txt": "xin chào\nchào buổi sáng\nbuổi sáng tốt lành\n",
		"vietnamese-personal.txt": "Nguyễn Văn A\nTrần Thị B\nLê C\n",
		"vn-stocks.txt": "VNM\nFPT\nVIC\n",
	}

	for filename, content := range files {
		filePath := filepath.Join(tempDir, filename)
		err := os.WriteFile(filePath, []byte(content), 0644)
		assert.NoError(t, err)
	}

	// Return cleanup function
	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	return tempDir, cleanup
}

func TestNewDict(t *testing.T) {
	// Setup test files
	tempDir, cleanup := setupTestFiles(t)
	defer cleanup()

	tests := []struct {
		name          string
		setupConfig   func() *config.Config
		expectedError bool
		validate      func(*testing.T, *WordDict)
	}{
		{
			name: "Valid files and paths",
			setupConfig: func() *config.Config {
				return &config.Config{
					TemplatePath: &config.Template{
						PretrainPath: tempDir,
					},
				}
			},
			expectedError: false,
			validate: func(t *testing.T, dict *WordDict) {
				assert.NotNil(t, dict)
				
				// Check Words dictionary
				assert.Equal(t, 3, len(dict.Words))
				assert.Contains(t, dict.Words, "xin chào")
				assert.Contains(t, dict.Words, "chào buổi sáng")
				assert.Contains(t, dict.Words, "buổi sáng tốt lành")
				
				// Check MaxGram
				assert.Equal(t, 4, dict.MaxGram) // "buổi sáng tốt lành" has 4 words
				
				// Check PersonalFeatures
				assert.Equal(t, 3, len(dict.PersonalFeatures))
				assert.Contains(t, dict.PersonalFeatures, "Nguyễn Văn A")
				assert.Contains(t, dict.PersonalFeatures, "Trần Thị B")
				assert.Contains(t, dict.PersonalFeatures, "Lê C")
				
				// Check StockFeatures
				assert.Equal(t, 3, len(dict.StockFeatures))
				assert.Contains(t, dict.StockFeatures, "VNM")
				assert.Contains(t, dict.StockFeatures, "FPT")
				assert.Contains(t, dict.StockFeatures, "VIC")
			},
		},
		{
			name: "Invalid path",
			setupConfig: func() *config.Config {
				return &config.Config{
					TemplatePath: &config.Template{
						PretrainPath: "/nonexistent/path",
					},
				}
			},
			expectedError: true,
			validate: func(t *testing.T, dict *WordDict) {
				assert.Nil(t, dict)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := tt.setupConfig()
			dict, err := NewDict(cfg)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			tt.validate(t, dict)
		})
	}
}

func TestNewDictWithSpecialCharacters(t *testing.T) {
	// Setup test directory
	tempDir, err := os.MkdirTemp("", "chatbot_special_test")
	assert.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create test file with special characters
	content := "xin chào!!!\nchào buổi sáng@#$\nbuổi sáng tốt lành!!@@##\n"
	filePath := filepath.Join(tempDir, "Viet74K.txt")
	err = os.WriteFile(filePath, []byte(content), 0644)
	assert.NoError(t, err)

	// Create empty personal and stock files
	emptyFiles := []string{"vietnamese-personal.txt", "vn-stocks.txt"}
	for _, filename := range emptyFiles {
		path := filepath.Join(tempDir, filename)
		f, err := os.Create(path)
		assert.NoError(t, err)
		f.Close()
	}

	cfg := &config.Config{
		TemplatePath: &config.Template{
			PretrainPath: tempDir,
		},
	}

	dict, err := NewDict(cfg)
	assert.NoError(t, err)
	assert.NotNil(t, dict)

	// Verify special characters are removed
	assert.Contains(t, dict.Words, "xin chào")
	assert.Contains(t, dict.Words, "chào buổi sáng")
	assert.Contains(t, dict.Words, "buổi sáng tốt lành")
}

func TestNewDictWithEmptyFiles(t *testing.T) {
	// Setup test directory
	tempDir, err := os.MkdirTemp("", "chatbot_empty_test")
	assert.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create empty files
	files := []string{"Viet74K.txt", "vietnamese-personal.txt", "vn-stocks.txt"}
	for _, filename := range files {
		path := filepath.Join(tempDir, filename)
		f, err := os.Create(path)
		assert.NoError(t, err)
		f.Close()
	}

	cfg := &config.Config{
		TemplatePath: &config.Template{
			PretrainPath: tempDir,
		},
	}

	dict, err := NewDict(cfg)
	assert.NoError(t, err)
	assert.NotNil(t, dict)
	assert.Empty(t, dict.Words)
	assert.Empty(t, dict.PersonalFeatures)
	assert.Empty(t, dict.StockFeatures)
	assert.Equal(t, 0, dict.MaxGram)
}
