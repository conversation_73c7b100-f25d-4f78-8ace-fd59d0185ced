package chatbot

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"regexp"
	"strings"

	"github.com/007lock/simon-homestead/pkg/config"
)

type WordDict struct {
	Words            map[string]int
	PersonalFeatures map[string]int
	StockFeatures    map[string]int
	MaxGram          int
}

func NewDict(cfg *config.Config) (*WordDict, error) {
	reg, err := regexp.Compile(`[^\p{L}\d\s]+`)
	if err != nil {
		return nil, err
	}

	fd, err := os.Open(fmt.Sprintf("%s/Viet74K.txt", cfg.TemplatePath.PretrainPath))
	if err != nil {
		return nil, err
	}
	defer fd.Close()

	br := bufio.NewReader(fd)
	dictWords := make(map[string]int)
	longestGram := 0
	for {
		a, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}
		words := string(a)
		// Processes special char
		words = reg.ReplaceAllString(words, "")
		lenW := len(strings.Split(words, " "))
		if longestGram < lenW {
			longestGram = lenW
		}
		dictWords[words]++
	}

	fs, err := os.Open(fmt.Sprintf("%s/vietnamese-personal.txt", cfg.TemplatePath.PretrainPath))
	if err != nil {
		return nil, err
	}
	defer fs.Close()

	br = bufio.NewReader(fs)
	dictPerWords := make(map[string]int)
	for {
		a, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}
		words := string(a)
		dictPerWords[words]++
	}

	fst, err := os.Open(fmt.Sprintf("%s/vn-stocks.txt", cfg.TemplatePath.PretrainPath))
	if err != nil {
		return nil, err
	}
	defer fst.Close()

	br = bufio.NewReader(fst)
	dictStocks := make(map[string]int)
	for {
		a, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}
		word := string(a)
		dictStocks[word]++
	}

	return &WordDict{Words: dictWords, MaxGram: longestGram, PersonalFeatures: dictPerWords, StockFeatures: dictStocks}, nil
}
