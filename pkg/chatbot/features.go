package chatbot

import (
	"database/sql"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/pkg/config"
	"golang.org/x/net/context"
)

func DumpStockFromDB(db *sql.DB, cfg *config.Config) error {
	stockPath := fmt.Sprintf("%s/vn-stocks.txt", cfg.TemplatePath.PretrainPath)
	f, err := os.Create(stockPath)
	if err != nil {
		return err
	}
	defer f.Close()
	offset := 0
	for {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		r := db.QueryRowContext(ctx, fmt.Sprintf("SELECT id FROM stocks ORDER BY id LIMIT 1 OFFSET %d", offset))
		offset++

		var (
			stockCode string
		)
		if err := r.<PERSON>(&stockCode); err != nil {
			if err == sql.ErrNoRows {
				break
			}
			return err
		}
		f.WriteString(fmt.Sprintf("%s\n", strings.ToLower(stockCode)))
	}
	return nil
}
