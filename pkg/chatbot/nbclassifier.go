package chatbot

import (
	"encoding/gob"
	"fmt"
	"math"
	"os"
	"regexp"
	"strings"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/spf13/viper"
	"gonum.org/v1/gonum/mat"
)

type NaiveBayeModel struct {
	Classes       map[string]float64
	Vocabs        map[string]float64
	Logprior      map[string]float64
	Loglikelihood map[string]float64
	dict          *WordDict
	cfg           *config.Config
	DF            map[string]float64
	NDoc          float64
}

func NewNaiveBayes(scopes []string, cfg *config.Config, dict *WordDict) (*NaiveBayeModel, error) {
	// Check model file exists
	nbModel := &NaiveBayeModel{
		dict: dict,
		cfg:  cfg,
	}
	modelPath := fmt.Sprintf("%s/naivebayes.model", cfg.StoragePath.Model)
	if _, err := os.Stat(modelPath); os.IsNotExist(err) {
		// Train model
		err = nbModel.TrainNaiveBayes(scopes, cfg)
		if err != nil {
			return nil, err
		}
		return nbModel, nil
	}
	// Load model
	f, err := os.Open(modelPath)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	decoder := gob.NewDecoder(f)
	err = decoder.Decode(nbModel)
	if err != nil {
		return nil, err
	}
	return nbModel, nil
}

func (nbModel *NaiveBayeModel) PredictWithScore(sentence string) (string, float64, error) {
	// Predict
	predict := make(map[string]float64)
	tokens := nbModel.Tokenizer(sentence)
	// lenVocab := len(nbModel.Vocabs)
	for class := range nbModel.Classes {
		predict[class] += nbModel.Logprior[class]
		for _, token := range tokens {
			_, okWord := nbModel.Vocabs[token]
			if okWord {
				wtoclass := fmt.Sprintf("%s-%s", token, class)
				predict[class] += nbModel.Loglikelihood[wtoclass]
			}
		}
		// fmt.Printf("Class: %s -> %f\n", class, predict[class])
	}
	max := 0.0
	argMax := ""
	for class, p := range predict {
		if max == 0 || p > max {
			max = p
			argMax = class
		}
	}
	// fmt.Printf("Argmax Class: %s -> %f\n", argMax, predict[argMax])
	// Check cosine similarity
	score, err := nbModel.CosineSimilarity(argMax, tokens)
	if err != nil {
		return "", 0.0, err
	}
	// fmt.Printf("Argmax Class: %s -> %f Cosine similarity: %f\n", argMax, predict[argMax], cosine)
	return argMax, score, nil
}

func (nbModel *NaiveBayeModel) Predict(sentence string, threshold float64) (string, error) {
	// Predict
	predictClass, score, err := nbModel.PredictWithScore(sentence)
	if err != nil {
		return "", err
	}
	// fmt.Printf("Argmax Class: %s -> %f Cosine similarity: %f\n", argMax, predict[argMax], cosine)
	if score > threshold {
		return predictClass, nil
	}
	return "", nil
}

func (nbModel *NaiveBayeModel) CosineSimilarity(classPredicted string, tokens []string) (float64, error) {
	r := regexp.MustCompile(`(\((?P<text>[^\)]+)\)\[(?P<variable>[^\]]+)\])`)
	// Cosine similarity
	// Bag of words
	namespaces := strings.Split(classPredicted, "/")
	viper.SetConfigName(namespaces[0])
	viper.AddConfigPath(nbModel.cfg.TemplatePath.ChatPath)
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		return 0, err
	}
	var cb *model.Chatbot
	err := viper.Unmarshal(&cb)
	if err != nil {
		return 0, err
	}
	vocabs := make(map[string]float64)
	for _, wf := range cb.Workflows {
		if wf.Topic == namespaces[1] {
			for _, kw := range wf.Keywords {
				processedText := r.ReplaceAllString(kw, "${text}")
				tokens := nbModel.Tokenizer(processedText)
				for _, token := range tokens {
					vocabs[token]++
				}
			}
			break
		}
	}
	// Vector bow
	// TF-IDF
	df := make(map[string]float64)
	for token, count := range nbModel.DF {
		df[token] = count
	}
	vectorTokens := make(map[string]float64)
	for token := range vocabs {
		vectorTokens[token]++
	}
	bow := make(map[string]float64)
	var orderedTokens []string
	countedTokens := make(map[string]float64)
	for _, token := range tokens {
		if _, ok := countedTokens[token]; !ok {
			df[token]++
		}
		bow[token]++
		vectorTokens[token]++
	}
	for token := range vectorTokens {
		orderedTokens = append(orderedTokens, token)
	}
	// tf
	ptf := make(map[string]float64)
	dtf := make(map[string]float64)
	idf := make(map[string]float64)
	nDoc := nbModel.NDoc + 1
	for _, token := range orderedTokens {
		if _, ok := bow[token]; ok {
			ptf[token] = 1 + math.Log(bow[token])
		}
		if _, ok := vocabs[token]; ok {
			dtf[token] = 1 + math.Log(vocabs[token])
		}
		if _, ok := df[token]; ok {
			idf[token] = math.Log(nDoc / df[token])
		}
	}
	predictVec := mat.NewVecDense(len(vectorTokens), nil)
	docVec := mat.NewVecDense(len(vectorTokens), nil)
	for i, token := range orderedTokens {
		predictVec.SetVec(i, ptf[token]*idf[token])
		docVec.SetVec(i, dtf[token]*idf[token])
	}
	// Use TF term frequency reduce frequency of words
	// Scale by division of normalized vectors
	// predictVec.ScaleVec(1/mat.Norm(predictVec, 2), predictVec)
	// docVec.ScaleVec(1/mat.Norm(docVec, 2), docVec)
	// Print the result using the formatter.
	// fp := mat.Formatted(predictVec, mat.Prefix("    "), mat.Squeeze())
	// fmt.Printf("p = %v\n", fp)
	// fd := mat.Formatted(docVec, mat.Prefix("    "), mat.Squeeze())
	// fmt.Printf("d = %v\n", fd)

	cosine := mat.Dot(predictVec, docVec) / (mat.Norm(predictVec, 2) * mat.Norm(docVec, 2))
	// Cosine lie between 0 and 1
	return cosine, nil
}

func (nbModel *NaiveBayeModel) TrainNaiveBayes(scopes []string, cfg *config.Config) error {
	r := regexp.MustCompile(`(\((?P<text>[^\)]+)\)\[(?P<variable>[^\]]+)\])`)
	// Train naive bayes
	nbModel.Vocabs = make(map[string]float64)
	// Ndoc = number of documents
	nbModel.NDoc = 0.0
	// Nc = number of documents in class
	nbModel.Classes = make(map[string]float64)
	countwc := make(map[string]float64)
	nbModel.DF = make(map[string]float64)

	for _, scope := range scopes {
		viper.SetConfigName(scope)
		viper.AddConfigPath(cfg.TemplatePath.ChatPath)
		viper.SetConfigType("yaml")
		if err := viper.ReadInConfig(); err != nil {
			return err
		}
		var cb *model.Chatbot
		err := viper.Unmarshal(&cb)
		if err != nil {
			return err
		}

		// Counting
		for _, wf := range cb.Workflows {
			// namespace
			class := fmt.Sprintf("%s/%s", scope, wf.Topic)
			nbModel.Classes[class]++
			for _, kw := range wf.Keywords {
				nbModel.NDoc++
				nbModel.Classes[class]++
				processedText := r.ReplaceAllString(kw, "${text}")
				tokens := nbModel.Tokenizer(processedText)
				for _, token := range tokens {
					nbModel.Vocabs[token]++
					wtoclass := fmt.Sprintf("%s-%s", token, class)
					countwc[wtoclass]++
					nbModel.DF[token]++
				}
			}
		}
	}
	// TF-IDF
	tf := make(map[string]float64)
	idf := make(map[string]float64)
	for token := range nbModel.Vocabs {
		for class := range nbModel.Classes {
			wtoclass := fmt.Sprintf("%s-%s", token, class)
			if countwc[wtoclass] > 0 {
				tf[wtoclass] = 1 + math.Log(countwc[wtoclass])
			}
		}
		idf[token] = math.Log(nbModel.NDoc / nbModel.DF[token])
	}

	nbModel.Logprior = make(map[string]float64)
	nbModel.Loglikelihood = make(map[string]float64)
	// using Lapace smoothing
	alpha := 1.0
	lenVocabs := len(nbModel.Vocabs)
	for class := range nbModel.Classes {
		nbModel.Logprior[class] = math.Log(nbModel.Classes[class] / nbModel.NDoc)
		for token := range nbModel.Vocabs {
			wtoclass := fmt.Sprintf("%s-%s", token, class)

			totalWtoC := 0.0
			for t := range nbModel.Vocabs {
				wslashtoclass := fmt.Sprintf("%s-%s", t, class)
				totalWtoC += tf[wslashtoclass]*idf[token] + alpha*float64(lenVocabs)
			}
			nbModel.Loglikelihood[wtoclass] = math.Log((tf[wtoclass]*idf[token] + alpha) / totalWtoC)
		}
	}
	// Save logprior, loglikelihood, classes and vocabs
	modelPath := fmt.Sprintf("%s/naivebayes.model", cfg.StoragePath.Model)
	f, err := os.Create(modelPath)
	if err != nil {
		return err
	}
	defer f.Close()
	encoder := gob.NewEncoder(f)
	err = encoder.Encode(nbModel)
	if err != nil {
		return err
	}
	return nil
}

func (nbModel *NaiveBayeModel) Tokenizer(sentence string) []string {
	// Lower case
	sentence = strings.ToLower(sentence)
	// Strip punctuation
	punctuationReg := regexp.MustCompile(`[\@\$\/\#\.\-\:\&\*\+\=\[\]\?\!\(\)\{\}\,\''\"\>\_\<\;\%]+`)
	sentence = punctuationReg.ReplaceAllString(sentence, " ")
	// Handle number
	numberReg := regexp.MustCompile(`[0-9]+`)
	sentence = numberReg.ReplaceAllString(sentence, constants.GenericFeatures.NUMBER)
	// Handle URLs
	urlReg := regexp.MustCompile(`(http|https)://[^\s]*`)
	sentence = urlReg.ReplaceAllString(sentence, constants.GenericFeatures.HTTPADDR)
	// Handle email address
	emailReg := regexp.MustCompile(`[^\s]+@[^\s]+`)
	sentence = emailReg.ReplaceAllString(sentence, constants.GenericFeatures.EMAILADDR)
	// Handle $ sign
	dollarReg := regexp.MustCompile(`[$]+`)
	sentence = dollarReg.ReplaceAllString(sentence, constants.GenericFeatures.DOLLARSIGN)
	// Trim space
	spaceReg := regexp.MustCompile(`\s+`)
	sentence = spaceReg.ReplaceAllString(sentence, " ")
	sentence = strings.Trim(sentence, " ")

	sWords := strings.Split(sentence, " ")
	nGram := nbModel.dict.MaxGram
	var tokens []string
	lenSW := len(sWords)
	i := 0
	for i < lenSW {
		word := sWords[i]
		// max ngram
		maxGram := nGram
		if maxGram > (lenSW - i) {
			maxGram = lenSW - i
		}

		// Match longest ngram
		token := word
		indexMatch := i + 1
		for j := maxGram; j > 0; j-- {
			bow := sWords[i : i+j]
			dictW := strings.Join(bow, " ")
			// Check if word is personal pronouns
			_, okPerPronouns := nbModel.dict.PersonalFeatures[dictW]
			if okPerPronouns {
				token = constants.GenericFeatures.PERSONALPRONOUN
				indexMatch = i + j
				break
			}
			_, okWord := nbModel.dict.Words[dictW]
			if okWord {
				token = dictW
				indexMatch = i + j
				break
			} else if j == 1 {
				// Single word not in dictionary check stock features
				_, okStock := nbModel.dict.StockFeatures[dictW]
				if okStock {
					token = constants.GenericFeatures.STOCK
					indexMatch = i + j
					break
				}
			}
		}
		tokens = append(tokens, token)
		i = indexMatch // jump to next match
	}
	return tokens
}
