package chatbot

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"regexp"
	"strings"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/spf13/viper"
	"golang.org/x/net/context"
)

type MSM struct {
	cfg          *config.Config
	intentionReg *regexp.Regexp
	classifier   *LogisticRegressionModel
	d            *WordDict
	entities     map[string]bool
	ui           map[string]*model.UI
	intents      map[string]*model.Intent
	workflowData map[string]*model.Workflow
}

func NewMarkovStateMachine(cfg *config.Config) (contract.StateMachine, error) {
	regEntities := regexp.MustCompile(`(\((?P<text>[^)]+)\)\[(?P<variable>[^]]+)])`)
	reIntention := regexp.MustCompile(`(?m)(?m)\s*(?P<intention>.+?)\s*\((?P<params>[^)]+?)\)`)
	entities := make(map[string]bool)
	uiData := make(map[string]*model.UI)
	ittData := make(map[string]*model.Intent)
	wfData := make(map[string]*model.Workflow)
	// init scopes
	for _, scope := range cfg.ChatScopes {
		viper.SetConfigName(scope)
		viper.AddConfigPath(cfg.TemplatePath.ChatPath)
		viper.SetConfigType("yaml")
		if err := viper.ReadInConfig(); err != nil {
			return nil, err
		}
		var cb *model.Chatbot
		err := viper.Unmarshal(&cb)
		if err != nil {
			return nil, err
		}

		for _, wf := range cb.Workflows {
			state := fmt.Sprintf("%s/%s", scope, wf.Topic)
			wf.Namespace = scope
			wfData[state] = wf
			for _, kw := range wf.Keywords {
				if regEntities.MatchString(kw) {
					entities[state] = true
					break
				}
			}
		}
		for _, ui := range cb.UI {
			uiData[fmt.Sprintf("%s/%s", scope, ui.Input)] = ui
		}
		for _, itt := range cb.Intentions {
			ittData[itt.Intent] = itt
		}
	}
	// init classifier
	d, err := NewDict(cfg)
	if err != nil {
		return nil, err
	}

	lg, err := NewLGModel(cfg, d)
	if err != nil {
		return nil, err
	}

	chatbot := &MSM{
		cfg:          cfg,
		classifier:   lg,
		entities:     entities,
		d:            d,
		ui:           uiData,
		intents:      ittData,
		workflowData: wfData,
		intentionReg: reIntention,
	}

	return chatbot, nil
}

func (m *MSM) DetectWorkflowByChatObj(chatObj *model.ChatObject, message string) (string, error) {
	// Detect workflow

	// Threshold > 15%
	class, err := m.classifier.Predict(message, 0.15)
	if err != nil {
		return "", err
	}
	if class == "" {
		return "scheduler/default", nil
	}
	// Check permission
	granted := false
	for _, scope := range chatObj.Scopes {
		if strings.Contains(class, scope) {
			granted = true
			break
		}
	}
	if !granted {
		// Fallback to default
		return "scheduler/default", nil
	}
	return class, nil
}

func (m *MSM) DetectWorkflow(ctx context.Context, chatID string, message string) (string, error) {
	// Detect workflow
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	// Threshold > 15%
	class, err := m.classifier.Predict(message, 0.15)
	if err != nil {
		return "", err
	}
	if class == "" {
		return "scheduler/default", nil
	}
	// Check permission
	stmt, err := tx.Prepare("SELECT scopes FROM chatbot WHERE id = $1")
	if err != nil {
		return "", err
	}

	var (
		scopeNull sql.NullString
		scopes    []string
	)

	err = stmt.QueryRowContext(ctx, chatID).Scan(&scopeNull)
	if err != nil {
		return "", err
	}
	if scopeNull.Valid {
		err := json.Unmarshal([]byte(scopeNull.String), &scopes)
		if err != nil {
			return "", err
		}
	}
	granted := false
	for _, scope := range scopes {
		if strings.Contains(class, scope) {
			granted = true
			break
		}
	}
	if !granted {
		// Fallback to default
		return "scheduler/default", nil
	}
	return class, nil
}

func (m *MSM) LoadUserChatObject(ctx context.Context, chatID string) (*model.ChatObject, error) {
	chatObj := new(model.ChatObject)
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	stmt, err := tx.Prepare("SELECT id, state, username, first_name, last_name, owner, scopes FROM chatbot WHERE id = $1")
	if err != nil {
		return nil, err
	}
	var scopeNull sql.NullString

	err = stmt.QueryRowContext(ctx, chatID).Scan(&chatObj.ID, &chatObj.State, &chatObj.User, &chatObj.FirstName, &chatObj.LastName, &chatObj.Owner, &scopeNull)
	if err != nil {
		return nil, err
	}
	if scopeNull.Valid {
		err = json.Unmarshal([]byte(scopeNull.String), &chatObj.Scopes)
		if err != nil {
			return nil, err
		}
	} else {
		chatObj.Scopes = append(chatObj.Scopes, "scheduler")
	}
	return chatObj, nil
}

func (m *MSM) DetectUserContext(ctx context.Context, chatObj *model.ChatObject, message string) (*model.NanoJob, error) {
	// Step 1: Detect user context
	workflowStr, err := m.DetectWorkflowByChatObj(chatObj, message)
	if err != nil {
		return nil, err
	}

	// Step 2: Detect user entities and store in context
	chatCtx := make(map[string]interface{})
	if m.entities[workflowStr] {
		hmm, err := NewHMM(m.cfg, workflowStr, m.d)
		if err != nil {
			return nil, err
		}
		entities := hmm.Entities(message)
		for _, entity := range entities {
			for label, val := range entity {
				chatCtx[label] = val
			}
		}
	}
	workflow, ok := m.workflowData[workflowStr]
	if !ok {
		return nil, constants.CommonError.ERROR_NOT_FOUND
	}

	// If workflow is cancel inform and return immediately
	if workflowStr == "scheduler/cancel" {
		// Return job ask uncompleted param
		params := map[string]string{
			"chat_id":      chatObj.ID,
			"chat_channel": chatObj.Channel,
			"message":      "Không tìm thấy yêu cầu hủy",
		}
		paramData, err := json.Marshal(params)
		if err != nil {
			return nil, err
		}
		job := &model.NanoJob{
			Url:       fmt.Sprintf("http://localhost:%s/v1/channel/ask", m.cfg.HTTPPort),
			Params:    string(paramData),
			Method:    http.MethodPost,
			NameSpace: workflow.Namespace,
			Owner:     chatObj.ID,
		}
		return job, nil
	}
	intentMap := make(map[int]string, len(workflow.Intents))
	for i, intent := range workflow.Intents {
		intentMap[i] = intent
	}

	// Seed job
	mapData, err := json.Marshal(intentMap)
	if err != nil {
		return nil, err
	}
	chatCtx["chat_id"] = chatObj.ID
	chatCtx["chat_channel"] = chatObj.Channel

	contextData, err := json.Marshal(chatCtx)
	if err != nil {
		return nil, err
	}
	jobResp := &model.JobResponse{
		RequestContext: string(contextData),
		Map:            string(mapData),
		Step:           0,
		NameSpace:      workflow.Namespace,
		Owner:          chatObj.ID,
	}
	return m.BuildNextJob(ctx, jobResp)
}

func (m *MSM) UserInput(ctx context.Context, chatObj *model.ChatObject, inputVal string) (*model.NanoJob, error) {
	state, err := m.GetState(ctx, chatObj.ID)
	if err != nil {
		return nil, err
	}

	var reqCtx map[string]interface{}
	err = json.Unmarshal([]byte(state["request_ctx"].(string)), &reqCtx)
	if err != nil {
		return nil, err
	}
	reqCtx[state["field_input"].(string)] = inputVal

	newReqCtx, err := json.Marshal(reqCtx)
	if err != nil {
		return nil, err
	}

	err = m.ClearState(ctx, chatObj.ID)
	if err != nil {
		return nil, err
	}
	jobResp := &model.JobResponse{
		RequestContext: string(newReqCtx),
		Map:            state["intentions"].(string),
		Step:           int(state["step"].(float64)),
		NameSpace:      state["name_space"].(string),
		Owner:          chatObj.ID,
	}
	job, err := m.BuildNextJob(ctx, jobResp)
	if err != nil {
		return nil, err
	}

	return job, nil
}

func (m *MSM) BuildNextJob(ctx context.Context, jobResp *model.JobResponse) (*model.NanoJob, error) {
	var intentMap map[int]string
	err := json.Unmarshal([]byte(jobResp.Map), &intentMap)
	if err != nil {
		return nil, err
	}
	if len(intentMap) == 0 {
		return nil, constants.CommonError.ERROR_NOT_FOUND
	}

	intentStr, ok := intentMap[jobResp.Step]
	if !ok {
		return nil, constants.CommonError.ERROR_NOT_FOUND
	}

	// Parse params of intent
	intention, intentParams, err := m.parseIntention(intentStr)
	if err != nil {
		return nil, err
	}

	paramBuilder := make(map[string]interface{})
	reqParams := make(map[string]interface{})
	if intention.Context.Params != nil {
		for pk, pv := range *intention.Context.Params {
			paramBuilder[pk] = pv
		}
	}

	// Replace params from the context
	var reqCtx map[string]interface{}
	err = json.Unmarshal([]byte(jobResp.RequestContext), &reqCtx)

	// Add param of intention as a context
	for k, v := range intentParams {
		reqCtx[k] = v
	}
	// Replace context if needed
	if jobResp.Step > 0 {
		prevIntentStr, ok := intentMap[jobResp.Step-1]
		if !ok {
			return nil, constants.CommonError.ERROR_NOT_FOUND
		}

		// Parse params of intent
		prevIntention, _, err := m.parseIntention(prevIntentStr)
		if err != nil {
			return nil, err
		}
		if prevIntention.Context.Response != nil {
			for k, v := range reqCtx {
				key := k
				for sKey, sOrig := range *prevIntention.Context.Response {
					if sOrig == k {
						key = sKey
						break
					}
				}
				reqCtx[key] = v
			}
		}
	}

	// Build request param for next job
	for pk, pv := range paramBuilder {
		val, ok := reqCtx[fmt.Sprint(pv)]
		// Save uncompleted job to user state
		if !ok {
			err = m.SaveState(ctx, jobResp.Owner, map[string]interface{}{
				"step":        jobResp.Step,
				"intentions":  jobResp.Map,
				"request_ctx": jobResp.RequestContext,
				"field_input": pv,
				"name_space":  jobResp.NameSpace,
			})
			if err != nil {
				return nil, err
			}
			// Return job ask uncompleted param
			params := map[string]string{
				"chat_id": jobResp.Owner,
				"message": fmt.Sprintf("%s %s", m.TranslateInput(jobResp.NameSpace, "demand"), m.TranslateInput(jobResp.NameSpace, fmt.Sprint(pv))),
			}
			paramData, err := json.Marshal(params)
			if err != nil {
				return nil, err
			}
			job := &model.NanoJob{
				Url:       fmt.Sprintf("http://localhost:%s/v1/channel/ask", m.cfg.HTTPPort),
				Params:    string(paramData),
				Method:    http.MethodPost,
				NameSpace: jobResp.NameSpace,
				Owner:     jobResp.Owner,
			}
			return job, nil
		}
		reqParams[pk] = val
	}

	// Check fulfillment params
	if intention.Context.ParamFixes != nil {
		for pk, pv := range *intention.Context.ParamFixes {
			reqParams[pk] = pv
		}
	}

	reqParamsByte, err := json.Marshal(reqParams)
	if err != nil {
		return nil, err
	}

	newCtx, err := json.Marshal(reqCtx)
	if err != nil {
		return nil, err
	}

	job := &model.NanoJob{
		Url:            fmt.Sprintf("http://localhost:%s%s", m.cfg.HTTPPort, intention.Context.URL),
		Params:         string(reqParamsByte),
		Method:         intention.Context.Method,
		RequestContext: string(newCtx),
		Step:           jobResp.Step,
		Map:            jobResp.Map,
		Owner:          jobResp.Owner,
	}

	return job, nil
}

func (m *MSM) parseIntention(intentStr string) (*model.Intent, map[string]interface{}, error) {
	match := m.intentionReg.FindStringSubmatch(intentStr)
	matchedResults := make(map[string]string)
	if match != nil {
		for i, name := range m.intentionReg.SubexpNames() {
			if i != 0 && name != "" {
				matchedResults[name] = match[i]
			}
		}
	} else {
		return m.intents[intentStr], make(map[string]interface{}), nil
	}
	// Decode params
	params := make(map[string]interface{})
	err := json.Unmarshal([]byte(matchedResults["params"]), &params)
	if err != nil {
		return nil, nil, err
	}
	return m.intents[matchedResults["intention"]], params, nil
}

func (m *MSM) TranslateInput(namespace string, input string) string {
	ui, ok := m.ui[fmt.Sprintf("%s/%s", namespace, input)]
	if !ok {
		return input
	}
	return ui.Output[rand.Intn(len(ui.Output))]
}

func (m *MSM) CancelUserState(ctx context.Context, chatID string) (*model.NanoJob, error) {
	state, err := m.GetState(ctx, chatID)
	if err != nil {
		return nil, err
	}

	// Get state workflow
	intentionMapStr := state["intentions"]
	var intentionMap map[int]string
	err = json.Unmarshal([]byte(intentionMapStr.(string)), &intentionMap)
	if err != nil {
		return nil, err
	}
	step := int(state["step"].(float64))
	matchedResults := make(map[string]string)
	if intentionStr, ok := intentionMap[step]; ok {
		match := m.intentionReg.FindStringSubmatch(intentionStr)
		if match != nil {
			for i, name := range m.intentionReg.SubexpNames() {
				if i != 0 && name != "" {
					matchedResults[name] = match[i]
				}
			}
		} else {
			matchedResults["intention"] = intentionStr
			matchedResults["param"] = ""
			matchedResults["value"] = ""
		}
	}
	// Inform user the workflow has cancelled
	nameSpace := state["name_space"].(string)
	workflow, ok := m.workflowData[fmt.Sprintf("%s/%s", nameSpace, matchedResults["intention"])]
	if !ok {
		return nil, constants.CommonError.ERROR_NOT_FOUND
	}
	// Return job ask uncompleted param
	params := map[string]string{
		"chat_id": chatID,
		"message": fmt.Sprintf("Đã hủy yêu cầu %s rồi nhé", workflow.Description),
	}
	paramData, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}
	err = m.ClearState(ctx, chatID)
	if err != nil {
		return nil, err
	}
	job := &model.NanoJob{
		Url:       fmt.Sprintf("http://localhost:%s/v1/channel/ask", m.cfg.HTTPPort),
		Params:    string(paramData),
		Method:    http.MethodPost,
		NameSpace: workflow.Namespace,
	}
	return job, nil
}

func (m *MSM) GetState(ctx context.Context, chatID string) (map[string]interface{}, error) {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	stmt, err := tx.Prepare("SELECT state FROM chatbot WHERE id = $1")
	if err != nil {
		return nil, err
	}
	var (
		stateNull sql.NullString
		state     map[string]interface{}
	)

	err = stmt.QueryRowContext(ctx, chatID).Scan(&stateNull)
	if err != nil {
		return nil, err
	}
	if stateNull.Valid {
		err := json.Unmarshal([]byte(stateNull.String), &state)
		if err != nil {
			return nil, err
		}
	} else {
		state = make(map[string]interface{})
	}
	return state, nil
}

func (m *MSM) ClearState(ctx context.Context, chatID string) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	stmt, err := tx.Prepare("UPDATE chatbot SET state = NULL WHERE id = $1")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, chatID)
	if err != nil {
		return err
	}
	return nil
}

func (m *MSM) SaveState(ctx context.Context, chatID string, state map[string]interface{}) error {
	stateData, err := json.Marshal(state)
	if err != nil {
		return err
	}
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	stmt, err := tx.Prepare("UPDATE chatbot SET state = $1 WHERE id = $2")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, stateData, chatID)
	if err != nil {
		return err
	}
	return nil
}
