package sentiment

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/stretchr/testify/assert"
)

func setupTestFiles(t *testing.T) (string, func()) {
	// Create a temporary directory for test files
	tempDir, err := os.MkdirTemp("", "sentiment_test")
	assert.NoError(t, err)

	// Create sentiment directory
	sentimentDir := filepath.Join(tempDir, "sentiment")
	err = os.MkdirAll(sentimentDir, 0755)
	assert.NoError(t, err)

	// Create test files with sample content
	files := map[string][]string{
		"neutral.txt": {
			"neutral1",
			"neutral2@#$%", // with special characters
			"neutral3",
		},
		"positive.txt": {
			"positive1",
			"positive2!@#", // with special characters
			"positive3",
		},
		"negative.txt": {
			"negative1",
			"negative2$%^", // with special characters
			"negative3",
		},
	}

	for filename, content := range files {
		filePath := filepath.Join(sentimentDir, filename)
		f, err := os.Create(filePath)
		assert.NoError(t, err)

		for _, line := range content {
			_, err = f.WriteString(line + "\n")
			assert.NoError(t, err)
		}
		f.Close()
	}

	// Return cleanup function
	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	return tempDir, cleanup
}

func TestNewSentimentDict(t *testing.T) {
	// Setup test files
	tempDir, cleanup := setupTestFiles(t)
	defer cleanup()

	tests := []struct {
		name          string
		setupConfig   func() *config.Config
		expectedError bool
		validate      func(*testing.T, *SentimentDict)
	}{
		{
			name: "Valid files and paths",
			setupConfig: func() *config.Config {
				return &config.Config{
					TemplatePath: &config.Template{
						PretrainPath: tempDir,
					},
				}
			},
			expectedError: false,
			validate: func(t *testing.T, dict *SentimentDict) {
				assert.NotNil(t, dict)
				// Check neurals
				assert.Equal(t, 3, len(dict.Neurals))
				assert.Contains(t, dict.Neurals, "neutral1")
				assert.Contains(t, dict.Neurals, "neutral2")
				assert.Contains(t, dict.Neurals, "neutral3")

				// Check actives (positives)
				assert.Equal(t, 3, len(dict.Actives))
				assert.Contains(t, dict.Actives, "positive1")
				assert.Contains(t, dict.Actives, "positive2")
				assert.Contains(t, dict.Actives, "positive3")

				// Check passives (negatives)
				assert.Equal(t, 3, len(dict.Passives))
				assert.Contains(t, dict.Passives, "negative1")
				assert.Contains(t, dict.Passives, "negative2")
				assert.Contains(t, dict.Passives, "negative3")
			},
		},
		{
			name: "Invalid path",
			setupConfig: func() *config.Config {
				return &config.Config{
					TemplatePath: &config.Template{
						PretrainPath: "/nonexistent/path",
					},
				}
			},
			expectedError: true,
			validate: func(t *testing.T, dict *SentimentDict) {
				assert.Nil(t, dict)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := tt.setupConfig()
			dict, err := NewSentimentDict(cfg)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			tt.validate(t, dict)
		})
	}
}

func TestSentimentDictWithEmptyFiles(t *testing.T) {
	// Setup test directory
	tempDir, err := os.MkdirTemp("", "sentiment_empty_test")
	assert.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create sentiment directory
	sentimentDir := filepath.Join(tempDir, "sentiment")
	err = os.MkdirAll(sentimentDir, 0755)
	assert.NoError(t, err)

	// Create empty files
	files := []string{"neutral.txt", "positive.txt", "negative.txt"}
	for _, filename := range files {
		filePath := filepath.Join(sentimentDir, filename)
		f, err := os.Create(filePath)
		assert.NoError(t, err)
		f.Close()
	}

	cfg := &config.Config{
		TemplatePath: &config.Template{
			PretrainPath: tempDir,
		},
	}

	dict, err := NewSentimentDict(cfg)
	assert.NoError(t, err)
	assert.NotNil(t, dict)
	assert.Empty(t, dict.Neurals)
	assert.Empty(t, dict.Actives)
	assert.Empty(t, dict.Passives)
}
