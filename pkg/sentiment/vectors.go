package sentiment

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"regexp"

	"github.com/007lock/simon-homestead/pkg/config"
)

type SentimentDict struct {
	Neurals  []string
	Actives  []string
	Passives []string
}

func NewSentimentDict(cfg *config.Config) (*SentimentDict, error) {
	reg, err := regexp.Compile(`[^\p{L}\d\s]+`)
	if err != nil {
		return nil, err
	}

	// Read neural
	fn, err := os.Open(fmt.Sprintf("%s/sentiment/neutral.txt", cfg.TemplatePath.PretrainPath))
	if err != nil {
		return nil, err
	}
	defer fn.Close()

	var neurals []string

	bn := bufio.NewReader(fn)
	for {
		a, _, c := bn.ReadLine()
		if c == io.EOF {
			break
		}
		words := string(a)
		// Processes special char
		words = reg.ReplaceAllString(words, "")
		neurals = append(neurals, words)
	}

	// Read active
	fa, err := os.Open(fmt.Sprintf("%s/sentiment/positive.txt", cfg.TemplatePath.PretrainPath))
	if err != nil {
		return nil, err
	}
	defer fa.Close()

	var actives []string

	ba := bufio.NewReader(fa)
	for {
		a, _, c := ba.ReadLine()
		if c == io.EOF {
			break
		}
		words := string(a)
		// Processes special char
		words = reg.ReplaceAllString(words, "")
		actives = append(actives, words)
	}

	// Read passive
	fp, err := os.Open(fmt.Sprintf("%s/sentiment/negative.txt", cfg.TemplatePath.PretrainPath))
	if err != nil {
		return nil, err
	}
	defer fa.Close()

	var passives []string

	bp := bufio.NewReader(fp)
	for {
		a, _, c := bp.ReadLine()
		if c == io.EOF {
			break
		}
		words := string(a)
		// Processes special char
		words = reg.ReplaceAllString(words, "")
		passives = append(passives, words)
	}

	return &SentimentDict{
		Neurals:  neurals,
		Actives:  actives,
		Passives: passives,
	}, nil
}
