package facebook

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"io"
	"net/http"

	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
)

type facebook struct {
	cfg       *config.Config
	logger    *zap.Logger
	transport *http.Transport
}

func NewFacebook(cfg *config.Config, logger *zap.Logger, trans *http.Transport) contract.Facebook {
	return &facebook{cfg, logger, trans}
}

// SendReqCustomerInfo sends a request customer_information back to the user via the Facebook Messenger API
func (fb *facebook) SendReqCustomerInfo(ctx context.Context, message *model.FaceBookSendMessageReqBody) error {
	url := fmt.Sprintf("%s/me/messages", fb.cfg.Facebook.GraphURL)

	payload := map[string]interface{}{
		"recipient": map[string]string{
			"id": message.RecipientID,
		},
		"message": map[string]interface{}{
			"attachment": map[string]interface{}{
				"type": "template",
				"payload": map[string]interface{}{
					"template_type": "customer_information",
					"countries": []string{
						"VN",
					},
					"business_privacy": map[string]interface{}{
						"url": fb.cfg.FrontendURL,
					},
					"expires_in_days": 7,
				},
			},
		},
	}

	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		fb.logger.Error("Failed to marshal Facebook response", zap.Error(err))
		return err
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
	if err != nil {
		fb.logger.Error("Failed to create Facebook API request", zap.Error(err))
		return err
	}
	req = req.WithContext(ctx)
	req.Header.Set("Content-Type", "application/json")

	// Add a page access token
	q := req.URL.Query()
	q.Add("access_token", fb.cfg.Facebook.PageAccessToken)
	req.URL.RawQuery = q.Encode()

	client := &http.Client{
		Transport: fb.transport,
	}
	resp, err := client.Do(req)
	if err != nil {
		fb.logger.Error("Failed to send message to Facebook", zap.Error(err))
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		fb.logger.Error("Facebook API error",
			zap.Int("status", resp.StatusCode),
			zap.String("response", string(body)))
		return fmt.Errorf("facebook API error: %s", string(body))
	}

	return nil
}

// SendMessage sends a message back to the user via the Facebook Messenger API
func (fb *facebook) SendMessage(ctx context.Context, message *model.FaceBookSendMessageReqBody) error {
	url := fmt.Sprintf("%s/me/messages", fb.cfg.Facebook.GraphURL)

	payload := map[string]interface{}{
		"recipient": map[string]string{
			"id": message.RecipientID,
		},
		"message": map[string]string{
			"text": message.Message,
		},
	}

	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		fb.logger.Error("Failed to marshal Facebook response", zap.Error(err))
		return err
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
	if err != nil {
		fb.logger.Error("Failed to create Facebook API request", zap.Error(err))
		return err
	}
	req = req.WithContext(ctx)
	req.Header.Set("Content-Type", "application/json")

	// Add a page access token
	q := req.URL.Query()
	q.Add("access_token", fb.cfg.Facebook.PageAccessToken)
	req.URL.RawQuery = q.Encode()

	client := &http.Client{
		Transport: fb.transport,
	}
	resp, err := client.Do(req)
	if err != nil {
		fb.logger.Error("Failed to send message to Facebook", zap.Error(err))
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		fb.logger.Error("Facebook API error",
			zap.Int("status", resp.StatusCode),
			zap.String("response", string(body)))
		return fmt.Errorf("facebook API error: %s", string(body))
	}

	return nil
}

// GetUserInfo retrieves basic user information from Facebook Graph API
func (fb *facebook) GetUserInfo(ctx context.Context, userID string) (*model.FaceBookUserInfo, error) {
	// Construct the URL for the Facebook Graph API
	url := fmt.Sprintf("%s/%s", fb.cfg.Facebook.GraphURL, userID)

	// Create a new request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	// Add page access token and fields to request
	q := req.URL.Query()
	q.Add("access_token", fb.cfg.Facebook.PageAccessToken)
	q.Add("fields", "name,first_name,last_name") // Add more fields if needed
	req.URL.RawQuery = q.Encode()

	// Send the request
	client := &http.Client{
		Transport: fb.transport,
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Check response
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to get user info: %s", string(body))
	}

	// Parse the response
	var userInfo model.FaceBookUserInfo
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, err
	}

	return &userInfo, nil
}
