package telegram

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
)

type telegram struct {
	cfg       *config.Config
	transport *http.Transport
}

func NewTelegram(cfg *config.Config, trans *http.Transport) contract.Telegram {
	return &telegram{cfg, trans}
}

func (t *telegram) SendChatAction(ctx context.Context, message *model.SendChatActionReqBody) error {
	// Create the JSON body from the struct
	reqBytes, err := json.Marshal(message)
	if err != nil {
		return err
	}

	client := &http.Client{
		Timeout:   30 * time.Second,
		Transport: t.transport,
	}
	// Send a post request with your token
	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("https://api.telegram.org/%s/sendChatAction", t.cfg.Telegram.Token), bytes.NewBuffer(reqBytes))
	if err != nil {
		return err
	}
	req = req.WithContext(ctx)
	req.Header.Set("User-Agent", "Homestead client")
	req.Header.Set("Content-Type", "application/json")
	res, err := client.Do(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		content, err := io.ReadAll(res.Body)
		if err != nil {
			return err
		}

		s := strings.TrimSpace(string(content))
		fmt.Println("=============Response===============")
		fmt.Println(s)
		return fmt.Errorf("unexpected status %s", res.Status)
	}

	return nil
}

func (t *telegram) SetMessageReaction(ctx context.Context, message *model.SendMessageReactionReqBody) error {
	// Create the JSON body from the struct
	reqBytes, err := json.Marshal(message)
	if err != nil {
		return err
	}

	client := &http.Client{
		Timeout:   30 * time.Second,
		Transport: t.transport,
	}
	// Send a post request with your token
	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("https://api.telegram.org/%s/setMessageReaction", t.cfg.Telegram.Token), bytes.NewBuffer(reqBytes))
	if err != nil {
		return err
	}
	req = req.WithContext(ctx)
	req.Header.Set("User-Agent", "Homestead client")
	req.Header.Set("Content-Type", "application/json")
	res, err := client.Do(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		content, err := io.ReadAll(res.Body)
		if err != nil {
			return err
		}

		s := strings.TrimSpace(string(content))
		fmt.Println("=============Response===============")
		fmt.Println(s)
		return fmt.Errorf("unexpected status %s", res.Status)
	}

	return nil
}

func (t *telegram) SendMessage(ctx context.Context, message *model.SendMessageReqBody) error {
	// Create the JSON body from the struct
	reqBytes, err := json.Marshal(message)
	if err != nil {
		return err
	}

	client := &http.Client{
		Timeout:   30 * time.Second,
		Transport: t.transport,
	}
	// Send a post request with your token
	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("https://api.telegram.org/%s/sendMessage", t.cfg.Telegram.Token), bytes.NewBuffer(reqBytes))
	if err != nil {
		return err
	}
	req = req.WithContext(ctx)
	req.Header.Set("User-Agent", "Homestead client")
	req.Header.Set("Content-Type", "application/json")
	res, err := client.Do(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		content, err := io.ReadAll(res.Body)
		if err != nil {
			return err
		}

		s := strings.TrimSpace(string(content))
		fmt.Println("=============Response===============")
		fmt.Println(s)
		return fmt.Errorf("unexpected status %s", res.Status)
	}

	return nil
}

func (t *telegram) NumberFormat(N interface{}, lang ...string) string {

	decimal := false
	switch N.(type) {
	case int:
	case int16:
	case int32:
	case int64:
	case int8:
		break
	case float32:
	case float64:
		decimal = true

	default:
		return ""

	}
	n := fmt.Sprintf("%v", N)
	if decimal {
		n = fmt.Sprintf("%.2f", N)
	}

	n = strings.ReplaceAll(n, ",", "")
	dec := ""

	if strings.Contains(n, `.`) {
		dec = n[strings.Index(n, `.`)+1:]
		n = n[0:strings.Index(n, `.`)]

	}

	for i := 0; i <= len(n); i = i + 4 {

		a := n[0 : len(n)-i]
		b := n[len(n)-i:]
		if a != "-" {
			n = a + "," + b
		} else {
			n = a + b
		}

	}

	if n[0:1] == "," {
		n = n[1:]
	}

	if n[len(n)-1:] == "," {
		n = n[0 : len(n)-1]
	}

	if dec != "" {
		n = n + `.` + dec
	}

	return n

}
func (t *telegram) StarFormat(stars int) string {
	var starsStr []string
	for i := 0; i < stars; i++ {
		starsStr = append(starsStr, "⭐")
	}
	return strings.Join(starsStr, " ")
}

func (t *telegram) EscapeMarkdown(originalString string) string {
	// Detect link token
	linkTokenReg := regexp.MustCompile(`\[(?P<text>[^]]+).*?\((?P<link>[^)]+)\)`)
	matches := linkTokenReg.FindAllString(originalString, -1)
	processedStr := originalString
	for i, match := range matches {
		processedStr = strings.ReplaceAll(processedStr, match, fmt.Sprintf("$%d", i))
	}
	specialChar := "_[]()~>#+-=|{}.!`"
	var regexBuilder []string
	for _, v := range specialChar {
		regexBuilder = append(regexBuilder, regexp.QuoteMeta(string(v)))
	}

	myRegex := "(" + strings.Join(regexBuilder, "|") + ")"
	var re = regexp.MustCompile(myRegex)
	s := re.ReplaceAllString(processedStr, "\\$1")
	// Reverse match link
	for i, match := range matches {
		s = strings.ReplaceAll(s, fmt.Sprintf("$%d", i), match)
	}
	return s
}
