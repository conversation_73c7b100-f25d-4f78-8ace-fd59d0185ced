package model

type CafefStockInfo struct {
	Name    string `json:"Name"`
	Link    string `json:"Link"`
	Price   string `json:"Price"`
	Change  string `json:"Change"`
	Chart   string `json:"Chart"`
	Time    string `json:"Time"`
	Vdl     string `json:"VDL"`
	Vh      string `json:"VH"`
	Eps     string `json:"EPS"`
	Pe      string `json:"PE"`
	Kltb    string `json:"KLTB"`
	Related []struct {
		Symbol string  `json:"Symbol"`
		Name   string  `json:"Name"`
		Link   string  `json:"Link"`
		Price  float64 `json:"Price"`
	} `json:"Related"`
}

type CafefBankInterest struct {
	Data []struct {
		ID            string `json:"id"`
		Name          string `json:"name"`
		Symbol        string `json:"symbol"`
		Icon          string `json:"icon"`
		InterestRates []struct {
			Deposit int     `json:"deposit"`
			Value   float64 `json:"value"`
		} `json:"interestRates"`
	} `json:"Data"`
	Message interface{} `json:"Message"`
	Success bool        `json:"Success"`
}
