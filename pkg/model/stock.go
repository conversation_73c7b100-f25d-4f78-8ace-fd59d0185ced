package model

import "database/sql"

type Stock struct {
	ID                   string         `json:"id"`
	YearFields           sql.NullString `json:"years"`
	Price                float64        `json:"price"`
	MarketCap            float64        `json:"market_cap"`
	PE                   float64        `json:"pe"`
	ROE                  float64        `json:"roe"`
	EPS                  float64        `json:"eps"`
	BVPS                 float64        `json:"bvps"`
	Dividend             float64        `json:"dividend"`
	GrossProfitMargin    float64        `json:"gross_profit_margin"`
	InterestExpenseRatio float64        `json:"interest_expense_ratio"`
	AdminExpenseRatio    float64        `json:"admin_expense_ratio"`
	CashRatio            float64        `json:"cash_ratio"`
	DebtCoverRatio       float64        `json:"debt_cover_ratio"`
	GrahamValue          float64        `json:"graham_value"`
	Grade                int            `json:"grade"`
	RevComparedYOY       float64        `json:"rev_compared_yoy"`
	NetSmooth            bool           `json:"net_smooth"`
	DataSource           string         `json:"data_source"`
}
