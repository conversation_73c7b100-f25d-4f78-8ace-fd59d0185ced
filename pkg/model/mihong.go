package model

type GoldResponse struct {
	Success bool `json:"success"`
	Data    []struct {
		BuyingPrice       float64 `json:"buyingPrice"`
		SellingPrice      float64 `json:"sellingPrice"`
		Code              string  `json:"code"`
		SellChange        float64 `json:"sellChange"`
		SellChangePercent float64 `json:"sellChangePercent"`
		BuyChange         float64 `json:"buyChange"`
		BuyChangePercent  float64 `json:"buyChangePercent"`
		DateTime          string  `json:"dateTime"`
	} `json:"data"`
}
