package model

import "time"

type FialdaPrices struct {
	Now    time.Time `json:"now"`
	Result []struct {
		DateTime string  `json:"dateTime"`
		Value    float64 `json:"value"`
	} `json:"result"`
	TargetURL           interface{} `json:"targetUrl"`
	Success             bool        `json:"success"`
	Error               interface{} `json:"error"`
	UnAuthorizedRequest bool        `json:"unAuthorizedRequest"`
	Abp                 bool        `json:"__abp"`
}

type FialdaPricePagination struct {
	Now    time.Time `json:"now"`
	Result struct {
		TotalCount int `json:"totalCount"`
		Items      []struct {
			TradingTime     string      `json:"tradingTime"`
			Change          float64     `json:"change"`
			ChangePercent   float64     `json:"changePercent"`
			Open            float64     `json:"open"`
			High            float64     `json:"high"`
			Low             float64     `json:"low"`
			Close           float64     `json:"close"`
			AvgPrice        float64     `json:"avgPrice"`
			AdjClose        float64     `json:"adjClose"`
			TotalVolume     float64     `json:"totalVolume"`
			PtVol           interface{} `json:"ptVol"`
			TotalDealVolume float64     `json:"totalDealVolume"`
			RS52W           int         `json:"rS52W"`
			RS1M            int         `json:"rS1M"`
			RS3M            int         `json:"rS3M"`
			RS6M            int         `json:"rS6M"`
		} `json:"items"`
	} `json:"result"`
	TargetURL           interface{} `json:"targetUrl"`
	Success             bool        `json:"success"`
	Error               interface{} `json:"error"`
	UnAuthorizedRequest bool        `json:"unAuthorizedRequest"`
	Abp                 bool        `json:"__abp"`
}
