package model

type NanoJob struct {
	Url            string `json:"url"`
	Params         string `json:"params"`
	Method         string `json:"method"`
	RequestContext string `json:"request_context"`
	Map            string `json:"map"`
	Step           int    `json:"step"`
	NameSpace      string `json:"namespace"`
	Owner          string `json:"owner"`
}

type JobResponse struct {
	RequestContext string `json:"request_context"`
	Map            string `json:"map"`
	Step           int    `json:"step"`
	NameSpace      string `json:"name_space"`
	Owner          string `json:"owner"`
}
