package model

import (
	"time"
)

type PortfolioWeighted struct {
	Code      string  `json:"code" required:"true"`
	Weight    float64 `json:"weight" required:"true"`
	Quantity  float64 `json:"quantity" required:"true"`
	BookValue float64 `json:"book_value" required:"true"`
	Taxed     float64 `json:"taxed" required:"true"`
	BookPrice float64 `json:"book_price" required:"true"`
	BoughtAt  string  `json:"bought_at" required:"true"`
	Dividend  float64 `json:"dividend"`
}

type PortfolioProfitNLoss struct {
	Code     string    `json:"code"`
	Profit   float64   `json:"profit"`
	Taxed    float64   `json:"taxed"`
	Recorded time.Time `json:"recorded"`
	Source   string    `json:"source"`
}

type PorfolioAccumulateStock struct {
	Quantity       float64
	BookValue      float64
	Taxed          float64
	LatestBoughtAt time.Time
}

type PortfolioFairAsset struct {
	Code                string  `json:"code"`
	Weight              string  `json:"weight"`
	Quantity            string  `json:"quantity"`
	Velocity            string  `json:"velocity"`
	BookValue           string  `json:"book_value"`
	BookPriceNumber     float64 `json:"-"`
	BookPrice           string  `json:"book_price"`
	OpenPrice           string  `json:"open_price"`
	OpenPriceTax        string  `json:"open_price_tax"`
	HighestPrice        string  `json:"highest_price"`
	LowestPrice         string  `json:"lowest_price"`
	FairPrice           string  `json:"fair_price"`
	FairPriceTax        string  `json:"fair_price_tax"`
	PeakPrice           string  `json:"peak_price"`
	TaxedAtPeak         string  `json:"taxed_at_peak_price"`
	PeakPriceNumber     float64 `json:"-"`
	BottomPrice         string  `json:"bottom_price"`
	BottomPriceNumber   float64 `json:"-"`
	MeanPriceVolatility string  `json:"price_volatility"`
	PriceRisk           string  `json:"price_risk"`
	IsGain              bool    `json:"is_gain"`
	Profit              string  `json:"profit"`
	Dividend            string  `json:"dividend"`
	PEx                 string  `json:"pex"`
}
