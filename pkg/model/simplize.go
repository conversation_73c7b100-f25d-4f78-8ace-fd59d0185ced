package model

type SimpStockPrices struct {
	Status  int         `json:"status"`
	Message string      `json:"message"`
	Data    [][]float64 `json:"data"`
}

type SimpQuarterCompare struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	Data    struct {
		IndustryGroup string `json:"industryGroup"`
		Items         []struct {
			PeriodDate                string  `json:"periodDate"`
			PeriodDateName            string  `json:"periodDateName"`
			Revenue                   float64 `json:"revenue"`
			GrossRevenue              float64 `json:"grossRevenue"`
			NetIncomeParent           float64 `json:"netIncomeParent"`
			EbitGrowth                float64 `json:"ebitGrowth"`
			EbitdaGrowth              float64 `json:"ebitdaGrowth"`
			TotalAssets               float64 `json:"totalAssets"`
			TotalEquity               float64 `json:"totalEquity"`
			GrossMargin               float64 `json:"grossMargin"`
			EbitMargin                float64 `json:"ebitMargin"`
			EbitdaMargin              float64 `json:"ebitdaMargin"`
			NetInterestMargin         float64 `json:"netInterestMargin"`
			AssetsReturn              float64 `json:"assetsReturn"`
			CostIncomeRatio           float64 `json:"costIncomeRatio"`
			InsuranceNetMargin        float64 `json:"insuranceNetMargin"`
			NetMargin                 float64 `json:"netMargin"`
			ReturnOnEquity            float64 `json:"returnOnEquity"`
			ReturnOnAssets            float64 `json:"returnOnAssets"`
			AssetTurnover             float64 `json:"assetTurnover"`
			CccRatio                  float64 `json:"cccRatio"`
			DsoRatio                  float64 `json:"dsoRatio"`
			DioRatio                  float64 `json:"dioRatio"`
			DpoRatio                  float64 `json:"dpoRatio"`
			AssetsToEquity            float64 `json:"assetsToEquity"`
			GrossRevenueToEquity      float64 `json:"grossRevenueToEquity"`
			NetInsurancePremiumEquity float64 `json:"netInsurancePremiumEquity"`
			InvestmentYield           float64 `json:"investmentYield"`
			ReinsuranceRatio          float64 `json:"reinsuranceRatio"`
			ReceivableToEquity        float64 `json:"receivableToEquity"`
			BaddebtProvisionEquity    float64 `json:"baddebtProvisionEquity"`
			LiabilitiesToEquity       float64 `json:"liabilitiesToEquity"`
			NetDebtEquityRatio        float64 `json:"netDebtEquityRatio"`
			CurrentRatio              float64 `json:"currentRatio"`
			CashRatio                 float64 `json:"cashRatio"`
			InterestCoverageRatio     float64 `json:"interestCoverageRatio"`
			CasaRatio                 float64 `json:"casaRatio,omitempty"`
			NonPerformingLoanRatio    float64 `json:"nonPerformingLoanRatio,omitempty"`
			ProvisionBadDebtsRatio    float64 `json:"provisionBadDebtsRatio,omitempty"`
		} `json:"items"`
	} `json:"data"`
}

type SimpLatestQuarterRevRatio struct {
	QuarterName string  `json:"quarter_name"`
	Revenue     float64 `json:"revenue_qoq"`
}
