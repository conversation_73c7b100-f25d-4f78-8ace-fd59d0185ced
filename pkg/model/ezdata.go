package model

type EzStockList struct {
	D struct {
		Total int `json:"Total"`
		Rows  []struct {
			Type         string  `json:"__type"`
			CpnyID       int     `json:"CpnyID"`
			StockCode    string  `json:"stock_code"`
			MinistryName string  `json:"MinistryName"`
			NameShort    string  `json:"name_short"`
			NameVn       string  `json:"name_vn"`
			PostTo       string  `json:"post_to"`
			PostDate     string  `json:"post_date"`
			Capital      int64   `json:"capital"`
			MktCAP       int64   `json:"MktCAP"`
			LatestPrice  int     `json:"LatestPrice"`
			AvgQty3      int     `json:"AvgQty3"`
			ChangePrice  int     `json:"ChangePrice"`
			Eps          float64 `json:"EPS"`
			Pe           float64 `json:"PE"`
			DoanhThu     int64   `json:"DoanhThu"`
			LoiNhuan     int64   `json:"LoiNhuan"`
			SMktCAP      string  `json:"sMktCAP"`
			SDoanhThu    string  `json:"sDoanhThu"`
			SLoiNhuan    string  `json:"sLoiNhuan"`
			SEPS         string  `json:"sEPS"`
		} `json:"Rows"`
	} `json:"d"`
}
