package model

import "time"

type StockFilter struct {
	Code      string    `json:"code"`
	Year      int       `json:"year"`
	UpdatedAt time.Time `json:"updated_at"`
}

type StockGrades struct {
	Stock                  string  `json:"stock_code"`
	Name                   string  `json:"name"`
	RevRatioYOY            string  `json:"rev_ratio_yoy"`
	RevRatioQOQ            string  `json:"rev_ratio_qoq"`
	LatestQuarter          string  `json:"latest_quarter"`
	Year                   string  `json:"latest_year"`
	Stars                  string  `json:"stars"`
	NetSmooth              bool    `json:"net_smooth"`
	Price                  string  `json:"price"`
	PricePerExpectedValue  string  `json:"p_ev"`
	IsPriceIncreased       bool    `json:"price_increased"`
	CurrentPercentDividend string  `json:"current_percent_dividend"`
	PercentDividend        string  `json:"percent_dividend"`
	ProfitMarginGraham     string  `json:"profit_margin_graham"`
	PriceVolatilityNumber  float64 `json:"-"`
	PriceVolatility        string  `json:"price_volatility"`
	PriceRisk              string  `json:"price_risk"`
	Token                  string  `json:"token"`
	FrontendURL            string  `json:"frontend_url"`
}
