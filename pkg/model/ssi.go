package model

type SSIOrganization struct {
	Page       int `json:"page"`
	PageSize   int `json:"pageSize"`
	TotalCount int `json:"totalCount"`
	Items      []struct {
		OrganCode      string `json:"organCode"`
		Ticker         string `json:"ticker"`
		ComGroupCode   string `json:"comGroupCode"`
		IcbCode        string `json:"icbCode"`
		OrganTypeCode  string `json:"organTypeCode"`
		ComTypeCode    string `json:"comTypeCode"`
		OrganName      string `json:"organName"`
		OrganShortName string `json:"organShortName"`
	} `json:"items"`
	PackageID any    `json:"packageId"`
	Status    string `json:"status"`
	Errors    any    `json:"errors"`
}

type SSIIncomestatement struct {
	Page       int `json:"page"`
	PageSize   int `json:"pageSize"`
	TotalCount int `json:"totalCount"`
	Items      []struct {
		Quarterly []struct {
			OrganCode     string  `json:"organCode"`
			YearReport    int     `json:"yearReport"`
			QuarterReport int     `json:"quarterReport"`
			Isb27         any     `json:"isb27"`
			Isb30         any     `json:"isb30"`
			Isb38         any     `json:"isb38"`
			Isb41         any     `json:"isb41"`
			Isa20         float64 `json:"isa20"`
			Isa1          float64 `json:"isa1"`
			Isa11         float64 `json:"isa11"`
			Rtq29         float64 `json:"rtq29"`
			Isb25         any     `json:"isb25"`
			Isb26         any     `json:"isb26"`
			Isb28         any     `json:"isb28"`
			Isb29         any     `json:"isb29"`
			Isb31         any     `json:"isb31"`
			Isb32         any     `json:"isb32"`
			Isb33         any     `json:"isb33"`
			Isb34         any     `json:"isb34"`
			Isb35         any     `json:"isb35"`
			Isb36         any     `json:"isb36"`
			Isb37         any     `json:"isb37"`
			Isb39         any     `json:"isb39"`
			Isb40         any     `json:"isb40"`
			Isa16         float64 `json:"isa16"`
			Isa17         float64 `json:"isa17"`
			Isa18         float64 `json:"isa18"`
			Isa19         float64 `json:"isa19"`
			Isa21         float64 `json:"isa21"`
			Isa22         float64 `json:"isa22"`
			Isa23         float64 `json:"isa23"`
			Isa2          float64 `json:"isa2"`
			Isa3          float64 `json:"isa3"`
			Isa4          float64 `json:"isa4"`
			Isa5          float64 `json:"isa5"`
			Isa6          float64 `json:"isa6"`
			Isa7          float64 `json:"isa7"`
			Isa8          float64 `json:"isa8"`
			Isa15         float64 `json:"isa15"`
			Isa9          float64 `json:"isa9"`
			Isa10         float64 `json:"isa10"`
			Isa12         float64 `json:"isa12"`
			Isa13         float64 `json:"isa13"`
			Isa14         float64 `json:"isa14"`
			Isa24         float64 `json:"isa24"`
			Isa102        float64 `json:"isa102"`
			Isi103        any     `json:"isi103"`
			Isi104        any     `json:"isi104"`
			Isi51         any     `json:"isi51"`
			Isi52         any     `json:"isi52"`
			Isi53         any     `json:"isi53"`
			Isi54         any     `json:"isi54"`
			Isi55         any     `json:"isi55"`
			Isi56         any     `json:"isi56"`
			Isi57         any     `json:"isi57"`
			Isi105        any     `json:"isi105"`
			Isi58         any     `json:"isi58"`
			Isi59         any     `json:"isi59"`
			Isi106        any     `json:"isi106"`
			Isi60         any     `json:"isi60"`
			Isi61         any     `json:"isi61"`
			Isi62         any     `json:"isi62"`
			Isi63         any     `json:"isi63"`
			Isi64         any     `json:"isi64"`
			Isi65         any     `json:"isi65"`
			Isi66         any     `json:"isi66"`
			Isi67         any     `json:"isi67"`
			Isi68         any     `json:"isi68"`
			Isi69         any     `json:"isi69"`
			Isi70         any     `json:"isi70"`
			Isi176        any     `json:"isi176"`
			Isi175        any     `json:"isi175"`
			Isi107        any     `json:"isi107"`
			Isi71         any     `json:"isi71"`
			Isi108        any     `json:"isi108"`
			Isi109        any     `json:"isi109"`
			Isi110        any     `json:"isi110"`
			Isi73         any     `json:"isi73"`
			Isi111        any     `json:"isi111"`
			Isi72         any     `json:"isi72"`
			Isi74         any     `json:"isi74"`
			Isi75         any     `json:"isi75"`
			Isi76         any     `json:"isi76"`
			Isi77         any     `json:"isi77"`
			Isi78         any     `json:"isi78"`
			Isi79         any     `json:"isi79"`
			Isi80         any     `json:"isi80"`
			Isi81         any     `json:"isi81"`
			Isi82         any     `json:"isi82"`
			Isi83         any     `json:"isi83"`
			Isi84         any     `json:"isi84"`
			Isi85         any     `json:"isi85"`
			Isi86         any     `json:"isi86"`
			Isi87         any     `json:"isi87"`
			Isi90         any     `json:"isi90"`
			Isi88         any     `json:"isi88"`
			Isi89         any     `json:"isi89"`
			Isi177        any     `json:"isi177"`
			Isi178        any     `json:"isi178"`
			Isi179        any     `json:"isi179"`
			Isi180        any     `json:"isi180"`
			Isi100        any     `json:"isi100"`
			Isi93         any     `json:"isi93"`
			Isi91         any     `json:"isi91"`
			Isi92         any     `json:"isi92"`
			Isi97         any     `json:"isi97"`
			Isi112        any     `json:"isi112"`
			Isi113        any     `json:"isi113"`
			Isi114        any     `json:"isi114"`
			Isi94         any     `json:"isi94"`
			Isi95         any     `json:"isi95"`
			Isi96         any     `json:"isi96"`
			Isi98         any     `json:"isi98"`
			Isi99         any     `json:"isi99"`
			Isi101        any     `json:"isi101"`
			Iss115        any     `json:"iss115"`
			Iss116        any     `json:"iss116"`
			Iss117        any     `json:"iss117"`
			Iss118        any     `json:"iss118"`
			Iss119        any     `json:"iss119"`
			Iss120        any     `json:"iss120"`
			Iss121        any     `json:"iss121"`
			Iss122        any     `json:"iss122"`
			Iss42         any     `json:"iss42"`
			Iss44         any     `json:"iss44"`
			Iss45         any     `json:"iss45"`
			Iss46         any     `json:"iss46"`
			Iss48         any     `json:"iss48"`
			Iss47         any     `json:"iss47"`
			Iss43         any     `json:"iss43"`
			Iss49         any     `json:"iss49"`
			Iss123        any     `json:"iss123"`
			Iss50         any     `json:"iss50"`
			Iss124        any     `json:"iss124"`
			Iss125        any     `json:"iss125"`
			Iss126        any     `json:"iss126"`
			Iss127        any     `json:"iss127"`
			Iss128        any     `json:"iss128"`
			Iss129        any     `json:"iss129"`
			Iss130        any     `json:"iss130"`
			Iss168        any     `json:"iss168"`
			Iss131        any     `json:"iss131"`
			Iss132        any     `json:"iss132"`
			Iss133        any     `json:"iss133"`
			Iss134        any     `json:"iss134"`
			Iss135        any     `json:"iss135"`
			Iss136        any     `json:"iss136"`
			Iss137        any     `json:"iss137"`
			Iss138        any     `json:"iss138"`
			Iss139        any     `json:"iss139"`
			Iss141        any     `json:"iss141"`
			Iss142        any     `json:"iss142"`
			Iss143        any     `json:"iss143"`
			Iss144        any     `json:"iss144"`
			Iss145        any     `json:"iss145"`
			Iss146        any     `json:"iss146"`
			Iss147        any     `json:"iss147"`
			Iss148        any     `json:"iss148"`
			Iss149        any     `json:"iss149"`
			Iss150        any     `json:"iss150"`
			Iss151        any     `json:"iss151"`
			Iss152        any     `json:"iss152"`
			Iss153        any     `json:"iss153"`
			Iss154        any     `json:"iss154"`
			Iss155        any     `json:"iss155"`
			Iss164        any     `json:"iss164"`
			Iss156        any     `json:"iss156"`
			Iss157        any     `json:"iss157"`
			Iss158        any     `json:"iss158"`
			Iss159        any     `json:"iss159"`
			Iss160        any     `json:"iss160"`
			Iss161        any     `json:"iss161"`
			Iss162        any     `json:"iss162"`
			Iss163        any     `json:"iss163"`
			Iss165        any     `json:"iss165"`
			Iss166        any     `json:"iss166"`
			Iss167        any     `json:"iss167"`
			Cfa2          float64 `json:"cfa2"`
			Ebit          float64 `json:"ebit"`
			EbitDa        float64 `json:"ebitDa"`
		} `json:"quarterly"`
		Yearly []struct {
			OrganCode     string  `json:"organCode"`
			YearReport    int     `json:"yearReport"`
			QuarterReport int     `json:"quarterReport"`
			Isb27         any     `json:"isb27"`
			Isb30         any     `json:"isb30"`
			Isb38         any     `json:"isb38"`
			Isb41         any     `json:"isb41"`
			Isa20         float64 `json:"isa20"`
			Isa1          float64 `json:"isa1"`
			Isa11         float64 `json:"isa11"`
			Rtq29         float64 `json:"rtq29"`
			Isb25         any     `json:"isb25"`
			Isb26         any     `json:"isb26"`
			Isb28         any     `json:"isb28"`
			Isb29         any     `json:"isb29"`
			Isb31         any     `json:"isb31"`
			Isb32         any     `json:"isb32"`
			Isb33         any     `json:"isb33"`
			Isb34         any     `json:"isb34"`
			Isb35         any     `json:"isb35"`
			Isb36         any     `json:"isb36"`
			Isb37         any     `json:"isb37"`
			Isb39         any     `json:"isb39"`
			Isb40         any     `json:"isb40"`
			Isa16         float64 `json:"isa16"`
			Isa17         float64 `json:"isa17"`
			Isa18         float64 `json:"isa18"`
			Isa19         float64 `json:"isa19"`
			Isa21         float64 `json:"isa21"`
			Isa22         float64 `json:"isa22"`
			Isa23         float64 `json:"isa23"`
			Isa2          float64 `json:"isa2"`
			Isa3          float64 `json:"isa3"`
			Isa4          float64 `json:"isa4"`
			Isa5          float64 `json:"isa5"`
			Isa6          float64 `json:"isa6"`
			Isa7          float64 `json:"isa7"`
			Isa8          float64 `json:"isa8"`
			Isa15         float64 `json:"isa15"`
			Isa9          float64 `json:"isa9"`
			Isa10         float64 `json:"isa10"`
			Isa12         float64 `json:"isa12"`
			Isa13         float64 `json:"isa13"`
			Isa14         float64 `json:"isa14"`
			Isa24         float64 `json:"isa24"`
			Isa102        float64 `json:"isa102"`
			Isi103        any     `json:"isi103"`
			Isi104        any     `json:"isi104"`
			Isi51         any     `json:"isi51"`
			Isi52         any     `json:"isi52"`
			Isi53         any     `json:"isi53"`
			Isi54         any     `json:"isi54"`
			Isi55         any     `json:"isi55"`
			Isi56         any     `json:"isi56"`
			Isi57         any     `json:"isi57"`
			Isi105        any     `json:"isi105"`
			Isi58         any     `json:"isi58"`
			Isi59         any     `json:"isi59"`
			Isi106        any     `json:"isi106"`
			Isi60         any     `json:"isi60"`
			Isi61         any     `json:"isi61"`
			Isi62         any     `json:"isi62"`
			Isi63         any     `json:"isi63"`
			Isi64         any     `json:"isi64"`
			Isi65         any     `json:"isi65"`
			Isi66         any     `json:"isi66"`
			Isi67         any     `json:"isi67"`
			Isi68         any     `json:"isi68"`
			Isi69         any     `json:"isi69"`
			Isi70         any     `json:"isi70"`
			Isi176        any     `json:"isi176"`
			Isi175        any     `json:"isi175"`
			Isi107        any     `json:"isi107"`
			Isi71         any     `json:"isi71"`
			Isi108        any     `json:"isi108"`
			Isi109        any     `json:"isi109"`
			Isi110        any     `json:"isi110"`
			Isi73         any     `json:"isi73"`
			Isi111        any     `json:"isi111"`
			Isi72         any     `json:"isi72"`
			Isi74         any     `json:"isi74"`
			Isi75         any     `json:"isi75"`
			Isi76         any     `json:"isi76"`
			Isi77         any     `json:"isi77"`
			Isi78         any     `json:"isi78"`
			Isi79         any     `json:"isi79"`
			Isi80         any     `json:"isi80"`
			Isi81         any     `json:"isi81"`
			Isi82         any     `json:"isi82"`
			Isi83         any     `json:"isi83"`
			Isi84         any     `json:"isi84"`
			Isi85         any     `json:"isi85"`
			Isi86         any     `json:"isi86"`
			Isi87         any     `json:"isi87"`
			Isi90         any     `json:"isi90"`
			Isi88         any     `json:"isi88"`
			Isi89         any     `json:"isi89"`
			Isi177        any     `json:"isi177"`
			Isi178        any     `json:"isi178"`
			Isi179        any     `json:"isi179"`
			Isi180        any     `json:"isi180"`
			Isi100        any     `json:"isi100"`
			Isi93         any     `json:"isi93"`
			Isi91         any     `json:"isi91"`
			Isi92         any     `json:"isi92"`
			Isi97         any     `json:"isi97"`
			Isi112        any     `json:"isi112"`
			Isi113        any     `json:"isi113"`
			Isi114        any     `json:"isi114"`
			Isi94         any     `json:"isi94"`
			Isi95         any     `json:"isi95"`
			Isi96         any     `json:"isi96"`
			Isi98         any     `json:"isi98"`
			Isi99         any     `json:"isi99"`
			Isi101        any     `json:"isi101"`
			Iss115        any     `json:"iss115"`
			Iss116        any     `json:"iss116"`
			Iss117        any     `json:"iss117"`
			Iss118        any     `json:"iss118"`
			Iss119        any     `json:"iss119"`
			Iss120        any     `json:"iss120"`
			Iss121        any     `json:"iss121"`
			Iss122        any     `json:"iss122"`
			Iss42         any     `json:"iss42"`
			Iss44         any     `json:"iss44"`
			Iss45         any     `json:"iss45"`
			Iss46         any     `json:"iss46"`
			Iss48         any     `json:"iss48"`
			Iss47         any     `json:"iss47"`
			Iss43         any     `json:"iss43"`
			Iss49         any     `json:"iss49"`
			Iss123        any     `json:"iss123"`
			Iss50         any     `json:"iss50"`
			Iss124        any     `json:"iss124"`
			Iss125        any     `json:"iss125"`
			Iss126        any     `json:"iss126"`
			Iss127        any     `json:"iss127"`
			Iss128        any     `json:"iss128"`
			Iss129        any     `json:"iss129"`
			Iss130        any     `json:"iss130"`
			Iss168        any     `json:"iss168"`
			Iss131        any     `json:"iss131"`
			Iss132        any     `json:"iss132"`
			Iss133        any     `json:"iss133"`
			Iss134        any     `json:"iss134"`
			Iss135        any     `json:"iss135"`
			Iss136        any     `json:"iss136"`
			Iss137        any     `json:"iss137"`
			Iss138        any     `json:"iss138"`
			Iss139        any     `json:"iss139"`
			Iss141        any     `json:"iss141"`
			Iss142        any     `json:"iss142"`
			Iss143        any     `json:"iss143"`
			Iss144        any     `json:"iss144"`
			Iss145        any     `json:"iss145"`
			Iss146        any     `json:"iss146"`
			Iss147        any     `json:"iss147"`
			Iss148        any     `json:"iss148"`
			Iss149        any     `json:"iss149"`
			Iss150        any     `json:"iss150"`
			Iss151        any     `json:"iss151"`
			Iss152        any     `json:"iss152"`
			Iss153        any     `json:"iss153"`
			Iss154        any     `json:"iss154"`
			Iss155        any     `json:"iss155"`
			Iss164        any     `json:"iss164"`
			Iss156        any     `json:"iss156"`
			Iss157        any     `json:"iss157"`
			Iss158        any     `json:"iss158"`
			Iss159        any     `json:"iss159"`
			Iss160        any     `json:"iss160"`
			Iss161        any     `json:"iss161"`
			Iss162        any     `json:"iss162"`
			Iss163        any     `json:"iss163"`
			Iss165        any     `json:"iss165"`
			Iss166        any     `json:"iss166"`
			Iss167        any     `json:"iss167"`
			Cfa2          float64 `json:"cfa2"`
			Ebit          float64 `json:"ebit"`
			EbitDa        float64 `json:"ebitDa"`
		} `json:"yearly"`
	} `json:"items"`
	PackageID any    `json:"packageId"`
	Status    string `json:"status"`
	Errors    any    `json:"errors"`
}

type SSIBalanceSheet struct {
	Page       int `json:"page"`
	PageSize   int `json:"pageSize"`
	TotalCount int `json:"totalCount"`
	Items      []struct {
		Quarterly []struct {
			YearReport        int     `json:"yearReport"`
			QuarterReport     int     `json:"quarterReport"`
			Bsb98             any     `json:"bsb98"`
			Bsb99             any     `json:"bsb99"`
			Bsb103            any     `json:"bsb103"`
			Bsb106            any     `json:"bsb106"`
			OtherAssetBank    any     `json:"otherAssetBank"`
			Bsb113            any     `json:"bsb113"`
			OtherLiabilties   any     `json:"otherLiabilties"`
			Bsa78             float64 `json:"bsa78"`
			Bsa95             float64 `json:"bsa95"`
			Bsa1              float64 `json:"bsa1"`
			Bsa30             float64 `json:"bsa30"`
			Bsa36             float64 `json:"bsa36"`
			OtherAssetNonBank float64 `json:"otherAssetNonBank"`
			Bsa55             float64 `json:"bsa55"`
			Bsa67             float64 `json:"bsa67"`
			Bsa53             float64 `json:"bsa53"`
			Bsa2              float64 `json:"bsa2"`
			Bsb97             any     `json:"bsb97"`
			Bsb100            any     `json:"bsb100"`
			Bsb101            any     `json:"bsb101"`
			Bsb102            any     `json:"bsb102"`
			Bsb104            any     `json:"bsb104"`
			Bsb105            any     `json:"bsb105"`
			Bsb107            any     `json:"bsb107"`
			Bsb108            float64 `json:"bsb108"`
			Bsb109            any     `json:"bsb109"`
			Bsa43             float64 `json:"bsa43"`
			Bsa44             float64 `json:"bsa44"`
			Bsa45             float64 `json:"bsa45"`
			Bsa46             float64 `json:"bsa46"`
			Bsa47             float64 `json:"bsa47"`
			Bsa29             float64 `json:"bsa29"`
			Bsa33             float64 `json:"bsa33"`
			Bsa40             float64 `json:"bsa40"`
			Bsb110            any     `json:"bsb110"`
			Bsa96             float64 `json:"bsa96"`
			Bsa54             float64 `json:"bsa54"`
			Bsb111            any     `json:"bsb111"`
			Bsb112            any     `json:"bsb112"`
			Bsb114            any     `json:"bsb114"`
			Bsb115            any     `json:"bsb115"`
			Bsb116            any     `json:"bsb116"`
			Bsb117            any     `json:"bsb117"`
			Bsb118            any     `json:"bsb118"`
			Bsa80             float64 `json:"bsa80"`
			Bsb119            any     `json:"bsb119"`
			Bsa81             float64 `json:"bsa81"`
			Bsa83             float64 `json:"bsa83"`
			Bsb120            float64 `json:"bsb120"`
			Bsa82             float64 `json:"bsa82"`
			Bsb121            any     `json:"bsb121"`
			Bsb174            any     `json:"bsb174"`
			Bsa85             float64 `json:"bsa85"`
			Bsa84             float64 `json:"bsa84"`
			Bsa90             float64 `json:"bsa90"`
			Bsa3              float64 `json:"bsa3"`
			Bsa4              float64 `json:"bsa4"`
			Bsa5              float64 `json:"bsa5"`
			Bsa6              float64 `json:"bsa6"`
			Bsa7              float64 `json:"bsa7"`
			Bsa8              float64 `json:"bsa8"`
			Bsa9              float64 `json:"bsa9"`
			Bsa10             float64 `json:"bsa10"`
			Bsa11             float64 `json:"bsa11"`
			Bsa12             float64 `json:"bsa12"`
			Bsa159            float64 `json:"bsa159"`
			Bsa13             float64 `json:"bsa13"`
			Bsa14             float64 `json:"bsa14"`
			BsI141            float64 `json:"bsI141"`
			Bsa15             float64 `json:"bsa15"`
			Bsa16             float64 `json:"bsa16"`
			Bsa17             float64 `json:"bsa17"`
			Bsa18             float64 `json:"bsa18"`
			Bsa19             float64 `json:"bsa19"`
			Bsa20             float64 `json:"bsa20"`
			Bsa21             float64 `json:"bsa21"`
			Bsa22             float64 `json:"bsa22"`
			Bsa160            float64 `json:"bsa160"`
			Bsa165            float64 `json:"bsa165"`
			Bsa169            float64 `json:"bsa169"`
			Bsa23             float64 `json:"bsa23"`
			Bsa24             float64 `json:"bsa24"`
			Bsa25             float64 `json:"bsa25"`
			Bsa161            float64 `json:"bsa161"`
			BsS134            float64 `json:"bsS134"`
			Bsa26             float64 `json:"bsa26"`
			Bsa162            float64 `json:"bsa162"`
			Bsa27             float64 `json:"bsa27"`
			Bsa28             float64 `json:"bsa28"`
			Bsa31             float64 `json:"bsa31"`
			Bsa32             float64 `json:"bsa32"`
			Bsa34             float64 `json:"bsa34"`
			Bsa35             float64 `json:"bsa35"`
			Bsa37             float64 `json:"bsa37"`
			Bsa38             float64 `json:"bsa38"`
			Bsa41             float64 `json:"bsa41"`
			Bsa42             float64 `json:"bsa42"`
			Bsa163            float64 `json:"bsa163"`
			Bsa164            float64 `json:"bsa164"`
			Bsa39             float64 `json:"bsa39"`
			Bsa49             float64 `json:"bsa49"`
			Bsa50             float64 `json:"bsa50"`
			Bsa51             float64 `json:"bsa51"`
			Bsa166            float64 `json:"bsa166"`
			Bsa52             float64 `json:"bsa52"`
			Bsa48             float64 `json:"bsa48"`
			Bsa57             float64 `json:"bsa57"`
			Bsa58             float64 `json:"bsa58"`
			Bsa59             float64 `json:"bsa59"`
			Bsa60             float64 `json:"bsa60"`
			Bsa61             float64 `json:"bsa61"`
			Bsa62             float64 `json:"bsa62"`
			Bsa63             float64 `json:"bsa63"`
			Bsa167            float64 `json:"bsa167"`
			Bsa64             float64 `json:"bsa64"`
			Bsa56             float64 `json:"bsa56"`
			Bsa65             float64 `json:"bsa65"`
			Bsa66             float64 `json:"bsa66"`
			Bsa168            float64 `json:"bsa168"`
			Bsa68             float64 `json:"bsa68"`
			Bsa170            float64 `json:"bsa170"`
			Bsa171            float64 `json:"bsa171"`
			Bsa172            float64 `json:"bsa172"`
			Bsa69             float64 `json:"bsa69"`
			Bsa76             float64 `json:"bsa76"`
			Bsa70             float64 `json:"bsa70"`
			Bsa71             float64 `json:"bsa71"`
			Bsa173            float64 `json:"bsa173"`
			Bsa72             float64 `json:"bsa72"`
			Bsa73             float64 `json:"bsa73"`
			Bsa74             float64 `json:"bsa74"`
			Bsa77             float64 `json:"bsa77"`
			Bsa79             float64 `json:"bsa79"`
			Bsa175            float64 `json:"bsa175"`
			Bsa176            float64 `json:"bsa176"`
			Bsa86             float64 `json:"bsa86"`
			Bsa91             float64 `json:"bsa91"`
			Bsa87             float64 `json:"bsa87"`
			Bsa89             float64 `json:"bsa89"`
			Bsa177            float64 `json:"bsa177"`
			Bsa178            float64 `json:"bsa178"`
			Bsa210            float64 `json:"bsa210"`
			Bsa92             float64 `json:"bsa92"`
			Bsa93             float64 `json:"bsa93"`
			Bsa94             float64 `json:"bsa94"`
			Bsa211            float64 `json:"bsa211"`
			Bsi198            any     `json:"bsi198"`
			Bsi199            any     `json:"bsi199"`
			Bsi139            any     `json:"bsi139"`
			Bsi140            any     `json:"bsi140"`
			Bsi190            any     `json:"bsi190"`
			Bsi191            any     `json:"bsi191"`
			Bsi142            any     `json:"bsi142"`
			Bsi192            any     `json:"bsi192"`
			Bsi193            any     `json:"bsi193"`
			Bsi194            any     `json:"bsi194"`
			Bsi143            any     `json:"bsi143"`
			Bsi144            any     `json:"bsi144"`
			Bsi145            any     `json:"bsi145"`
			Bsi195            any     `json:"bsi195"`
			Bsi196            any     `json:"bsi196"`
			Bsa188            float64 `json:"bsa188"`
			Bsi146            any     `json:"bsi146"`
			Bsa209            float64 `json:"bsa209"`
			Bsi200            any     `json:"bsi200"`
			Bsi201            any     `json:"bsi201"`
			Bsi197            any     `json:"bsi197"`
			Bsi202            any     `json:"bsi202"`
			Bsi203            any     `json:"bsi203"`
			Bsi204            any     `json:"bsi204"`
			Bsi205            any     `json:"bsi205"`
			Bsi206            any     `json:"bsi206"`
			Bsi207            any     `json:"bsi207"`
			Bsi208            any     `json:"bsi208"`
			Bsi147            any     `json:"bsi147"`
			Bsi148            any     `json:"bsi148"`
			Bsi149            any     `json:"bsi149"`
			Bsa174            float64 `json:"bsa174"`
			Bsi150            any     `json:"bsi150"`
			Bsa75             any     `json:"bsa75"`
			Bsi151            any     `json:"bsi151"`
			Bsi152            any     `json:"bsi152"`
			Bsi153            any     `json:"bsi153"`
			Bsi154            any     `json:"bsi154"`
			Bsi155            any     `json:"bsi155"`
			Bsi156            any     `json:"bsi156"`
			Bsa88             any     `json:"bsa88"`
			Bss214            any     `json:"bss214"`
			Bss215            any     `json:"bss215"`
			Bss216            any     `json:"bss216"`
			Bss217            any     `json:"bss217"`
			Bss223            any     `json:"bss223"`
			Bss224            any     `json:"bss224"`
			Bss225            any     `json:"bss225"`
			Bss133            any     `json:"bss133"`
			Bss226            any     `json:"bss226"`
			Bss227            any     `json:"bss227"`
			Bss228            any     `json:"bss228"`
			Bss229            any     `json:"bss229"`
			Bss230            any     `json:"bss230"`
			Bss231            any     `json:"bss231"`
			Bss189            any     `json:"bss189"`
			Bss232            any     `json:"bss232"`
			Bss233            any     `json:"bss233"`
			Bss234            any     `json:"bss234"`
			Bss235            any     `json:"bss235"`
			Bss236            any     `json:"bss236"`
			Bss212            any     `json:"bss212"`
			Bss237            any     `json:"bss237"`
			Bss238            any     `json:"bss238"`
			Bss239            any     `json:"bss239"`
			Bss240            any     `json:"bss240"`
			Bss241            any     `json:"bss241"`
			Bss242            any     `json:"bss242"`
			Bss243            any     `json:"bss243"`
			Bss135            any     `json:"bss135"`
			Bss244            any     `json:"bss244"`
			Bss245            any     `json:"bss245"`
			Bss246            any     `json:"bss246"`
			Bss136            any     `json:"bss136"`
			Bss137            any     `json:"bss137"`
			Bss247            any     `json:"bss247"`
			Bss248            any     `json:"bss248"`
			Bss249            any     `json:"bss249"`
			Bss250            any     `json:"bss250"`
			Bss251            any     `json:"bss251"`
			Bss138            any     `json:"bss138"`
			Bss252            any     `json:"bss252"`
			Bss253            any     `json:"bss253"`
			Bss254            any     `json:"bss254"`
			Bss255            any     `json:"bss255"`
			Bss256            any     `json:"bss256"`
			Bss257            any     `json:"bss257"`
		} `json:"quarterly"`
		Yearly []struct {
			YearReport        int     `json:"yearReport"`
			QuarterReport     int     `json:"quarterReport"`
			Bsb98             any     `json:"bsb98"`
			Bsb99             any     `json:"bsb99"`
			Bsb103            any     `json:"bsb103"`
			Bsb106            any     `json:"bsb106"`
			OtherAssetBank    any     `json:"otherAssetBank"`
			Bsb113            any     `json:"bsb113"`
			OtherLiabilties   any     `json:"otherLiabilties"`
			Bsa78             float64 `json:"bsa78"`
			Bsa95             float64 `json:"bsa95"`
			Bsa1              float64 `json:"bsa1"`
			Bsa30             float64 `json:"bsa30"`
			Bsa36             float64 `json:"bsa36"`
			OtherAssetNonBank float64 `json:"otherAssetNonBank"`
			Bsa55             float64 `json:"bsa55"`
			Bsa67             float64 `json:"bsa67"`
			Bsa53             float64 `json:"bsa53"`
			Bsa2              float64 `json:"bsa2"`
			Bsb97             any     `json:"bsb97"`
			Bsb100            any     `json:"bsb100"`
			Bsb101            any     `json:"bsb101"`
			Bsb102            any     `json:"bsb102"`
			Bsb104            any     `json:"bsb104"`
			Bsb105            any     `json:"bsb105"`
			Bsb107            any     `json:"bsb107"`
			Bsb108            float64 `json:"bsb108"`
			Bsb109            any     `json:"bsb109"`
			Bsa43             float64 `json:"bsa43"`
			Bsa44             float64 `json:"bsa44"`
			Bsa45             float64 `json:"bsa45"`
			Bsa46             float64 `json:"bsa46"`
			Bsa47             float64 `json:"bsa47"`
			Bsa29             float64 `json:"bsa29"`
			Bsa33             float64 `json:"bsa33"`
			Bsa40             float64 `json:"bsa40"`
			Bsb110            any     `json:"bsb110"`
			Bsa96             float64 `json:"bsa96"`
			Bsa54             float64 `json:"bsa54"`
			Bsb111            any     `json:"bsb111"`
			Bsb112            any     `json:"bsb112"`
			Bsb114            any     `json:"bsb114"`
			Bsb115            any     `json:"bsb115"`
			Bsb116            any     `json:"bsb116"`
			Bsb117            any     `json:"bsb117"`
			Bsb118            any     `json:"bsb118"`
			Bsa80             float64 `json:"bsa80"`
			Bsb119            any     `json:"bsb119"`
			Bsa81             float64 `json:"bsa81"`
			Bsa83             float64 `json:"bsa83"`
			Bsb120            float64 `json:"bsb120"`
			Bsa82             float64 `json:"bsa82"`
			Bsb121            any     `json:"bsb121"`
			Bsb174            any     `json:"bsb174"`
			Bsa85             float64 `json:"bsa85"`
			Bsa84             float64 `json:"bsa84"`
			Bsa90             float64 `json:"bsa90"`
			Bsa3              float64 `json:"bsa3"`
			Bsa4              float64 `json:"bsa4"`
			Bsa5              float64 `json:"bsa5"`
			Bsa6              float64 `json:"bsa6"`
			Bsa7              float64 `json:"bsa7"`
			Bsa8              float64 `json:"bsa8"`
			Bsa9              float64 `json:"bsa9"`
			Bsa10             float64 `json:"bsa10"`
			Bsa11             float64 `json:"bsa11"`
			Bsa12             float64 `json:"bsa12"`
			Bsa159            float64 `json:"bsa159"`
			Bsa13             float64 `json:"bsa13"`
			Bsa14             float64 `json:"bsa14"`
			BsI141            float64 `json:"bsI141"`
			Bsa15             float64 `json:"bsa15"`
			Bsa16             float64 `json:"bsa16"`
			Bsa17             float64 `json:"bsa17"`
			Bsa18             float64 `json:"bsa18"`
			Bsa19             float64 `json:"bsa19"`
			Bsa20             float64 `json:"bsa20"`
			Bsa21             float64 `json:"bsa21"`
			Bsa22             float64 `json:"bsa22"`
			Bsa160            float64 `json:"bsa160"`
			Bsa165            float64 `json:"bsa165"`
			Bsa169            float64 `json:"bsa169"`
			Bsa23             float64 `json:"bsa23"`
			Bsa24             float64 `json:"bsa24"`
			Bsa25             float64 `json:"bsa25"`
			Bsa161            float64 `json:"bsa161"`
			BsS134            float64 `json:"bsS134"`
			Bsa26             float64 `json:"bsa26"`
			Bsa162            float64 `json:"bsa162"`
			Bsa27             float64 `json:"bsa27"`
			Bsa28             float64 `json:"bsa28"`
			Bsa31             float64 `json:"bsa31"`
			Bsa32             float64 `json:"bsa32"`
			Bsa34             float64 `json:"bsa34"`
			Bsa35             float64 `json:"bsa35"`
			Bsa37             float64 `json:"bsa37"`
			Bsa38             float64 `json:"bsa38"`
			Bsa41             float64 `json:"bsa41"`
			Bsa42             float64 `json:"bsa42"`
			Bsa163            float64 `json:"bsa163"`
			Bsa164            float64 `json:"bsa164"`
			Bsa39             float64 `json:"bsa39"`
			Bsa49             float64 `json:"bsa49"`
			Bsa50             float64 `json:"bsa50"`
			Bsa51             float64 `json:"bsa51"`
			Bsa166            float64 `json:"bsa166"`
			Bsa52             float64 `json:"bsa52"`
			Bsa48             float64 `json:"bsa48"`
			Bsa57             float64 `json:"bsa57"`
			Bsa58             float64 `json:"bsa58"`
			Bsa59             float64 `json:"bsa59"`
			Bsa60             float64 `json:"bsa60"`
			Bsa61             float64 `json:"bsa61"`
			Bsa62             float64 `json:"bsa62"`
			Bsa63             float64 `json:"bsa63"`
			Bsa167            float64 `json:"bsa167"`
			Bsa64             float64 `json:"bsa64"`
			Bsa56             float64 `json:"bsa56"`
			Bsa65             float64 `json:"bsa65"`
			Bsa66             float64 `json:"bsa66"`
			Bsa168            float64 `json:"bsa168"`
			Bsa68             float64 `json:"bsa68"`
			Bsa170            float64 `json:"bsa170"`
			Bsa171            float64 `json:"bsa171"`
			Bsa172            float64 `json:"bsa172"`
			Bsa69             float64 `json:"bsa69"`
			Bsa76             float64 `json:"bsa76"`
			Bsa70             float64 `json:"bsa70"`
			Bsa71             float64 `json:"bsa71"`
			Bsa173            float64 `json:"bsa173"`
			Bsa72             float64 `json:"bsa72"`
			Bsa73             float64 `json:"bsa73"`
			Bsa74             float64 `json:"bsa74"`
			Bsa77             float64 `json:"bsa77"`
			Bsa79             float64 `json:"bsa79"`
			Bsa175            float64 `json:"bsa175"`
			Bsa176            float64 `json:"bsa176"`
			Bsa86             float64 `json:"bsa86"`
			Bsa91             float64 `json:"bsa91"`
			Bsa87             float64 `json:"bsa87"`
			Bsa89             float64 `json:"bsa89"`
			Bsa177            float64 `json:"bsa177"`
			Bsa178            float64 `json:"bsa178"`
			Bsa210            float64 `json:"bsa210"`
			Bsa92             float64 `json:"bsa92"`
			Bsa93             float64 `json:"bsa93"`
			Bsa94             float64 `json:"bsa94"`
			Bsa211            float64 `json:"bsa211"`
			Bsi198            any     `json:"bsi198"`
			Bsi199            any     `json:"bsi199"`
			Bsi139            any     `json:"bsi139"`
			Bsi140            any     `json:"bsi140"`
			Bsi190            any     `json:"bsi190"`
			Bsi191            any     `json:"bsi191"`
			Bsi142            any     `json:"bsi142"`
			Bsi192            any     `json:"bsi192"`
			Bsi193            any     `json:"bsi193"`
			Bsi194            any     `json:"bsi194"`
			Bsi143            any     `json:"bsi143"`
			Bsi144            any     `json:"bsi144"`
			Bsi145            any     `json:"bsi145"`
			Bsi195            any     `json:"bsi195"`
			Bsi196            any     `json:"bsi196"`
			Bsa188            float64 `json:"bsa188"`
			Bsi146            any     `json:"bsi146"`
			Bsa209            float64 `json:"bsa209"`
			Bsi200            any     `json:"bsi200"`
			Bsi201            any     `json:"bsi201"`
			Bsi197            any     `json:"bsi197"`
			Bsi202            any     `json:"bsi202"`
			Bsi203            any     `json:"bsi203"`
			Bsi204            any     `json:"bsi204"`
			Bsi205            any     `json:"bsi205"`
			Bsi206            any     `json:"bsi206"`
			Bsi207            any     `json:"bsi207"`
			Bsi208            any     `json:"bsi208"`
			Bsi147            any     `json:"bsi147"`
			Bsi148            any     `json:"bsi148"`
			Bsi149            any     `json:"bsi149"`
			Bsa174            float64 `json:"bsa174"`
			Bsi150            any     `json:"bsi150"`
			Bsa75             any     `json:"bsa75"`
			Bsi151            any     `json:"bsi151"`
			Bsi152            any     `json:"bsi152"`
			Bsi153            any     `json:"bsi153"`
			Bsi154            any     `json:"bsi154"`
			Bsi155            any     `json:"bsi155"`
			Bsi156            any     `json:"bsi156"`
			Bsa88             any     `json:"bsa88"`
			Bss214            any     `json:"bss214"`
			Bss215            any     `json:"bss215"`
			Bss216            any     `json:"bss216"`
			Bss217            any     `json:"bss217"`
			Bss223            any     `json:"bss223"`
			Bss224            any     `json:"bss224"`
			Bss225            any     `json:"bss225"`
			Bss133            any     `json:"bss133"`
			Bss226            any     `json:"bss226"`
			Bss227            any     `json:"bss227"`
			Bss228            any     `json:"bss228"`
			Bss229            any     `json:"bss229"`
			Bss230            any     `json:"bss230"`
			Bss231            any     `json:"bss231"`
			Bss189            any     `json:"bss189"`
			Bss232            any     `json:"bss232"`
			Bss233            any     `json:"bss233"`
			Bss234            any     `json:"bss234"`
			Bss235            any     `json:"bss235"`
			Bss236            any     `json:"bss236"`
			Bss212            any     `json:"bss212"`
			Bss237            any     `json:"bss237"`
			Bss238            any     `json:"bss238"`
			Bss239            any     `json:"bss239"`
			Bss240            any     `json:"bss240"`
			Bss241            any     `json:"bss241"`
			Bss242            any     `json:"bss242"`
			Bss243            any     `json:"bss243"`
			Bss135            any     `json:"bss135"`
			Bss244            any     `json:"bss244"`
			Bss245            any     `json:"bss245"`
			Bss246            any     `json:"bss246"`
			Bss136            any     `json:"bss136"`
			Bss137            any     `json:"bss137"`
			Bss247            any     `json:"bss247"`
			Bss248            any     `json:"bss248"`
			Bss249            any     `json:"bss249"`
			Bss250            any     `json:"bss250"`
			Bss251            any     `json:"bss251"`
			Bss138            any     `json:"bss138"`
			Bss252            any     `json:"bss252"`
			Bss253            any     `json:"bss253"`
			Bss254            any     `json:"bss254"`
			Bss255            any     `json:"bss255"`
			Bss256            any     `json:"bss256"`
			Bss257            any     `json:"bss257"`
		} `json:"yearly"`
	} `json:"items"`
	PackageID any    `json:"packageId"`
	Status    string `json:"status"`
	Errors    any    `json:"errors"`
}
