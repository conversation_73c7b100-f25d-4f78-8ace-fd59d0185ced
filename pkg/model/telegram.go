package model

type ChatMessage struct {
	UpdateID int `json:"update_id"`
	Message  struct {
		MessageID int `json:"message_id"`
		From      struct {
			ID           int    `json:"id"`
			IsBot        bool   `json:"is_bot"`
			FirstName    string `json:"first_name"`
			LastName     string `json:"last_name"`
			Username     string `json:"username"`
			LanguageCode string `json:"language_code"`
		} `json:"from"`
		Chat struct {
			ID        int    `json:"id"`
			FirstName string `json:"first_name"`
			LastName  string `json:"last_name"`
			Username  string `json:"username"`
			Type      string `json:"type"`
		} `json:"chat"`
		Date int    `json:"date"`
		Text string `json:"text"`
	} `json:"message"`
}

// Create a struct to conform to the JSON body
// of the send message request
// https://core.telegram.org/bots/api#sendmessage
type SendMessageReqBody struct {
	ChatID    string `json:"chat_id"`
	Text      string `json:"text"`
	ParseMode string `json:"parse_mode"`
}

// https://core.telegram.org/bots/api#sendchataction
type SendChatActionReqBody struct {
	ChatID string `json:"chat_id"`
	Action string `json:"action"`
}

// https://core.telegram.org/bots/api#setMessageReaction
type SendMessageReactionReqBody struct {
	ChatID       string          `json:"chat_id"`
	MessageID    int             `json:"message_id"`
	ReactionType []*ReactionType `json:"reaction"`
	IsBig        bool            `json:"is_big"`
}

type ReactionType struct {
	Type  string `json:"type"` // always “emoji”
	Emoji string `json:"emoji"`
}

type SendMessageIntent struct {
	Intent  string              `json:"intent"`
	Context string              `json:"context"`
	Message *SendMessageReqBody `json:"message"`
}
