package model

type TradingStock struct {
	Sc        string  `json:"_sc_"`
	Bp        float64 `json:"_bp_"`
	Clp       float64 `json:"_clp_"`
	Fp        float64 `json:"_fp_"`
	Op        float64 `json:"_op_"`
	Cp        float64 `json:"_cp_"`
	Lp        float64 `json:"_lp_"`
	Change    float64 `json:"change"`
	Pc        float64 `json:"_pc_"`
	Vhtt      float64 `json:"_vhtt_"`
	In        string  `json:"_in_"`
	Sin       string  `json:"_sin_"`
	CatID     int     `json:"catID"`
	StockName string  `json:"stockName"`
	Diviend   float64 `json:"_diviend_"`
}

type FinanReportHeaders struct {
	Headers     []*YearPeriodHeader
	FinancialRp *FinancialReport
}

type FinancialReport struct {
	CNIKToN           []KTQuKinhDoanh `json:"Cân đối kế toán"`
	KTQuKinhDoanh     []KTQuKinhD<PERSON>h `json:"Kết qu<PERSON> kinh doanh"`
	NhMChSNhGi        []KTQuKinhDoanh `json:"Nhóm chỉ số Định giá"`
	LUChuyNTiNTGiNTiP []KTQuKinhDoanh `json:"Lưu chuyển tiền tệ gián tiếp"`
	LUChuyNTiNTTNTiP  []KTQuKinhDoanh `json:"Lưu chuyển tiền tệ trực tiếp"`
}

type KTQuKinhDoanh struct {
	ID                    int         `json:"ID"`
	ReportNormID          int         `json:"ReportNormID"`
	Name                  string      `json:"Name"`
	NameEn                string      `json:"NameEn"`
	NameMobile            string      `json:"NameMobile"`
	NameMobileEn          string      `json:"NameMobileEn"`
	CSSStyle              string      `json:"CssStyle"`
	Padding               string      `json:"Padding"`
	ParentReportNormID    int         `json:"ParentReportNormID"`
	ReportComponentName   string      `json:"ReportComponentName"`
	ReportComponentNameEn string      `json:"ReportComponentNameEn"`
	Unit                  interface{} `json:"Unit"`
	UnitEn                interface{} `json:"UnitEn"`
	OrderType             interface{} `json:"OrderType"`
	OrderingComponent     interface{} `json:"OrderingComponent"`
	RowNumber             interface{} `json:"RowNumber"`
	ReportComponentTypeID interface{} `json:"ReportComponentTypeID"`
	ChildTotal            int         `json:"ChildTotal"`
	Levels                int         `json:"Levels"`
	Value1                float64     `json:"Value1"`
	Value2                float64     `json:"Value2"`
	Value3                float64     `json:"Value3"`
	Value4                float64     `json:"Value4"`
	Vl                    interface{} `json:"Vl"`
	IsShowData            bool        `json:"IsShowData"`
}

type YearPeriodHeader struct {
	ID              int         `json:"ID"`
	Row             int         `json:"Row"`
	CompanyID       int         `json:"CompanyID"`
	YearPeriod      int         `json:"YearPeriod"`
	TermCode        string      `json:"TermCode"`
	TermName        string      `json:"TermName"`
	TermNameEN      string      `json:"TermNameEN"`
	ReportTermID    int         `json:"ReportTermID"`
	DisplayOrdering int         `json:"DisplayOrdering"`
	United          string      `json:"United"`
	AuditedStatus   string      `json:"AuditedStatus"`
	PeriodBegin     string      `json:"PeriodBegin"`
	PeriodEnd       string      `json:"PeriodEnd"`
	TotalRow        int         `json:"TotalRow"`
	BusinessType    int         `json:"BusinessType"`
	ReportNote      interface{} `json:"ReportNote"`
	ReportNoteEn    interface{} `json:"ReportNoteEn"`
}

type StockPrice struct {
	StockCode  string  `json:"StockCode"`
	ClosePrice float64 `json:"ClosePrice"`
	TotalVol   float64 `json:"TotalVol"`
	MarketCap  float64 `json:"MarketCap"`
	YEps       float64 `json:"Y_EPS"`
	YBvps      float64 `json:"Y_BVPS"`
	YPe        float64 `json:"Y_PE"`
	YPb        float64 `json:"Y_PB"`
	YRos       float64 `json:"Y_ROS"`
	YRoe       float64 `json:"Y_ROE"`
	YRoa       float64 `json:"Y_ROA"`
	Beta       float64 `json:"Beta"`
}

type StockTrade struct {
	StockCode       string  `json:"StockCode"`
	TradingDate     string  `json:"TradingDate"`
	Klcplh          int     `json:"KLCPLH"`
	Klcpny          int     `json:"KLCPNY"`
	PriorClosePrice int     `json:"PriorClosePrice"`
	CeilingPrice    int     `json:"CeilingPrice"`
	FloorPrice      int     `json:"FloorPrice"`
	TotalVol        int     `json:"TotalVol"`
	TotalVal        int     `json:"TotalVal"`
	MarketCapital   int64   `json:"MarketCapital"`
	HighestPrice    int     `json:"HighestPrice"`
	LowestPrice     int     `json:"LowestPrice"`
	OpenPrice       int     `json:"OpenPrice"`
	LastPrice       int     `json:"LastPrice"`
	AvrPrice        int     `json:"AvrPrice"`
	Change          int     `json:"Change"`
	PerChange       float64 `json:"PerChange"`
	Min52W          int     `json:"Min52W"`
	Max52W          int     `json:"Max52W"`
	Vol52W          int     `json:"Vol52W"`
	OutstandingBuy  int     `json:"OutstandingBuy"`
	OutstandingSell int     `json:"OutstandingSell"`
	OwnedRatio      float64 `json:"OwnedRatio"`
	Dividend        int     `json:"Dividend"`
	Yield           float64 `json:"Yield"`
	Beta            float64 `json:"Beta"`
	Eps             float64 `json:"EPS"`
	Pe              float64 `json:"PE"`
	Feps            float64 `json:"FEPS"`
	Bvps            float64 `json:"BVPS"`
	Pb              float64 `json:"PB"`
	TotalRoom       int     `json:"TotalRoom"`
	CurrRoom        int     `json:"CurrRoom"`
	RemainRoom      float64 `json:"RemainRoom"`
	FBuyVol         int     `json:"F_BuyVol"`
	FBuyVal         int     `json:"F_BuyVal"`
	FSellVol        int     `json:"F_SellVol"`
	FSellVal        int     `json:"F_SellVal"`
	FBuyPutVol      int     `json:"F_BuyPutVol"`
	FBuyPutVal      int     `json:"F_BuyPutVal"`
	FSellPutVol     int     `json:"F_SellPutVol"`
	FSellPutVal     int     `json:"F_SellPutVal"`
	MarketStatus    int     `json:"MarketStatus"`
	ColorID         int     `json:"ColorId"`
	StatusName      string  `json:"StatusName"`
	StockStatus     string  `json:"StockStatus"`
}

type StockEvents struct {
	EventID          int         `json:"EventID"`
	Code             string      `json:"Code"`
	CompanyName      string      `json:"CompanyName"`
	CatID            int         `json:"CatID"`
	GDKHQDate        string      `json:"GDKHQDate"`
	NDKCCDate        string      `json:"NDKCCDate"`
	Time             string      `json:"Time"`
	Note             string      `json:"Note"`
	Exchange         string      `json:"Exchange"`
	Title            string      `json:"Title"`
	Content          string      `json:"Content"`
	FileURL          string      `json:"FileUrl"`
	DateOrder        string      `json:"DateOrder"`
	RateTypeID       int         `json:"RateTypeID"`
	Rate             string      `json:"Rate"`
	VolumePublishing interface{} `json:"VolumePublishing"`
	Row              int         `json:"Row"`
}
