package model

import "database/sql"

type Chatbot struct {
	Intentions []*Intent   `yaml:"intentions" mapstructure:"intentions" json:"intentions"`
	Workflows  []*Workflow `yaml:"workflows" mapstructure:"workflows" json:"workflows"`
	UI         []*UI       `yaml:"ui" mapstructure:"ui" json:"ui"`
	Fsm        []*Fsm      `yaml:"fsm" mapstructure:"fsm" json:"fsm"`
}

type UI struct {
	Input  string   `yaml:"input"`
	Output []string `yaml:"output"`
}

type Intent struct {
	Intent      string `yaml:"intent" mapstructure:"intent" json:"intent"`
	Description string `yaml:"description" mapstructure:"description" json:"description"`
	Context     struct {
		URL        string                  `yaml:"url" mapstructure:"url" json:"url"`
		Method     string                  `yaml:"method" mapstructure:"method" json:"method"`
		ParamFixes *map[string]interface{} `yaml:"param_fixes,omitempty" mapstructure:"param_fixes,omitempty" json:"param_fixes,omitempty"`
		Params     *map[string]interface{} `yaml:"params,omitempty" mapstructure:"params,omitempty" json:"params,omitempty"`
		Response   *map[string]interface{} `yaml:"response,omitempty" mapstructure:"response,omitempty" json:"response,omitempty"`
	} `yaml:"context" mapstructure:"context" json:"context"`
}

type Fsm struct {
	Action    string   `yaml:"action" mapstructure:"action" json:"action"`
	Workflows []string `yaml:"workflows" mapstructure:"workflows" json:"workflows"`
}

type UserEntities struct {
	Value string
	Label string
}

type Workflow struct {
	Namespace   string   `yaml:"namespace" mapstructure:"namespace" json:"namespace"`
	Topic       string   `yaml:"topic" mapstructure:"topic" json:"topic"`
	Description string   `yaml:"description" mapstructure:"description" json:"description"`
	ErrorStop   bool     `yaml:"error_stop" mapstructure:"error_stop" json:"error_stop"`
	Keywords    []string `yaml:"keywords" mapstructure:"keywords" json:"keywords"`
	Intents     []string `yaml:"intents" mapstructure:"intents" json:"intents"`
	IsEntity    bool
}

type ChatObject struct {
	ID         string         `json:"chat_id"`
	ExternalID string         `json:"external_id"`
	Channel    string         `json:"channel"`
	State      sql.NullString `json:"state"`
	User       string         `json:"username"`
	FirstName  string         `json:"first_name"`
	LastName   string         `json:"last_name"`
	Owner      int            `json:"owner"`
	Scopes     []string       `json:"scopes"`
}

type ChatStateTransition struct {
	Namespace string                 `json:"namespace"`
	State     string                 `json:"state"`
	ChatID    string                 `json:"chat_id"`
	Swap      map[string]interface{} `json:"swap"`
}

type SuccessResponse struct {
	Message string `json:"message"`
}
