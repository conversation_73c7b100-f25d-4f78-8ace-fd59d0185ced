package model

// FaceBookSendMessageReqBody request body for sending a message to a user
type FaceBookSendMessageReqBody struct {
	RecipientID string `json:"id"`
	Message     string `json:"message"`
}

// FaceBookUserInfo represents basic user profile information
type FaceBookUserInfo struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}
