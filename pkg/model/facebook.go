package model

// FaceBookSendMessageReqBody request body for sending a message to a user
type FaceBookSendMessageReqBody struct {
	RecipientID string `json:"id"`
	Message     string `json:"message"`
}

// FaceBookSendButtonMessageReqBody request body for sending a button template message
type FaceBookSendButtonMessageReqBody struct {
	RecipientID string                  `json:"id"`
	Text        string                  `json:"text"`
	Buttons     []FaceBookMessageButton `json:"buttons"`
}

// FaceBookMessageButton represents a button in Facebook Messenger
type FaceBookMessageButton struct {
	Type  string `json:"type"`
	URL   string `json:"url,omitempty"`
	Title string `json:"title"`
}

// FaceBookUserInfo represents basic user profile information
type FaceBookUserInfo struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}
