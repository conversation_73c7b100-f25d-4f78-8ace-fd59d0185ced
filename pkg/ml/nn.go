package ml

import (
	"math"
	"math/rand"

	"gonum.org/v1/gonum/mat"
)

type NeuralLayer struct {
	Units int
}

type NeuralNetwork struct {
	Weights []mat.Matrix
}

func NewNN(numLabels int, numFeats int, layers []int) *NeuralNetwork {
	var weights []mat.Matrix
	weights = append(weights, randInitializeWeights(numFeats, layers[0]))
	for i := 0; i < len(layers)-1; i++ {
		// First layer
		weights = append(weights, randInitializeWeights(layers[i], layers[i+1]))
	}
	weights = append(weights, randInitializeWeights(layers[len(layers)-1], numLabels))
	return &NeuralNetwork{Weights: weights}
}

func randInitializeWeights(Lin, Lout int) mat.Matrix {
	epsilonInit := math.Sqrt(6) / math.Sqrt(float64(Lin+Lout))
	W := mat.NewDense(Lout, 1+<PERSON>, nil)
	W.Apply(func(i, j int, v float64) float64 {
		return rand.Float64()*2*epsilonInit - epsilonInit
	}, W)
	return W
}

func AddBias(A mat.Matrix) mat.Matrix {
	r, c := A.Dims()
	Nc := c + 1
	ABias := mat.NewDense(r, Nc, nil)
	for i := 0; i < r; i++ {
		for j := 0; j < Nc; j++ {
			// Add bias
			if j == 0 {
				ABias.Set(i, j, 1)
			} else {
				ABias.Set(i, j, A.At(i, j-1))
			}
		}
	}
	return ABias
}

func RemoveBias(R mat.Matrix) mat.Matrix {
	r, c := R.Dims()
	Nc := c - 1
	RBias := mat.NewDense(r, Nc, nil)
	for i := 0; i < r; i++ {
		for j := 0; j < Nc; j++ {
			// Remove bias
			RBias.Set(i, j, R.At(i, j+1))
		}
	}
	return RBias
}

func (nn *NeuralNetwork) Predict(X mat.Matrix) ([]int, []float64) {
	a := AddBias(X)
	latestLayerIdx := len(nn.Weights) - 1
	for i := 0; i < latestLayerIdx; i++ {
		h := hypothesis(nn.Weights[i], a)
		a = AddBias(h)
	}
	hx := hypothesisSoftmax(nn.Weights[latestLayerIdx], a)
	rows, cols := hx.Dims()
	var maxIdxs []int
	var maxScores []float64
	for i := 0; i < rows; i++ {
		max := 0.0
		maxIdx := 0
		for j := 0; j < cols; j++ {
			if hx.At(i, j) > max {
				max = hx.At(i, j)
				maxIdx = j
			}
		}
		maxIdxs = append(maxIdxs, maxIdx)
		maxScores = append(maxScores, max)
	}
	return maxIdxs, maxScores
}

func (nn *NeuralNetwork) LossFunction(X mat.Matrix, Y mat.Matrix, lambda float64) (float64, []mat.Matrix) {
	m, _ := X.Dims()

	var zLayers []mat.Matrix
	var aLayers []mat.Matrix
	// Feedforward
	a := AddBias(X)
	aLayers = append(aLayers, a)
	latestLayerIdx := len(nn.Weights) - 1
	for i := 0; i < latestLayerIdx; i++ {
		z := new(mat.Dense)
		z.Mul(a, nn.Weights[i].T())
		zLayers = append(zLayers, z)
		a = AddBias(sigmoid(z))
		aLayers = append(aLayers, a)
	}
	HX := hypothesisSoftmax(nn.Weights[latestLayerIdx], a)

	// J = 1/m * sum(sum((-Y).*log(HX) - (1-Y).*log(1-HX)))
	// (-Y).*log(HX)
	logHX := log(HX)
	nY := new(mat.Dense)
	nY.Apply(func(i, j int, v float64) float64 {
		return -v
	}, Y)
	dotYlogH := new(mat.Dense)
	dotYlogH.MulElem(nY, logHX)
	// (1-Y).*log(1-HX)
	nHX := new(mat.Dense)
	nHX.Apply(func(i, j int, v float64) float64 {
		return 1 - v
	}, HX)
	lognH := log(nHX)

	oneNY := new(mat.Dense)
	oneNY.Apply(func(i, j int, v float64) float64 {
		return 1 - v
	}, Y)

	dotYnLognH := new(mat.Dense)
	dotYnLognH.MulElem(oneNY, lognH)

	JTheta := new(mat.Dense)
	JTheta.Sub(dotYlogH, dotYnLognH)
	sum := 0.0
	for _, v := range JTheta.RawMatrix().Data {
		sum += v
	}

	cost := sum / float64(m)

	// Sigma3 = HX - Y;
	// Sigma2 = (Sigma3*Theta2 .* sigmoidGradient([ones(size(Z2, 1), 1) Z2]));
	// Sigma2 = Sigma2(:, 2:end);

	// Delta1 = Sigma2'*A1;
	// Delta2 = Sigma3'*A2;

	// Theta1_grad = Delta1./m + (lambda/m)*[zeros(size(Theta1,1), 1) Theta1(:, 2:end)];
	// Theta2_grad = Delta2./m + (lambda/m)*[zeros(size(Theta2,1), 1) Theta2(:, 2:end)];

	// Backpropagation
	var deltas []mat.Matrix
	sigma := new(mat.Dense)
	sigma.Sub(HX, Y)
	latestDelta := new(mat.Dense)
	latestDelta.Mul(sigma.T(), aLayers[latestLayerIdx])
	deltas = append(deltas, latestDelta)

	for i := latestLayerIdx; i > 0; i-- {
		sigmaBias := new(mat.Dense)
		sigmaBias.Mul(sigma, nn.Weights[i])
		zBias := AddBias(zLayers[i-1])
		zBiasGrad := sigmoidGradient(zBias)
		sigmaBias.MulElem(sigmaBias, zBiasGrad)
		// Remove bias
		sigma = RemoveBias(sigmaBias).(*mat.Dense)
		// Calculate delta
		delta := new(mat.Dense)
		delta.Mul(sigma.T(), aLayers[i-1])
		deltas = append(deltas, delta)
	}
	// Reverse deltas
	for i, j := 0, len(deltas)-1; i < j; i, j = i+1, j-1 {
		deltas[i], deltas[j] = deltas[j], deltas[i]
	}

	var thetaGrads []mat.Matrix
	// Theta gradient
	for i, delta := range deltas {
		thetaZeros := new(mat.Dense)
		thetaZeros.CloneFrom(nn.Weights[i])
		rThetaZeros, _ := thetaZeros.Dims()
		for i := 0; i < rThetaZeros; i++ {
			thetaZeros.Set(i, 0, 0)
		}
		thetaLambda := new(mat.Dense)
		thetaLambda.Apply(func(i, j int, v float64) float64 {
			return lambda / float64(m) * v
		}, thetaZeros)
		thetaGrad := new(mat.Dense)
		thetaGrad.Scale(1.0/float64(m), delta)

		thetaGrad.Add(thetaGrad, thetaLambda)
		thetaGrads = append(thetaGrads, thetaGrad)
	}
	return cost, thetaGrads
}

func hypothesis(theta mat.Matrix, X mat.Matrix) mat.Matrix {
	hX := new(mat.Dense)
	hX.Mul(X, theta.T())
	return sigmoid(hX)
}

func hypothesisSoftmax(theta mat.Matrix, X mat.Matrix) mat.Matrix {
	hX := new(mat.Dense)
	hX.Mul(X, theta.T())
	return softmax(hX)
}

func log(M mat.Matrix) mat.Matrix {
	logM := new(mat.Dense)
	logM.Apply(func(i, j int, v float64) float64 {
		return math.Log(v)
	}, M)
	return logM
}

func sigmoid(z mat.Matrix) mat.Matrix {
	sigZ := new(mat.Dense)
	sigZ.Apply(func(i, j int, v float64) float64 {
		return 1 / (1 + math.Exp(-v))
	}, z)
	return sigZ
}

func softmax(z mat.Matrix) mat.Matrix {
	_, cols := z.Dims()
	smZ := new(mat.Dense)
	smZ.Apply(func(i, j int, v float64) float64 {
		// Sum
		sum := 0.0
		for k := 0; k < cols; k++ {
			sum += math.Exp(z.At(i, k))
		}
		return math.Exp(v) / sum
	}, z)
	return smZ
}

func sigmoidGradient(z mat.Matrix) mat.Matrix {
	g := new(mat.Dense)
	sigZ := sigmoid(z)
	nSigZ := new(mat.Dense)
	nSigZ.Apply(func(i, j int, v float64) float64 {
		return 1 - v
	}, sigZ)
	g.MulElem(sigZ, nSigZ)
	return g
}
