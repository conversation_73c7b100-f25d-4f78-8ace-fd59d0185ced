package util

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gonum.org/v1/gonum/mat"
)

func TestRemoveIndex(t *testing.T) {
	slice := []float64{1.0, 2.0, 3.0, 4.0}
	index := 2

	result := RemoveIndex(slice, index)
	expected := []float64{1.0, 2.0, 4.0}
	assert.Equal(t, expected, result)
}

func TestRemoveCol(t *testing.T) {
	X := mat.NewDense(3, 3, []float64{1, 2, 3, 4, 5, 6, 7, 8, 9})
	colIndex := 1

	result := RemoveCol(colIndex, X)
	expected := mat.NewDense(3, 2, []float64{1, 3, 4, 6, 7, 9})
	assert.Equal(t, expected, result)
}

func TestRegressionWeight(t *testing.T) {
	X := mat.NewDense(3, 2, []float64{1, 2, 3, 4, 5, 6})
	Y := mat.NewDense(3, 1, []float64{1, 2, 3})

	weights := RegressionWeight(X, Y)
	expected := []float64{0.5, 0.5} // Example expected weights
	assert.Equal(t, expected, weights)
}

func TestStandardizedWeightVector(t *testing.T) {
	X := mat.NewDense(3, 2, []float64{1, 2, 3, 4, 5, 6})
	Y := mat.NewDense(3, 1, []float64{1, 2, 3})
	weights := []float64{0.5, 0.5}

	standardizedWeights := StandardizedWeightVector(X, Y, weights)
	expected := []float64{0.5, 0.5} // Example expected standardized weights
	assert.Equal(t, expected, standardizedWeights)
}

func TestPropotionWeightVector(t *testing.T) {
	weights := []float64{0.5, 0.5}
	proportion := 0.5

	result := PropotionWeightVector(weights, proportion)
	expected := []float64{0.25, 0.25}
	assert.Equal(t, expected, result)
}

func TestCalCovariance(t *testing.T) {
	X := mat.NewDense(3, 2, []float64{1, 2, 3, 4, 5, 6})
	W := mat.NewDense(2, 1, []float64{0.5, 0.5})

	covariance := CalCovariance(X, W)
	expected := 1.0 // Example expected covariance
	assert.Equal(t, expected, covariance)
}

func TestCalSlopeSigma(t *testing.T) {
	revs := []float64{1.0, 2.0, 3.0, 4.0, 5.0}
	calSlope := true

	mean, sd, zScore := CalSlopeSigma(revs, calSlope)
	expectedMean := 3.0   // Example expected mean
	expectedSD := 1.0     // Example expected standard deviation
	expectedZScore := 0.0 // Example expected z-score
	assert.Equal(t, expectedMean, mean)
	assert.Equal(t, expectedSD, sd)
	assert.Equal(t, expectedZScore, zScore)
}

func TestCountWorkingDay(t *testing.T) {
	fromDate := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
	toDate := time.Date(2023, 1, 31, 0, 0, 0, 0, time.UTC)

	workingDays := CountWorkingDay(fromDate, toDate)
	expected := []time.Time{fromDate, toDate} // Example expected working days
	assert.Equal(t, expected, workingDays)
}
