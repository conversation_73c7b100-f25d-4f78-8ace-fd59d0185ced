package util

import (
	"reflect"
	"strings"

	"github.com/007lock/simon-homestead/internal/contract"

	"github.com/go-playground/validator/v10"
)

func NewValidator() contract.Validator {
	validate := validator.New()
	customValidate := &CustomValidator{validator: validate}
	// validate.RegisterValidation("unique", customValidate.validateUniqueField)
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		return strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
	})
	return customValidate
}

type CustomValidator struct {
	validator *validator.Validate
}

func (cv *CustomValidator) Validate(i interface{}) error {
	return cv.validator.Struct(i)
}

// func (cv *CustomValidator) validateUniqueField(fl validator.FieldLevel) bool {
// 	txi, err := cv.db.Begin()
// 	if err != nil {
// 		return false
// 	}
// 	tx := txi.(*gorm.DB)
// 	defer tx.Commit()
// 	type Result struct {
// 		Total int
// 	}
// 	var result Result
// 	err = tx.Exec(fmt.Sprintf("SELECT COUNT(*) total FROM %s WHERE %s=?", fl.Param(), fl.FieldName()), fl.Field().String()).
// 		Scan(&result).Error
// 	if err != nil && gorm.IsRecordNotFoundError(err) {
// 		return true
// 	}
// 	if result.Total > 0 {
// 		return true
// 	}

// 	return false
// }
