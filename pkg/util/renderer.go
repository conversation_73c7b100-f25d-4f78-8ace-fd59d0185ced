package util

import (
	"io"
	"net/http"
	"text/template"

	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
)

type Error struct {
	Code int         `json:"code"`
	Msg  interface{} `json:"message"`
}

type ErrorResponse struct {
	ErrorInfo Error `json:"error"`
}

type Template struct {
	Templates *template.Template
}

func NewTemplateRenderer(t *template.Template) contract.Renderer {
	return &Template{t}
}

func (t *Template) Render(w io.Writer, name string, data interface{}, c echo.Context) error {
	return t.Templates.ExecuteTemplate(w, name, data)
}

// Error handle here
func (t *Template) CustomHTTPErrorHandler(err error, c echo.Context) {
	if he, ok := err.(*echo.HTTPError); ok {
		if valErrs, ok := he.Message.(validator.ValidationErrors); ok {
			msg := convertValidationErrors(valErrs)
			c.J<PERSON>(he.Code, ErrorResponse{
				ErrorInfo: Error{
					Code: he.Code,
					Msg:  msg,
				},
			})
			return
		}
		// Extract error from message
		if errMsg, ok := he.Message.(error); ok {
			c.JSON(he.Code, ErrorResponse{
				ErrorInfo: Error{
					Code: he.Code,
					Msg:  errMsg.Error(),
				},
			})
			return
		}
	}
	// Unexxception error
	c.JSON(http.StatusInternalServerError, ErrorResponse{
		ErrorInfo: Error{
			Code: http.StatusInternalServerError,
			Msg:  err.Error(),
		},
	})
	// c.Logger().Error("Request error", err)
}

func convertValidationErrors(vErr validator.ValidationErrors) interface{} {
	msg := map[string]interface{}{}
	for _, er := range vErr {
		msg[er.Field()] = er.ActualTag()
	}
	return msg
}
