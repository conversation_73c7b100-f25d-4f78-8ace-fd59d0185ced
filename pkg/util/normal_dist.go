package util

import (
	"context"
	"database/sql"
	"fmt"
	"math"
	"time"

	"github.com/gosimple/slug"
	"gonum.org/v1/gonum/mat"
	"gonum.org/v1/gonum/stat"
	"gonum.org/v1/plot"
	"gonum.org/v1/plot/plotter"
	"gonum.org/v1/plot/plotutil"
	"gonum.org/v1/plot/vg"
)

func AccumulatePriceXSampling(db *sql.DB, dbPrice *sql.DB, assets []string, from time.Time, to time.Time) (mat.Matrix, error) {
	workdayRange := CountWorkingDay(from, to)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	// X dimensions are n*m matrix
	// n is the number of days
	// m is the number of assets
	X := mat.NewDense(len(workdayRange), len(assets), nil)
	// Data transformation
	for i, asset := range assets {
		// Find missing days
		r := dbPrice.QueryRowContext(ctx, "SELECT `date` FROM `"+asset+".csv` ORDER BY date DESC LIMIT 1")
		var latestDate int64
		if err := r.Scan(&latestDate); err != nil {
			return nil, err
		}

		stmt, err := dbPrice.PrepareContext(ctx, "SELECT `close`, `date` FROM `"+asset+".csv` WHERE date >= ? AND date <= ? ORDER BY date ASC")
		if err != nil {
			return nil, err
		}
		rs, err := stmt.QueryContext(ctx, from.Unix(), to.Unix())
		if err != nil {
			return nil, err
		}
		defer rs.Close()
		closeTradingDays := make(map[string]float64)
		for rs.Next() {
			var (
				close     float64
				timeFrame int64
			)
			if err = rs.Scan(&close, &timeFrame); err != nil {
				return nil, err
			}
			tradingDay := time.Unix(timeFrame, 0)
			closeTradingDays[tradingDay.Format("20060102")] = close
		}
		// Add to matrix
		for j, tradingDay := range workdayRange {
			closePrice, ok := closeTradingDays[tradingDay.Format("20060102")]
			if !ok {
				// set price nearest workday
				for i := j; i < len(workdayRange); i++ {
					price := closeTradingDays[workdayRange[i].Format("20060102")]
					if price > 0 {
						closePrice = price
						break
					}
				}
				if closePrice == 0 {
					for i := j; i > 0; i-- {
						price := closeTradingDays[workdayRange[i].Format("20060102")]
						if price > 0 {
							closePrice = price
							break
						}
					}
				}
			}
			X.Set(j, i, closePrice)
		}
	}

	// fx := mat.Formatted(X, mat.Prefix("    "), mat.Squeeze())
	// fmt.Printf("X = %v\n", fx)

	// Calculate percentage change
	X.Apply(func(i, j int, v float64) float64 {
		if i == 0 {
			return v
		}
		return v/X.At(i-1, j) - 1
	}, X)
	// Remove zeros rows
	// fmt.Println(X.Dims())
SLICEMATRIX:
	for i := 0; i < X.RawMatrix().Rows; i++ {
		if X.At(i, 0) != 0 {
			continue
		}
		colZeros := 1
		for j := 1; j < X.RawMatrix().Cols; j++ {
			if X.At(i, j) == 0 {
				colZeros++
			}
		}

		if colZeros == X.RawMatrix().Cols {
			if i == 0 {
				// fx := mat.Formatted(X, mat.Prefix("    "), mat.Squeeze())
				X = X.Slice(1, X.RawMatrix().Rows, 0, X.RawMatrix().Cols).(*mat.Dense)
				// fmt.Println("=====Slide first======")
				// fmt.Println(X.Dims())
			} else if i == X.RawMatrix().Rows-1 {
				// Last row is all zeros
				X = X.Slice(0, X.RawMatrix().Rows-1, 0, X.RawMatrix().Cols).(*mat.Dense)
				// fmt.Println("=====Slide last======")
				// fmt.Println(X.Dims())
			} else {
				// Middle row is all zeros
				XFirstHaft := X.Slice(0, i, 0, X.RawMatrix().Cols).(*mat.Dense)
				XSecondHaft := X.Slice(i+1, X.RawMatrix().Rows, 0, X.RawMatrix().Cols).(*mat.Dense)
				// fmt.Println("=====Slide middle======")
				// fmt.Println(X.Dims())
				// fmt.Println(XFirstHaft.Dims())
				// fmt.Println(XSecondHaft.Dims())
				XStack := new(mat.Dense)
				XStack.Stack(XFirstHaft, XSecondHaft)
				X = XStack
			}
			// Change structure we have to reset iterator
			if i < X.RawMatrix().Rows {
				goto SLICEMATRIX
			}
		}
	}
	X = X.Slice(1, X.RawMatrix().Rows, 0, X.RawMatrix().Cols).(*mat.Dense)
	// fx := mat.Formatted(X, mat.Prefix("    "), mat.Squeeze())
	// fmt.Printf("X = %v\n", fx)
	// Todo: Accumulate the percentage change day by day
	for i := 1; i < X.RawMatrix().Rows; i++ {
		for j := 0; j < X.RawMatrix().Cols; j++ {
			X.Set(i, j, X.At(i, j)+X.At(i-1, j))
		}
	}
	return X, nil
}

func RemoveIndex(slice []float64, s int) []float64 {
	return append(slice[:s], slice[s+1:]...)
}

func HistogramPlot(title string, values plotter.Values, bins int) error {
	p := plot.New()
	p.Title.Text = title

	hist, err := plotter.NewHist(values, bins)
	if err != nil {
		return err
	}
	hist.FillColor = plotutil.Color(2)
	p.Add(hist)
	p.X.Min = -float64(bins)
	p.X.Max = float64(bins)

	slugName := fmt.Sprintf("%s.png", slug.Make(title))
	if err := p.Save(3*vg.Inch, 3*vg.Inch, slugName); err != nil {
		return err
	}
	return nil
}

func RemoveCol(s int, X mat.Matrix) mat.Matrix {
	r, c := X.Dims()
	Xdense := mat.DenseCopyOf(X)
	// Slice index
	if s == 0 {
		// First cols
		Xdense = Xdense.Slice(0, r, 1, c).(*mat.Dense)
	} else if s == c-1 {
		// Last cols
		Xdense = Xdense.Slice(0, r, 0, c-1).(*mat.Dense)
	} else {
		// Middle cols
		XFirstHaft := Xdense.Slice(0, r, 0, s).(*mat.Dense)
		XSecondHaft := Xdense.Slice(0, r, s+1, c).(*mat.Dense)
		XStack := new(mat.Dense)
		XStack.Augment(XFirstHaft, XSecondHaft)
		Xdense = XStack
	}
	return Xdense
}

func RegressionWeight(X, Y mat.Matrix) []float64 {
	_, n := X.Dims()
	// Normal equation using QR decomposition
	// https://medium.com/@vasanth260m12/solving-linear-regression-using-linear-algebra-in-golang-b0d66b7056ff

	// initializing a QR matrix
	qr := new(mat.QR)

	// decomposing the X matrix
	qr.Factorize(X)

	// accessing the Q and R matrices
	q := new(mat.Dense)
	qr.QTo(q)
	r := new(mat.Dense)
	qr.RTo(r)

	// transposing Q matrix
	qt := q.T()

	// calculating Q.T*y and storing it in qty
	qty := new(mat.Dense)
	qty.Mul(qt, Y)

	// initializing b to store constants
	W := mat.NewVecDense(n, nil)

	// using back-substitution calculating all constants
	for i := n - 1; i >= 0; i-- {
		W.SetVec(i, qty.At(i, 0))
		for j := i + 1; j < n; j++ {
			W.SetVec(i, W.AtVec(i)-r.At(i, j)*W.AtVec(j))
		}
		W.SetVec(i, W.AtVec(i)/r.At(i, i))
	}
	var weight []float64
	weight = append(weight, W.RawVector().Data...)
	return weight
}

func StandardizedWeightVector(X, Y mat.Matrix, weights []float64) []float64 {
	var newWeights []float64
	// Standardized Regression Coefficients to interpret the coefficients
	// b(i) = b(i) * stddev(X(i)) / stddev(Y)
	// https://en.wikipedia.org/wiki/Standardized_coefficient
	stddevY := stat.StdDev(mat.Col(nil, 0, Y), nil)
	for i := 0; i < len(weights); i++ {
		stddevXi := stat.StdDev(mat.Col(nil, i, X), nil)
		newWeights = append(newWeights, weights[i]*stddevXi/stddevY)
	}

	return newWeights
}

func PropotionWeightVector(weights []float64, proportion float64) []float64 {
	var newWeights []float64
	n := len(weights)
	total := 0.0
	for _, v := range weights {
		total += v
	}
	for i := 0; i < n; i++ {
		newWeights = append(newWeights, weights[i]*proportion/total)
	}
	return newWeights
}

func CalCovariance(X, W mat.Matrix) float64 {
	m, n := X.Dims()
	if n > 1 {
		// Calculate covariance matrix
		// Σ = (X.T * X) / (n - 1)
		var Cov mat.Dense
		Cov.Mul(X.T(), X)
		Cov.Scale(1/float64(n-1), &Cov)
		// Calculate portfolio variance
		// SQRT (W.T * Σ * W)
		var WX mat.Dense
		WX.Mul(W.T(), &Cov)
		var Var mat.Dense
		Var.Mul(&WX, W)
		Var.Apply(func(i, j int, v float64) float64 { return math.Sqrt(v) }, &Var)
		return Var.At(0, 0)
	}
	// Single sample calculate standard deviation
	var samples []float64
	for i := 0; i < m; i++ {
		samples = append(samples, X.At(i, 0))
	}
	_, sd, _ := CalSlopeSigma(samples, false)
	return sd
}

func CalSlopeSigma(revs []float64, calSlope bool) (mean float64, sd float64, zScore float64) {
	// Calculate slopes
	var revSlopes []float64
	if calSlope {
		for i, rev := range revs {
			if i+1 < len(revs) {
				denominator := rev
				if denominator == 0 {
					denominator = 1
				}
				revSlopes = append(revSlopes, (revs[i+1]-rev)/denominator)
			}
		}
	} else {
		revSlopes = revs
	}

	lnSlopes := len(revSlopes)
	if lnSlopes == 0 {
		return 0, 0, 0
	}
	// Calculate mean
	for _, slope := range revSlopes {
		mean += slope
	}
	mean = mean / float64(lnSlopes)
	// Calculate sd
	for _, slope := range revSlopes {
		sd += math.Pow(slope-mean, 2)
	}
	sd = math.Sqrt(sd / float64(lnSlopes))
	if sd != 0 {
		zScore = (revSlopes[lnSlopes-1] - mean) / sd
	}

	return mean, sd, zScore
}

func CountWorkingDay(fromDate time.Time, toDate time.Time) []time.Time {
	// Calculate working days
	workdayRange := make([]time.Time, 0)
	for fromDate.Before(toDate) {
		if fromDate.Weekday() != time.Saturday && fromDate.Weekday() != time.Sunday {
			workdayRange = append(workdayRange, fromDate)
		}
		fromDate = fromDate.AddDate(0, 0, 1)
	}
	return workdayRange
}
