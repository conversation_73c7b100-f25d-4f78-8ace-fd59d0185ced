package util

import (
	"testing"
	"time"
	"github.com/stretchr/testify/assert"
)

func TestDiffMonths(t *testing.T) {
	tests := []struct {
		now  time.Time
		then time.Time
		expected int
	}{
		{time.Date(2023, 12, 1, 0, 0, 0, 0, time.UTC), time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC), 11},
		{time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC), time.Date(2022, 12, 1, 0, 0, 0, 0, time.UTC), 1},
		{time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC), time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC), 24},
	}

	for _, tt := range tests {
		t.Run("DiffMonths", func(t *testing.T) {
			result := DiffMonths(tt.now, tt.then)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestMonthsTillEndOfYear(t *testing.T) {
	tests := []struct {
		then     time.Time
		expected int
	}{
		{time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC), 11},
		{time.Date(2023, 6, 1, 0, 0, 0, 0, time.UTC), 6},
		{time.Date(2023, 12, 1, 0, 0, 0, 0, time.UTC), 0},
	}

	for _, tt := range tests {
		t.Run("MonthsTillEndOfYear", func(t *testing.T) {
			result := monthsTillEndOfYear(tt.then)
			assert.Equal(t, tt.expected, result)
		})
	}
}
