package util

import (
	"bytes"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"text/template"

	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/test"
	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
)

func TestRenderTemplate(t *testing.T) {
	// Setup
	e := echo.New()

	// Template
	cfg, err := test.CreateConfig()
	if err != nil {
		t.Fatal(err)
	}
	temp := template.Must(template.ParseGlob(cfg.TemplatePath.EmailPath))
	tpl := &Template{
		Templates: temp,
	}

	e.Renderer = tpl
	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()
	c := e.New<PERSON>ontext(req, rec)

	// Assertions
	if assert.NoError(t, renderTemplate(c)) {
		assert.Equal(t, http.StatusOK, rec.Code)
	}
}

func renderTemplate(c echo.Context) error {
	return c.Render(http.StatusOK, "email-receipt", map[string]interface{}{
		"title": "Homestead",
	})
}

func TestTemplate_Render(t *testing.T) {
	cfg, err := test.CreateConfig()
	if err != nil {
		t.Fatal(err)
	}
	temp := template.Must(template.ParseGlob(cfg.TemplatePath.EmailPath))
	type fields struct {
		Templates *template.Template
	}
	type args struct {
		name string
		data interface{}
		c    echo.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "Template_Render Success",
			fields: fields{
				Templates: temp,
			},
			args: args{
				name: "email-receipt",
				data: map[string]interface{}{
					"title": cfg.AppName,
				},
				c: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tempTest := &Template{
				Templates: tt.fields.Templates,
			}
			w := &bytes.Buffer{}
			if err := tempTest.Render(w, tt.args.name, tt.args.data, tt.args.c); (err != nil) != tt.wantErr {
				t.Errorf("Template.Render() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestTemplate_CustomHTTPErrorHandler(t *testing.T) {
	c, err := test.CreateContext()
	if err != nil {
		t.Fatal(err)
	}

	type fields struct {
		Templates *template.Template
	}
	type args struct {
		err error
		c   echo.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "Template_CustomHTTPErrorHandler Success map[string]interface{}",
			fields: fields{
				Templates: nil,
			},
			args: args{
				err: echo.ErrNotFound,
				c:   c,
			},
		},
		{
			name: "Template_CustomHTTPErrorHandler Success ValidationErrors",
			fields: fields{
				Templates: nil,
			},
			args: args{
				err: echo.NewHTTPError(http.StatusInternalServerError, validator.ValidationErrors{}),
				c:   c,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tempTest := &Template{
				Templates: tt.fields.Templates,
			}
			tempTest.CustomHTTPErrorHandler(tt.args.err, tt.args.c)
		})
	}
}

func TestNewTemplateRenderer(t *testing.T) {
	type args struct {
		t *template.Template
	}
	tests := []struct {
		name string
		args args
		want contract.Renderer
	}{
		{
			name: "NewTemplateRenderer Success",
			args: args{nil},
			want: &Template{nil},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewTemplateRenderer(tt.args.t); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewTemplateRenderer() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertValidationErrors(t *testing.T) {
	type args struct {
		vErr validator.ValidationErrors
	}
	tests := []struct {
		name string
		args args
		want interface{}
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := convertValidationErrors(tt.args.vErr); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertValidationErrors() = %v, want %v", got, tt.want)
			}
		})
	}
}
