package util

import (
	"fmt"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/golang-jwt/jwt"
)

type JWT struct {
	cfg *config.Config
}

func NewJWTService(cfg *config.Config) contract.JWT {
	return &JWT{cfg}
}

func (auth *JWT) Sign(userClaim *model.UserInfo) (string, error) {
	claims := model.HomesteadClaims{
		User: *userClaim,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(time.Hour * 24).Unix(),
			Issuer:    auth.cfg.AppName,
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(auth.cfg.JWT.Secret))
}
func (auth *JWT) Verify(token string) (*model.UserInfo, error) {
	decodedToken, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		// Don't forget to validate the alg is what you expect:
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// hmacSampleSecret is a []byte containing your secret, e.g. []byte("my_secret_key")
		return []byte(auth.cfg.JWT.Secret), nil
	})
	if err != nil {
		return nil, err
	}

	if claims, ok := decodedToken.Claims.(jwt.MapClaims); ok && decodedToken.Valid {
		if userClaims, ok := claims["user"]; ok {
			userMap := userClaims.(map[string]interface{})
			user := &model.UserInfo{
				ID:        userMap["chat_id"].(string),
				User:      userMap["username"].(string),
				FirstName: userMap["first_name"].(string),
				LastName:  userMap["last_name"].(string),
			}
			return user, nil
		}
	}
	return nil, constants.AuthenticationError.EXPIRED
}
