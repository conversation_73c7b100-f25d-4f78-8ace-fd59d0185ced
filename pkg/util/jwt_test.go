package util

import (
	"testing"

	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/stretchr/testify/assert"
)

func TestSign(t *testing.T) {
	cfg := &config.Config{
		JWT:     &config.JWT{Secret: "test_secret"},
		AppName: "TestApp",
	}
	jwtService := NewJWTService(cfg)

	userClaim := &model.UserInfo{ID: "12345"}
	token, err := jwtService.Sign(userClaim)
	assert.NoError(t, err)
	assert.NotEmpty(t, token)
}

func TestVerify(t *testing.T) {
	cfg := &config.Config{
		JWT:     &config.JWT{Secret: "test_secret"},
		AppName: "TestApp",
	}
	jwtService := NewJWTService(cfg)

	userClaim := &model.UserInfo{ID: "12345"}
	token, _ := jwtService.Sign(userClaim)

	parsedClaim, err := jwtService.Verify(token)
	assert.NoError(t, err)
	assert.Equal(t, userClaim.ID, parsedClaim.ID)
}
