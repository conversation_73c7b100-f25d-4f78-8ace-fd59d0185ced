package route

import (
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/contract"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	apihttp "github.com/007lock/simon-homestead/pkg/api/http"
	"github.com/007lock/simon-homestead/pkg/service"

	midw "github.com/007lock/simon-homestead/pkg/middleware"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/labstack/gommon/log"
)

type GodServer struct {
	service *service.Service
}

// ZapLogger is an example of echo middleware that logs requests using logger "zap"
func ZapLogger(log *zap.Logger) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			start := time.Now()

			err := next(c)
			if err != nil {
				c.<PERSON>rror(err)
			}

			req := c.Request()
			res := c.Response()

			id := req.Header.Get(echo.HeaderXRequestID)
			if id == "" {
				id = res.Header().Get(echo.HeaderXRequestID)
			}

			fields := []zapcore.Field{
				zap.Int("status", res.Status),
				zap.String("latency", time.Since(start).String()),
				zap.String("id", id),
				zap.String("method", req.Method),
				zap.String("uri", req.RequestURI),
				zap.String("host", req.Host),
				zap.String("remote_ip", c.RealIP()),
			}

			n := res.Status
			switch {
			case n >= 500:
				log.Error("Server error", fields...)
			case n >= 400:
				log.Warn("Client error", fields...)
			case n >= 300:
				log.Info("Redirection", fields...)
			default:
				log.Info("Success", fields...)
			}

			return nil
		}
	}
}

func urlSkipper(c echo.Context) bool {
	if strings.HasPrefix(c.Path(), "/health") {
		return true
	}
	if strings.HasPrefix(c.Path(), "/metrics") {
		return true
	}
	if strings.HasPrefix(c.Path(), "/check-time") {
		return true
	}

	return false
}

// Init echo
func Init(s *service.Service) contract.WebServer {
	// Echo instance
	e := s.Echo
	e.Use(ZapLogger(s.Logger))
	p := midw.NewPrometheus(s.Config.AppName, s.Config.Environment, "echo", urlSkipper)
	p.Use(e)

	// Add services to echo
	e.Validator = s.Validator
	e.Renderer = s.Template
	e.HTTPErrorHandler = s.Template.CustomHTTPErrorHandler

	// Middleware
	e.Use(middleware.LoggerWithConfig(middleware.LoggerConfig{
		Skipper: urlSkipper,
	}))

	if s.Config.Environment == "production" {
		e.Logger.SetLevel(log.WARN)
	}

	e.Use(middleware.Recover())
	e.Use(middleware.Gzip())
	// Fetch new store.
	e.Use(midw.TransactionHandler(s.DB))

	//CORS
	e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins: []string{"*"},
		AllowMethods: []string{echo.GET, echo.HEAD, echo.PUT, echo.PATCH, echo.POST, echo.DELETE},
	}))

	// Routes
	router := NewRouter(s, e)
	NewRouteBot(s, router)
	NewRoutePublic(s, router, midw.JWTHandler(s))

	return &GodServer{
		service: s,
	}
}

func (s *GodServer) Serve() error {

	cfg := s.service.Config
	e := s.service.Echo

	// if cfg.Environment == "development" {
	// Start server
	// return e.StartTLS(":443", "ssl/server.crt", "ssl/server.key")
	// }
	if cfg.Environment == "testing" {
		return nil
	}
	// Start server
	return e.Start(":" + cfg.HTTPPort)
}

func NewRouter(s *service.Service, router *echo.Echo, middwares ...echo.MiddlewareFunc) *echo.Group {
	router.GET("/health", apihttp.Health(s))
	return router.Group("/v1", middwares...)
}
