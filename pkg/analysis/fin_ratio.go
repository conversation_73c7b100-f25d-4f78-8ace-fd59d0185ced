package analysis

import (
	"context"
	"database/sql"
	"fmt"
	"math"
	"strconv"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/util"

	"go.uber.org/zap"
)

type Processor struct {
	logger    *zap.Logger
	db        *sql.DB
	dbSSIFin  *sql.DB
	dbCafeFin *sql.DB
}

func NewAnalysis(logger *zap.Logger, db *sql.DB, dbSSIFin *sql.DB, dbCafeFin *sql.DB) *Processor {
	return &Processor{
		logger:    logger,
		db:        db,
		dbSSIFin:  dbSSIFin,
		dbCafeFin: dbCafeFin,
	}
}

func (a *Processor) CalculateFinancialRatioOfStock(stock *model.Stock) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	// Calculate ratio
	if stock.YearFields.Valid && stock.YearFields.String != "" {
		a.logger.Info("Calculating", zap.String("stock", stock.ID), zap.String("source", stock.DataSource))
		dbFin := a.dbCafeFin
		finFields := constants.CafeFFields
		switch stock.DataSource {
		case constants.Datasources.SSI:
			dbFin = a.dbSSIFin
			finFields = constants.SSIFields
		}

		stock, err := a.calProfitRevenueRatio(ctx, dbFin, finFields, stock)
		if err != nil {
			a.logger.Warn("Failed to calculate financial ratio", zap.Error(err))
			return nil
		}

		err = a.updateRevenueRatio(ctx, stock)
		if err != nil {
			return err
		}
	}
	return nil
}

func (a *Processor) calProfitRevenueRatio(ctx context.Context, db *sql.DB, faFields constants.FAFIELDS, stock *model.Stock) (*model.Stock, error) {
	stmt, err := db.PrepareContext(ctx, fmt.Sprintf("SELECT id, %s FROM `%s.csv` WHERE id = ? OR id = ? OR id = ? OR id = ? OR id = ? OR id = ? OR id = ? OR id = ? OR id = ? OR id = ? OR id = ? OR id = ? OR id = ? OR id = ? OR id = ? OR id = ?", stock.YearFields.String, stock.ID))
	if err != nil {
		return nil, err
	}
	var rs *sql.Rows
	rs, err = stmt.QueryContext(ctx,
		faFields.Revenue,
		faFields.GrossProfit,
		faFields.InterestExpenses,
		faFields.AdministratorExpenses,
		faFields.SaleExpenses,
		faFields.NetIncomes,
		faFields.OwnerEquities,
		faFields.Cash,
		faFields.ShortTermInvestment,
		faFields.Ebit,
		faFields.Liabilities,
		faFields.CurrentLiabilities,
		faFields.LongLiabilities,
		faFields.PreferredDivs,
		faFields.CommonShares,
		faFields.PreferredEquities)
	if err != nil {
		return nil, err
	}
	defer func(rs *sql.Rows) {
		err := rs.Close()
		if err != nil {
			a.logger.Warn("Failed to close rows in calculateFinancialRatio", zap.Error(err))
		}
	}(rs)

	var cols []string
	cols, err = rs.Columns()
	if err != nil {
		return nil, err
	}
	pointers := make([]interface{}, len(cols))
	container := make([]string, len(cols))
	for i := range pointers {
		pointers[i] = &container[i]
	}

	type financialReport struct {
		revenues                []float64
		grossProfits            []float64
		interestExpenses        []float64
		adminExpenses           []float64
		saleExpenses            []float64
		netIncomes              []float64
		ownerEquities           []float64
		cashes                  []float64
		ebits                   []float64
		liabilities             []float64
		currentLiabilities      []float64
		longLiabilities         []float64
		preferredDivs           []float64
		commonSharesOutstanding []float64
		prefferedEquities       []float64
	}
	fin := new(financialReport)
	for rs.Next() {
		if err := rs.Scan(pointers...); err != nil {
			return nil, err
		}

		if container[0] == faFields.Revenue {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.revenues = append(fin.revenues, math.Abs(rev))
				}
			}
		}
		if container[0] == faFields.GrossProfit {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.grossProfits = append(fin.grossProfits, math.Abs(rev))
				}
			}
		}
		if container[0] == faFields.InterestExpenses {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.interestExpenses = append(fin.interestExpenses, math.Abs(rev))
				}
			}
		}
		if container[0] == faFields.AdministratorExpenses {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.adminExpenses = append(fin.adminExpenses, math.Abs(rev))
				}
			}
		}
		if container[0] == faFields.SaleExpenses {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.saleExpenses = append(fin.saleExpenses, math.Abs(rev))
				}
			}
		}
		if container[0] == faFields.NetIncomes {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.netIncomes = append(fin.netIncomes, math.Abs(rev))
				}
			}
		}
		if container[0] == faFields.OwnerEquities {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.ownerEquities = append(fin.ownerEquities, math.Abs(rev))
				}
			}
		}

		// Cash section
		if container[0] == faFields.Cash {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.cashes = append(fin.cashes, math.Abs(rev))
				}
			}
		}

		if container[0] == faFields.ShortTermInvestment {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.cashes[i-1] += math.Abs(rev)
				}
			}
		}

		// Ebit
		if container[0] == faFields.Ebit {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.ebits = append(fin.ebits, math.Abs(rev))
				}
			}
		}

		if container[0] == faFields.Liabilities {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.liabilities = append(fin.liabilities, math.Abs(rev))
				}
			}
		}

		if container[0] == faFields.CurrentLiabilities {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.currentLiabilities = append(fin.currentLiabilities, math.Abs(rev))
				}
			}
		}

		if container[0] == faFields.LongLiabilities {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.longLiabilities = append(fin.longLiabilities, math.Abs(rev))
				}
			}
		}
		// Preferred Dividends
		if container[0] == faFields.PreferredDivs {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.preferredDivs = append(fin.preferredDivs, math.Abs(rev))
				}
			}
		}
		// Common outstanding shares
		if container[0] == faFields.CommonShares {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					// Divide value of vietnamese stock is 10000
					fin.commonSharesOutstanding = append(fin.commonSharesOutstanding, math.Abs(rev)/10000)
				}
			}
		}
		// Intangible fixed assets
		if container[0] == faFields.PreferredEquities {
			for i := 1; i < len(container); i++ {
				if rev, err := strconv.ParseFloat(container[i], 64); err == nil {
					fin.prefferedEquities = append(fin.prefferedEquities, math.Abs(rev))
				}
			}
		}
	}

	grade := 0

	// Comparing revenue ratio
	revComparedYoY := 0.0
	if len(fin.revenues) > 1 {
		revComparedYoY = fin.revenues[len(fin.revenues)-1]/fin.revenues[len(fin.revenues)-2] - 1
	}

	// Gross Profit/Revenue ratio
	grRatio := 0.0
	revSum := 0.0
	countRevs := 0.0
	for i, rev := range fin.revenues {
		if rev != 0 && fin.grossProfits[i] != 0 {
			grRatio += fin.grossProfits[i] / rev
			countRevs++
		}
		revSum += rev
	}
	grRatio /= countRevs
	if grRatio >= 0.2 {
		grade++
	}
	// Interest/Gross profit ratio
	igRatio := 0.0
	countProfit := 0.0
	if len(fin.interestExpenses) > 0 {
		for i, profit := range fin.grossProfits {
			if profit != 0 {
				igRatio += fin.interestExpenses[i] / profit
				countProfit++
			}
		}
	}

	igRatio /= countProfit
	if igRatio < 0.4 || igRatio == 0 {
		grade++
	}
	// Administration cost/Gross profit ratio
	agRatio := 0.0
	countProfit = 1.0
	if len(fin.adminExpenses) > 0 {
		for i, profit := range fin.grossProfits {
			if profit != 0 && fin.adminExpenses[i] != 0 {
				agRatio += fin.adminExpenses[i] / profit
				if i > 0 {
					countProfit++
				}
			}
		}
	}

	agRatio /= countProfit
	if agRatio <= 0.2 {
		grade++
	}
	cashRatio := 0.0
	// Cash / current term liability
	if fin.cashes != nil && len(fin.currentLiabilities) > 0 {
		currentLialibility := fin.currentLiabilities[len(fin.currentLiabilities)-1]
		if currentLialibility != 0 {
			cashRatio = fin.cashes[len(fin.cashes)-1] / currentLialibility
		}
	}
	if cashRatio > 1 || cashRatio == 0 {
		grade++
	}

	// EBIT / long term liability
	ebitRatio := 0.0
	if fin.longLiabilities != nil && fin.ebits != nil {
		longLiability := fin.longLiabilities[len(fin.longLiabilities)-1]
		if longLiability != 0 {
			ebitRatio = fin.ebits[len(fin.ebits)-1] / longLiability
		}
	}
	if ebitRatio > 4 || ebitRatio == 0 {
		grade++
	}

	// ROE
	grahamVal := 0.0
	roe := 0.0
	pe := 0.0
	eps := 0.0
	bvps := 0.0
	if fin.netIncomes != nil {
		latestIncome := fin.netIncomes[len(fin.netIncomes)-1]
		equityLen := len(fin.ownerEquities)
		if len(fin.ownerEquities) > 0 {
			latestAVGEquity := fin.ownerEquities[len(fin.ownerEquities)-1]
			if equityLen > 2 {
				latestAVGEquity = (fin.ownerEquities[len(fin.ownerEquities)-1] + fin.ownerEquities[len(fin.ownerEquities)-2]) / 2
			}

			roe = latestIncome / latestAVGEquity * 100
		}

		// EPS
		lenSharesIdx := len(fin.commonSharesOutstanding)
		if len(fin.commonSharesOutstanding) > 0 {
			totalShares := fin.commonSharesOutstanding[lenSharesIdx-1]
			// Bug both ssc & cafef did not fill common shares some time
			if totalShares == 0 {
				for i := lenSharesIdx - 1; i > 0; i-- {
					totalShares = fin.commonSharesOutstanding[i]
					if totalShares > 0 {
						break
					}
				}
			}
			// weighted average of shares
			latestPreferredDiv := 0.0
			if len(fin.preferredDivs) > 0 {
				latestPreferredDiv = fin.preferredDivs[len(fin.preferredDivs)-1]
			}
			eps = (latestIncome - latestPreferredDiv) / totalShares
			if len(fin.ownerEquities) > 0 && len(fin.prefferedEquities) > 0 && totalShares > 0 {
				bvps = (fin.ownerEquities[len(fin.ownerEquities)-1] - fin.prefferedEquities[len(fin.prefferedEquities)-1]) / totalShares
			}
		}

		// PE
		pe = stock.Price / eps
		if eps > 0 && bvps > 0 {
			// Graham value = SQRT(15*1.5*EPS*BVPS)
			grahamVal = math.Sqrt(15 * 1.5 * eps * bvps)
		}
	}

	// Standard deviation revenue
	//revMean, revSD, revZScore := util.CalSlopeSigma(fin.revenues, true)

	// CY = CV(DELTA Income)/CV(DELTA Sale)
	netMean, netSD, _ := util.CalSlopeSigma(fin.netIncomes, true)
	saleMean, saleSD, _ := util.CalSlopeSigma(fin.saleExpenses, true)

	CVDI := netSD / netMean
	CVDS := saleSD / saleMean
	cy := CVDI / CVDS
	// Detect net smooth
	netSmooth := cy < 1

	// fmt.Printf(`Result: [stock]%s [smooth]%v [grade]%d [p/e]%f [roe]%f [eps]%f [bvps]%f
	// 			[revMean]%f [revSD]%f [rev-z-score]%f [saleMean]%f [saleSD]%f [sale-z-score]%f
	// 			[g/r]%f  [i/g]%f  [a/g]%f [cash ratio]%f [turn over ratio]%f [graham value]%f
	// 			`,
	// 	stock.ID, netSmooth, grade, pe, roe, eps, bvps,
	// 	revMean, revSD, revZScore,
	// 	saleMean, saleSD, saleZScore,
	// 	grRatio, igRatio, agRatio, cashRatio, ebitRatio, grahamVal)

	return &model.Stock{
		ID:                   stock.ID,
		PE:                   pe,
		ROE:                  roe,
		EPS:                  eps,
		BVPS:                 bvps,
		GrossProfitMargin:    grRatio,
		InterestExpenseRatio: igRatio,
		AdminExpenseRatio:    agRatio,
		CashRatio:            cashRatio,
		DebtCoverRatio:       ebitRatio,
		GrahamValue:          grahamVal,
		Grade:                grade,
		RevComparedYOY:       revComparedYoY,
		NetSmooth:            netSmooth,
	}, nil

}

func (a *Processor) updateRevenueRatio(ctx context.Context, stock *model.Stock) error {
	tx, err := a.db.BeginTx(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted, ReadOnly: false})
	if err != nil {
		return err
	}
	defer tx.Rollback()

	stmt, err := tx.Prepare("UPDATE stocks SET roe = $1, eps = $2, bvps = $3, gross_profit_margin = $4, interest_expense_ratio = $5, admin_expense_ratio = $6, cash_ratio = $7, debt_cover_ratio = $8, graham_value = $9, updated_at = $10, grade = $11, net_smooth = $12, rev_ratio_yoy = $13 WHERE id = $14")
	if err != nil {
		return err
	}

	_, err = stmt.ExecContext(ctx, stock.ROE, stock.EPS, stock.BVPS, stock.GrossProfitMargin, stock.InterestExpenseRatio, stock.AdminExpenseRatio, stock.CashRatio, stock.DebtCoverRatio, stock.GrahamValue, time.Now(), stock.Grade, stock.NetSmooth, stock.RevComparedYOY, stock.ID)
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	return nil
}
