package analysis

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"strconv"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/lib/pq"
)

func StocksFunnel(ctx context.Context) ([]*model.StockFilter, error) {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	var stocks []*model.StockFilter
	cursor := ""
	for {
		// Temp remove (cash_ratio >= 1 OR cash_ratio = 0) AND roe >= 20 AND mean_revenue > 0
		query := fmt.Sprintf(`SELECT id, years, updated_at FROM stocks WHERE
            (rev_ratio_yoy > 0 OR rev_ratio_qoq > 0) AND
			gross_profit_margin >= 0.2 AND
			interest_expense_ratio <= 0.4 AND
			admin_expense_ratio <= 0.25 AND
			(debt_cover_ratio >= 4 OR debt_cover_ratio = 0) AND
			rev_last_quarter is not null AND
			id > $1 ORDER BY id LIMIT 1`)
		r := tx.QueryRowContext(ctx, query, cursor)

		var (
			stock     string
			years     string
			updatedAt time.Time
		)
		if err := r.Scan(&stock, &years, &updatedAt); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return nil, err
		}
		cursor = stock

		// Process years
		var yearsList []string
		for _, y := range strings.Split(years, ",") {
			yearsList = append(yearsList, strings.ReplaceAll(y, "`", ""))
		}
		latestYear, err := strconv.Atoi(yearsList[len(yearsList)-1])
		if err != nil {
			return nil, err
		}
		stocks = append(stocks, &model.StockFilter{
			Code:      stock,
			Year:      latestYear,
			UpdatedAt: updatedAt,
		})
	}
	return stocks, nil
}

func StockDetailsFunnel(ctx context.Context, filteredStock []string, telegram contract.Telegram) ([]*model.StockGrades, error) {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	var stocks []*model.StockGrades
	sixMonthInterested := 0.0
	r := tx.QueryRowContext(ctx, "SELECT six_month_interest FROM bank_interests ORDER BY created_at DESC LIMIT 1")
	err := r.Scan(&sixMonthInterested)
	if err != nil {
		return nil, err
	}

	cursor := ""
	for {
		// Temp remove (price/eps) <= 20 AND price <= graham_value
		stmt, err := tx.PrepareContext(ctx, `SELECT id, years, company_name, rev_ratio_yoy, rev_ratio_qoq, rev_last_quarter, grade, price, open_price, expected_value_1y, dividend, mean_dividend, graham_value, net_smooth, mean_52w, sd_price_52w FROM stocks
			WHERE 
			price > 0 AND
			expected_value_1y + dividend > 0 AND
			((dividend/price) >= $1 OR (mean_dividend/price) >= $2) AND
			id = ANY($3) AND
			id > $4 ORDER BY id LIMIT 1`)
		if err != nil {
			return nil, err
		}
		r := stmt.QueryRowContext(ctx, sixMonthInterested, sixMonthInterested, pq.StringArray(filteredStock), cursor)

		var (
			stock          string
			years          string
			name           string
			revRatioYOY    float64
			revRatioQOQ    float64
			revLastQuarter string
			grade          float64
			price          float64
			openPrice      float64
			expectedValue  float64
			dividend       float64
			meanDividend   float64
			grahamValue    float64
			netSmooth      bool
			meanPrice      float64
			sdPrice        float64
		)
		if err := r.Scan(&stock, &years, &name, &revRatioYOY, &revRatioQOQ, &revLastQuarter, &grade, &price, &openPrice, &expectedValue, &dividend, &meanDividend, &grahamValue, &netSmooth, &meanPrice, &sdPrice); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return nil, err
		}
		cursor = stock
		// Process years
		var yearsList []string
		for _, y := range strings.Split(years, ",") {
			yearsList = append(yearsList, strings.ReplaceAll(y, "`", ""))
		}
		latestYear := yearsList[len(yearsList)-1]
		stocks = append(stocks, &model.StockGrades{
			Stock:                  stock,
			Year:                   latestYear,
			Name:                   name,
			RevRatioYOY:            telegram.NumberFormat(revRatioYOY * 100),
			RevRatioQOQ:            telegram.NumberFormat(revRatioQOQ * 100),
			PricePerExpectedValue:  telegram.NumberFormat(price / (expectedValue + dividend)),
			LatestQuarter:          revLastQuarter,
			NetSmooth:              netSmooth,
			Stars:                  telegram.StarFormat(int(grade)),
			Price:                  telegram.NumberFormat(int(price)),
			IsPriceIncreased:       price > openPrice,
			CurrentPercentDividend: telegram.NumberFormat((dividend / price) * 100),
			PercentDividend:        telegram.NumberFormat((meanDividend / price) * 100),
			ProfitMarginGraham:     telegram.NumberFormat((grahamValue/price - 1) * 100),
			PriceVolatilityNumber:  meanPrice,
			PriceVolatility:        telegram.NumberFormat(int(meanPrice)),
			PriceRisk:              telegram.NumberFormat(int(sdPrice)),
		})
	}
	return stocks, nil
}
