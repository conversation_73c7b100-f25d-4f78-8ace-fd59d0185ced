package filejob

import (
	"context"
	"crypto/sha256"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"io"
	"net"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/valyala/fastjson"
)

type pacman struct {
	cfg       *config.Config
	db        *sql.DB
	nextJob   chan bool
	transport *http.Transport
	logger    *zap.Logger
}

func NewPacman(cfg *config.Config, db *sql.DB, logger *zap.Logger, trans *http.Transport) (contract.JobEater, error) {
	p := &pacman{
		cfg:       cfg,
		nextJob:   make(chan bool),
		db:        db,
		logger:    logger,
		transport: trans,
	}
	return p, nil
}

func (p *pacman) NudgeQueue() {
	go func() {
		retry := p.cfg.WorkerPool
		i := 0
		for {
			<-time.After(1 * time.Second)
			// None-blocking channel
			select {
			case p.nextJob <- true:
			default:
			}
			i++
			if i > retry {
				return
			}
		}
	}()
}

func (p *pacman) IngestJob(ctx context.Context, job *model.NanoJob) error {
	return p.IngestJobWithPriority(ctx, job, false)
}

func (p *pacman) IngestJobWithPriority(ctx context.Context, job *model.NanoJob, isFirst bool) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	jobByte, err := json.Marshal(job)
	if err != nil {
		return err
	}
	// Check sum
	h := sha256.New()
	h.Write(jobByte)
	jSum := fmt.Sprintf("%x", h.Sum(nil))
	stmt, err := tx.Prepare("SELECT id FROM jobs WHERE id = $1")
	if err != nil {
		return err
	}
	insertTime := time.Now().UTC()
	// first priority
	if isFirst {
		var createdAt time.Time
		if err := tx.QueryRowContext(ctx, "SELECT created_at FROM jobs ORDER BY created_at ASC LIMIT 1").Scan(&createdAt); err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}
		if errors.Is(err, sql.ErrNoRows) {
			insertTime = createdAt.AddDate(0, 0, -1)
		}
	}
	r := stmt.QueryRowContext(ctx, jSum)
	var id string

	if err = r.Scan(&id); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// Insert new jobs
			stmt, err := tx.Prepare("INSERT INTO jobs(id,url,params,method,created_at,ctx,step,map,namespace,created_by) VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)")
			if err != nil {
				return err
			}
			_, err = stmt.ExecContext(ctx, jSum, job.Url, job.Params, job.Method, insertTime, job.RequestContext, job.Step, job.Map, job.NameSpace, job.Owner)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}
	p.NudgeQueue()

	return nil
}

func (p *pacman) ReleaseTheJobEater(sm contract.StateMachine) error {
	for i := 0; i < p.cfg.WorkerPool; i++ {
		go func() {
			for {
				<-p.nextJob
				resp, err := p.selectAndExecute(p.db)
				if err != nil {
					if errors.Is(err, sql.ErrNoRows) {
						continue
					}
					if strings.Contains(err.Error(), "pq: could not serialize access") {
						p.NudgeQueue()
					}
					p.logger.Error("selectAndExecute error", zap.Error(err))
					continue
				}
				// enqueue next job if job has map
				if resp.Map != "" {
					err = p.enqueueNextJob(p.db, sm, resp)
					if err != nil && !errors.Is(err, constants.CommonError.ERROR_NOT_FOUND) {
						// backup error jobs
						fmt.Println("enqueueNextJob job error:", err)
					}
				} else {
					p.NudgeQueue()
				}

			}
		}()
	}

	return nil
}

func (p *pacman) requestJobUrl(ctx context.Context, nanoJob *model.NanoJob) (*model.JobResponse, error) {
	client := &http.Client{
		Timeout:   180 * time.Second,
		Transport: p.transport,
	}

	req, err := http.NewRequest(nanoJob.Method, nanoJob.Url, strings.NewReader(nanoJob.Params))
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("Content-Type", "application/json")
	if nanoJob.Method == http.MethodGet && nanoJob.Params != "" {
		q := req.URL.Query()
		p, err := fastjson.Parse(nanoJob.Params)
		if err != nil {
			return nil, err
		}
		o, err := p.Object()
		if err != nil {
			return nil, err
		}
		o.Visit(func(k []byte, v *fastjson.Value) {
			b, err := v.StringBytes()
			if err != nil {
				return
			}
			q.Add(string(k), string(b))
		})
		req.URL.RawQuery = q.Encode()
	}

	p.logger.Info("Requesting job",
		zap.String("url", req.URL.String()),
		zap.String("params", nanoJob.Params),
	)

	resp, err := client.Do(req)
	var e net.Error
	if errors.As(err, &e) && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	}
	defer resp.Body.Close()

	// Store validator error
	content, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	s := strings.TrimSpace(string(content))
	p.logger.Info("Response", zap.String("response", s))

	var ctxParams map[string]interface{}
	if nanoJob.RequestContext != "" {
		err = json.Unmarshal([]byte(nanoJob.RequestContext), &ctxParams)
		if err != nil {
			return nil, err
		}
	} else {
		ctxParams = make(map[string]interface{})
	}

	// Always replace chat_id by the owner of the job
	ctxParams["chat_id"] = nanoJob.Owner

	if resp.StatusCode != http.StatusOK {
		return nil, constants.BotError.REQUESTERROR //p.sendErrorToUser(ctx, s, nanoJob.Owner)
	} else {
		var respCtx map[string]interface{}
		err = json.Unmarshal(content, &respCtx)
		if err != nil {
			return nil, err
		}
		// Merging response context with response context
		for k, v := range respCtx {
			ctxParams[k] = v
		}
	}

	newJobCtx, err := json.Marshal(ctxParams)
	if err != nil {
		return nil, err
	}

	// Check job expansion
	if expandedMap, ok := ctxParams[constants.JobDefination.EXPANDING]; ok {
		var prevMap map[int]string
		err = json.Unmarshal([]byte(nanoJob.Map), &prevMap)
		if err != nil {
			return nil, err
		}
		newMap := make(map[int]interface{})
		// Don't know why json unmarshal map[int]string -> map[string]string
		for k, v := range expandedMap.(map[string]interface{}) {
			i, err := strconv.Atoi(k)
			if err != nil {
				return nil, err
			}
			newMap[i] = v
		}
		neoIndex := len(newMap) - 1
		for k, v := range prevMap {
			if k <= nanoJob.Step {
				continue
			}
			// append new index
			newMap[neoIndex+k-nanoJob.Step] = v
		}
		newMapStr, err := json.Marshal(newMap)
		if err != nil {
			return nil, err
		}
		// Delete internal param
		delete(ctxParams, constants.JobDefination.EXPANDING)
		newJobCtx, err = json.Marshal(ctxParams)
		if err != nil {
			return nil, err
		}

		return &model.JobResponse{
			RequestContext: string(newJobCtx),
			Step:           -1, // reset step
			Map:            string(newMapStr),
			NameSpace:      nanoJob.NameSpace,
			Owner:          nanoJob.Owner,
		}, nil
	}

	jobResp := &model.JobResponse{
		RequestContext: string(newJobCtx),
		Step:           nanoJob.Step,
		Map:            nanoJob.Map,
		Owner:          nanoJob.Owner,
	}
	return jobResp, nil
}

func (p *pacman) selectAndExecute(db *sql.DB) (*model.JobResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 180*time.Second)
	defer cancel()

	tx, err := db.BeginTx(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted, ReadOnly: false})
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer tx.Rollback()
	nanoJob := new(model.NanoJob)
	var jobID string

	stmt, err := tx.PrepareContext(ctx, "SELECT id FROM jobs ORDER BY created_at ASC LIMIT 1")
	if err != nil {
		return nil, err
	}
	defer stmt.Close()
	r := stmt.QueryRowContext(ctx)
	err = r.Scan(&jobID)
	if err != nil {
		return nil, err
	}

	// Pessimistic lock that id for remove
	lockStmt, err := tx.PrepareContext(ctx, "SELECT url, params, method, ctx, map, step, namespace, created_by FROM jobs WHERE id = $1 FOR UPDATE")
	if err != nil {
		return nil, err
	}
	defer lockStmt.Close()

	r = lockStmt.QueryRowContext(ctx, jobID)
	if err := r.Scan(&nanoJob.Url, &nanoJob.Params, &nanoJob.Method, &nanoJob.RequestContext, &nanoJob.Map, &nanoJob.Step, &nanoJob.NameSpace, &nanoJob.Owner); err != nil {
		return nil, err
	}

	deleteStmt, err := tx.PrepareContext(ctx, "DELETE FROM jobs WHERE id = $1")
	if err != nil {
		return nil, err
	}
	defer deleteStmt.Close()

	_, err = deleteStmt.ExecContext(ctx, jobID)
	if err != nil {
		return nil, err
	}

	// Query user
	userStmt, err := tx.PrepareContext(ctx, "SELECT external_id, channel FROM chatbot WHERE id = $1")
	if err != nil {
		return nil, err
	}
	defer userStmt.Close()

	var (
		chatChannelID string
		chatChannel   string
	)
	r = userStmt.QueryRowContext(ctx, nanoJob.Owner)
	if err := r.Scan(&chatChannelID, &chatChannel); err != nil {
		return nil, err
	}
	var contextObj map[string]interface{}
	err = json.Unmarshal([]byte(nanoJob.Params), &contextObj)
	if err != nil {
		return nil, err
	}
	// Add channel user id to context
	contextObj["chat_channel_id"] = chatChannelID
	contextObj["chat_channel"] = chatChannel

	jsonContext, err := json.Marshal(contextObj)
	if err != nil {
		return nil, err
	}
	nanoJob.Params = string(jsonContext)

	// Commit the transaction before processing the job
	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return p.requestJobUrl(ctx, nanoJob)
}

func (p *pacman) sendErrorToUser(ctx context.Context, resp string, chatID string) error {
	rootObj, err := fastjson.Parse(resp)
	if err != nil {
		return err
	}
	errorMessage := ""
	returnErr := rootObj.GetStringBytes("error", "message")
	if string(returnErr) != "" {
		errorMessage = string(returnErr)
	} else {
		o := rootObj.GetObject("error", "message")
		validators := make(map[string]interface{})
		o.Visit(func(k []byte, v *fastjson.Value) {
			vb, _ := v.StringBytes()
			if string(vb) != "" {
				validators[string(k)] = string(vb)
			}
		})
		validatorErr, err := json.Marshal(validators)
		if err != nil {
			return err
		}
		errorMessage = string(validatorErr)
	}
	// Can't determine user to send error
	if chatID == "" {
		return errors.New(errorMessage)
	}
	// send error and stop the process
	params := map[string]string{
		"chat_id": chatID,
		"message": errorMessage,
	}
	paramData, err := json.Marshal(params)
	if err != nil {
		return err
	}
	job := &model.NanoJob{
		Url:    fmt.Sprintf("http://localhost:%s/v1/channel/ask", p.cfg.HTTPPort),
		Params: string(paramData),
		Method: http.MethodPost,
	}
	err = p.IngestJobWithPriority(ctx, job, true)
	if err != nil {
		return err
	}
	return errors.New(errorMessage)
}

func (p *pacman) enqueueNextJob(db *sql.DB, m contract.StateMachine, jobResp *model.JobResponse) error {
	ctx, cancel := context.WithTimeout(context.Background(), 180*time.Second)
	defer cancel()
	tx, err := db.BeginTx(ctx, &sql.TxOptions{Isolation: sql.LevelReadUncommitted, ReadOnly: false})
	if err != nil {
		return err
	}
	defer tx.Rollback()

	ctx = context.WithValue(ctx, constants.ContextTransactionKey, tx)
	// Copy new job
	newJobResp := *jobResp
	newJobResp.Step++
	job, err := m.BuildNextJob(ctx, &newJobResp)
	if err != nil {
		return err
	}
	err = p.IngestJob(ctx, job)
	if err != nil {
		return err
	}
	// Commit the transaction before processing the job
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	return nil
}
