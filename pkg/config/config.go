package config

import (
	"encoding/json"
	"time"
)

// Config holds all settings of krarks
type Config struct {
	AppName         string        `yaml:"app_name" mapstructure:"app_name" json:"app_name"`
	HTTPPort        string        `yaml:"port" mapstructure:"port" json:"port"`
	WorkerPool      int           `yaml:"worker_pool" mapstructure:"worker_pool" json:"worker_pool"`
	StockPriceCache time.Duration `yaml:"stock_price_cache_hour" mapstructure:"stock_price_cache_hour" json:"stock_price_cache_hour"`
	Environment     string        `yaml:"environment" mapstructure:"environment" json:"environment"`
	ChatScopes      []string      `yaml:"chat_scopes" mapstructure:"chat_scopes" json:"chat_scopes"`
	JWT             *JWT          `yaml:"jwt" mapstructure:"jwt" json:"jwt"`
	Telegram        *Telegram     `yaml:"telegram" mapstructure:"telegram" json:"telegram"`
	AWSS3           *S3           `yaml:"s3" mapstructure:"s3" json:"s3"`
	TemplatePath    *Template     `yaml:"template" mapstructure:"template" json:"template"`
	StoragePath     *StoragePath  `yaml:"storage" mapstructure:"storage" json:"storage"`
	RedisURL        string        `yaml:"redis_url" mapstructure:"redis_url" json:"redis_url"`
	FrontendURL     string        `yaml:"frontend_url" mapstructure:"frontend_url" json:"frontend_url"`
	Database        *Database     `yaml:"database" mapstructure:"database" json:"database"`
	SSI             *SSI          `yaml:"ssi" mapstructure:"ssi" json:"ssi"`
	FireAnt         *FireAnt      `yaml:"fireant" mapstructure:"fireant" json:"fireant"`
	Facebook        *Facebook     `yaml:"facebook" mapstructure:"facebook" json:"facebook"`
}

func (cfg *Config) IsProduction() bool {
	return cfg.Environment == "production"
}

func (cfg *Config) PrettyPrint() string {
	s, _ := json.MarshalIndent(cfg, "", "\t")
	return string(s)
}

// Database ...
type Database struct {
	URL       string `yaml:"url" mapstructure:"url" json:"url"`
	Migration string `yaml:"migration" mapstructure:"migration" json:"migration"`
}

// Template ...
type Template struct {
	EmailPath    string `yaml:"email_path" mapstructure:"email_path" json:"email_path"`
	RoutinePath  string `yaml:"routine_path" mapstructure:"routine_path" json:"routine_path"`
	PretrainPath string `yaml:"train_path" mapstructure:"train_path" json:"train_path"`
	SSIDictPath  string `yaml:"ssi_dict_path" mapstructure:"ssi_dict_path" json:"ssi_dict_path"`
	ChatPath     string `yaml:"chat_path" mapstructure:"chat_path" json:"chat_path"`
	MessagePath  string `yaml:"message_path" mapstructure:"message_path" json:"message_path"`
}

// JWT ...
type JWT struct {
	Secret string `yaml:"secret" mapstructure:"secret" json:"secret"`
}

// Telegram ...
type Telegram struct {
	Token string `yaml:"token" mapstructure:"token" json:"token"`
	Owner string `yaml:"owner" mapstructure:"owner" json:"owner"`
}

// Facebook ...
type Facebook struct {
	GraphURL        string `yaml:"graph_url" mapstructure:"graph_url" json:"graph_url"`
	VerifyToken     string `yaml:"verify_token" mapstructure:"verify_token" json:"verify_token"`
	AppSecret       string `yaml:"app_secret" mapstructure:"app_secret" json:"app_secret"`
	PageAccessToken string `yaml:"page_access_token" mapstructure:"page_access_token" json:"page_access_token"`
}

// Storage ...
type StoragePath struct {
	Model        string `yaml:"model" mapstructure:"model" json:"model"`
	Distribution string `yaml:"distribution" mapstructure:"distribution" json:"distribution"`
	Download     string `yaml:"download" mapstructure:"download" json:"download"`
	StockPrices  string `yaml:"stockprices" mapstructure:"stockprices" json:"stockprices"`
	CafefData    string `yaml:"cafefdata" mapstructure:"cafefdata" json:"cafefdata"`
	CafFin       string `yaml:"cafefin" mapstructure:"cafefin" json:"cafefin"`
	SSIFin       string `yaml:"ssifin" mapstructure:"ssifin" json:"ssifin"`
	VSD          string `yaml:"vsd" mapstructure:"vsd" json:"vsd"`
	SSCData      string `yaml:"sscdata" mapstructure:"sscdata" json:"sscdata"`
	SSCFin       string `yaml:"sscfin" mapstructure:"sscfin" json:"sscfin"`
}

// AWS s3 ...
type S3 struct {
	Bucket    string `yaml:"bucket" mapstructure:"bucket" json:"bucket"`
	Region    string `yaml:"region" mapstructure:"region" json:"region"`
	KeyID     string `yaml:"key_id" mapstructure:"key_id" json:"key_id"`
	AccessKey string `yaml:"access_key" mapstructure:"access_key" json:"access_key"`
	Internal  bool   `yaml:"internal" mapstructure:"internal" json:"internal"`
	URLPublic string `yaml:"url_public" mapstructure:"url_public" json:"url_public"`
}

// Vietstock ...
type Vietstock struct {
	Origin         string `yaml:"original_url" mapstructure:"original_url" json:"original_url"`
	StockScreener  string `yaml:"screener_url" mapstructure:"screener_url" json:"screener_url"`
	FinanceReport  string `yaml:"financial_report_url" mapstructure:"financial_report_url" json:"financial_report_url"`
	CompanyTrading string `yaml:"company_trading_url" mapstructure:"company_trading_url" json:"company_trading_url"`
	StocksTrading  string `yaml:"stocks_trading_url" mapstructure:"stocks_trading_url" json:"stocks_trading_url"`
}

// SSI ...
type SSI struct {
	XfiinUserToken string `yaml:"xfiin_user_token" mapstructure:"xfiin_user_token" json:"xfiin_user_token"`
}

// FireAnt ...
type FireAnt struct {
	BearerToken string `yaml:"bearer_token" mapstructure:"bearer_token" json:"bearer_token"`
}
