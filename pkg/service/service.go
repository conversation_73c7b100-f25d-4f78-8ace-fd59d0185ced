package service

import (
	"database/sql"

	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

// Service ...
type Service struct {
	Echo           *echo.Echo
	Config         *config.Config
	Logger         *zap.Logger
	Template       contract.Renderer
	Validator      contract.Validator
	DB             *sql.DB
	DownloadFileDB *sql.DB
	PriceFileDB    *sql.DB
	Storage        contract.UserStorage
	ServerStorage  contract.ServerStorage
	Vietstock      contract.Vietstock
	Cafef          contract.Cafef
	Telegram       contract.Telegram
	Facebook       contract.Facebook
	Redis          contract.Redis
	Pacman         contract.JobEater
	JWTs           contract.JWT
	Chatbot        contract.StateMachine
}
