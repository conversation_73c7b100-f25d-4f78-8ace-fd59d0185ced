package middleware

import (
	"context"
	"database/sql"

	"github.com/007lock/simon-homestead/internal/constants"

	"github.com/labstack/echo/v4"
)

func TransactionHandler(db *sql.DB) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			req := c.Request()
			if req.Method != "GET" {
				tx, err := db.BeginTx(req.Context(), &sql.TxOptions{Isolation: sql.LevelRepeatableRead, ReadOnly: false})
				if err != nil {
					return err
				}
				c.SetRequest(req.WithContext(context.WithValue(req.Context(), constants.ContextTransactionKey, tx)))

				if err := next(c); err != nil {
					tx.Rollback()
					c.Logger().Debug("Transaction Rollback: ", err)
					return err
				}
				c.<PERSON>gger().Debug("Transaction Commit")
				tx.Commit()
			} else {
				c.SetRequest(req.WithContext(context.WithValue(req.Context(), constants.ContextTransactionKey, db)))

				return next(c)
			}

			return nil
		}
	}
}

// func FileTransactionHandler(cfg *config.Config, st contract.Storage) echo.MiddlewareFunc {
// 	return func(next echo.HandlerFunc) echo.HandlerFunc {
// 		return func(c echo.Context) error {
// 			// Precache files
// 			for _, fileName := range c.ParamValues() {
// 				fileName = fmt.Sprintf("%s.csv", fileName)
// 				filePath := fmt.Sprintf("%s/%s", cfg.FinancialStoragePath, fileName)
// 				_, err := os.Stat(filePath)
// 				if err != nil {
// 					if os.IsNotExist(err) {
// 						driveFile, err := st.GetFileNameStorage(fileName)
// 						if err != nil {
// 							return echo.NewHTTPError(http.StatusBadRequest, err)
// 						}
// 						if err := st.DownloadFile(driveFile.FileID, filePath); err != nil {
// 							return echo.NewHTTPError(http.StatusInternalServerError, err)
// 						}
// 					} else {
// 						return echo.NewHTTPError(http.StatusBadRequest, err)
// 					}
// 				}
// 			}
// 			err := next(c)
// 			// Sync file storage
// 			for _, fileName := range c.ParamValues() {
// 				filePath := fmt.Sprintf("%s/%s.csv", cfg.FinancialStoragePath, fileName)
// 				driveFile, isSync, err := st.CheckfileExist(filePath)
// 				if err != nil {
// 					return echo.NewHTTPError(http.StatusBadRequest, err)
// 				}
// 				if !isSync {
// 					_, err := st.UploadReplaceFile(driveFile, filePath)
// 					if err != nil {
// 						return echo.NewHTTPError(http.StatusBadRequest, err)
// 					}
// 				}
// 			}
// 			return err
// 		}
// 	}
// }
