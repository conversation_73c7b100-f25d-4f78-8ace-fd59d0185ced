package middleware

import (
	"context"
	"net/http"
	"strings"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/labstack/echo/v4"
)

func JWTHandler(s *service.Service) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			authString := c.Request().Header.Get("Authorization")
			req := c.Request()
			if authString != "" {
				tokens := strings.Split(authString, " ")
				user, err := s.JWTs.Verify(tokens[1])
				if err != nil {
					return echo.NewHTTPError(http.StatusUnauthorized, constants.AuthenticationError.EXPIRED)
				}
				c.SetRequest(req.WithContext(context.WithValue(req.Context(), constants.ContextU<PERSON><PERSON><PERSON>, user)))
				return next(c)
			}
			return echo.NewHTTPError(http.StatusUnauthorized, constants.AuthenticationError.EXPIRED)
		}
	}
}
