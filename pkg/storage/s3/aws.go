package s3storage

import (
	"fmt"
	"io"
	"os"

	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3iface"
)

type s3Storage struct {
	cfg *config.Config
	svc s3iface.S3API
}

func NewS3Storage(cfg *config.Config) (contract.ServerStorage, error) {
	// S3 session
	awsCfg := aws.NewConfig().WithRegion(cfg.AWSS3.Region)
	if !cfg.AWSS3.Internal {
		creds := credentials.NewStaticCredentials(cfg.AWSS3.KeyID, cfg.AWSS3.AccessKey, "")
		awsCfg = awsCfg.WithCredentials(creds)
	}

	s, err := session.NewSession()
	if err != nil {
		return nil, err
	}

	svc := s3.New(s, awsCfg)

	return &s3Storage{cfg, svc}, nil
}

func (st *s3Storage) GetBucket() string {
	return st.cfg.AWSS3.Bucket
}

func (st *s3Storage) CreateFolderIfNotExisted(folder string, parent string) (string, error) {
	files, err := st.GetListfilesInFolder("", parent)
	if err != nil {
		return "", err
	}
	folderKey := fmt.Sprintf("%s/%s/", parent, folder)
	for _, file := range files {
		if file.FileID == folderKey {
			return file.FileID, nil
		}
	}
	return st.CreateFolder(folder, parent)
}

func (st *s3Storage) CreateFolder(folder string, parent string) (string, error) {
	params := &s3.PutObjectInput{
		Bucket:        aws.String(st.cfg.AWSS3.Bucket),
		Key:           aws.String(fmt.Sprintf("%s/%s/", parent, folder)),
		ContentLength: aws.Int64(0),
		ACL:           aws.String("private"),
	}
	_, err := st.svc.PutObject(params)
	if err != nil {
		return "", err
	}

	return *params.Key, nil
}

func (st *s3Storage) GetListfilesInFolder(continuationToken string, folderId string) ([]*model.StorageFile, error) {
	params := &s3.ListObjectsV2Input{
		Bucket: aws.String(st.cfg.AWSS3.Bucket),
		Prefix: aws.String(folderId),
	}
	if continuationToken != "" {
		params.ContinuationToken = &continuationToken
	}

	out, err := st.svc.ListObjectsV2(params)
	if err != nil {
		return nil, err
	}
	var files []*model.StorageFile
	for _, content := range out.Contents {
		file := &model.StorageFile{
			FileName: *content.Key,
			FileID:   *content.Key,
			FileSize: *content.Size,
		}
		files = append(files, file)
	}

	complete := out.IsTruncated != nil && !*out.IsTruncated
	if !complete {
		conFiles, err := st.GetListfilesInFolder(*out.ContinuationToken, folderId)
		if err != nil {
			return nil, err
		}
		files = append(files, conFiles...)
	}

	return files, nil
}

func (st *s3Storage) UploadNewFileIntoFolder(folderId string, fileName string, src string) (*model.StorageFile, error) {
	fileNamePath := fmt.Sprintf("%s/%s", folderId, fileName)
	reader, err := os.Open(src)
	if err != nil {
		return nil, err
	}
	stat, err := os.Stat(src)
	if err != nil {
		return nil, err
	}
	params := &s3.PutObjectInput{
		Bucket:        aws.String(st.cfg.AWSS3.Bucket),
		Key:           aws.String(fileNamePath),
		Body:          reader,
		ContentLength: aws.Int64(stat.Size()),
		ACL:           aws.String("private"),
	}
	_, err = st.svc.PutObject(params)
	if err != nil {
		return nil, err
	}
	return &model.StorageFile{
		FileName: *params.Key,
		FileID:   *params.Key,
		FileSize: stat.Size(),
	}, nil
}

func (st *s3Storage) UploadReplaceFile(folder string, filename string, src string) (*model.StorageFile, error) {
	files, err := st.GetListfilesInFolder("", folder)
	if err != nil {
		return nil, err
	}
	fileKey := fmt.Sprintf("%s/%s", folder, filename)
	for _, file := range files {
		if file.FileID == fileKey {
			return file, nil
		}
	}
	return st.UploadNewFileIntoFolder(folder, filename, src)
}

func (st *s3Storage) DownloadFile(fileID string, dest string) error {
	params := &s3.GetObjectInput{
		Bucket: aws.String(st.cfg.AWSS3.Bucket),
		Key:    aws.String(fileID),
	}
	out, err := st.svc.GetObject(params)
	if err != nil {
		return err
	}
	defer out.Body.Close()
	file, err := os.Create(dest)
	if err != nil {
		return err
	}
	defer file.Close()
	_, err = io.Copy(file, out.Body)
	if err != nil {
		return err
	}
	return nil
}
