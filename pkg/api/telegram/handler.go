package http

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"math/rand"
	"net/http"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"

	"github.com/labstack/echo/v4"
)

func ReceiveMessage(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		telegramMsg := new(model.ChatMessage)
		err := c.Bind(telegramMsg)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		id, err := uuid.NewV7() // Generate a new UUID
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		chatID := fmt.Sprintf("%d", telegramMsg.Message.Chat.ID)
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		stmt, err := tx.Prepare("SELECT id, state, scopes FROM chatbot WHERE external_id = $1 AND channel = $2")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		chatObject := &model.ChatObject{
			ID:         id.String(),
			ExternalID: fmt.Sprintf("%d", telegramMsg.Message.Chat.ID),
			Channel:    constants.ChatChannels.TELEGRAM,
			User:       telegramMsg.Message.Chat.Username,
			FirstName:  telegramMsg.Message.Chat.FirstName,
			LastName:   telegramMsg.Message.Chat.LastName,
			Owner:      0,
			Scopes: []string{
				"scheduler",
			},
		}
		r := stmt.QueryRowContext(c.Request().Context(), chatID, chatObject.Channel)
		var scopeNull sql.NullString
		if err = r.Scan(&chatObject.ID, &chatObject.State, &scopeNull); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				// Insert new user
				stmt, err = tx.Prepare("INSERT INTO chatbot(id,external_id,username,first_name,last_name,owner,scopes,routine_updated_at) VALUES($1,$2,$3,$4,$5,$6,$7,$8)")
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
				now := time.Now()
				eod := time.Date(now.Year(), now.Month(), now.Day(), 0, 30, 0, 0, loc)
				scopesByte, err := json.Marshal(chatObject.Scopes)
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				_, err = stmt.ExecContext(c.Request().Context(), chatObject.ID, chatObject.ExternalID, chatObject.User, chatObject.FirstName, chatObject.LastName, chatObject.Owner, string(scopesByte), eod.UTC())
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
			} else {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}

		if scopeNull.Valid {
			err := json.Unmarshal([]byte(scopeNull.String), &chatObject.Scopes)
			if err != nil {
				return err
			}
		}

		data, err := json.Marshal(telegramMsg)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		s.Logger.Info("Telegram Message:", zap.String("data", string(data)))

		// Check user has inputted state
		if chatObject.State.Valid {
			// user either cancel or input variable
			workflowStr, err := s.Chatbot.DetectWorkflowByChatObj(chatObject, telegramMsg.Message.Text)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			if workflowStr == "scheduler/cancel" {
				job, err := s.Chatbot.CancelUserState(c.Request().Context(), chatObject.ID)
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				err = s.Pacman.IngestJobWithPriority(c.Request().Context(), job, true)
				return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
			}

			// Merge input to context
			job, err := s.Chatbot.UserInput(c.Request().Context(), chatObject, telegramMsg.Message.Text)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			err = s.Pacman.IngestJob(c.Request().Context(), job)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
		}

		job, err := s.Chatbot.DetectUserContext(c.Request().Context(), chatObject, telegramMsg.Message.Text)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		err = s.Pacman.IngestJob(c.Request().Context(), job)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Send chat typing action to user
		typingReqBody := &model.SendChatActionReqBody{
			ChatID: chatObject.ExternalID,
			Action: "typing",
		}
		err = s.Telegram.SendChatAction(c.Request().Context(), typingReqBody)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Send chat emoji action to user
		rd := rand.New(rand.NewSource(time.Now().UnixNano()))
		n := rd.Intn(len(constants.TelegramSupportedEmojis))
		reactionReqBody := &model.SendMessageReactionReqBody{
			ChatID:    chatObject.ExternalID,
			MessageID: telegramMsg.Message.MessageID,
			IsBig:     true,
			ReactionType: []*model.ReactionType{
				{
					Type:  "emoji",
					Emoji: constants.TelegramSupportedEmojis[n],
				},
			},
		}
		err = s.Telegram.SetMessageReaction(c.Request().Context(), reactionReqBody)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}

func SendTypingActionUser(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type ActionUserRequest struct {
			ChatID string `json:"chat_id"`
		}
		actReq := new(ActionUserRequest)
		err := c.Bind(actReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}
		// Create the request body struct
		reqBody := &model.SendChatActionReqBody{
			ChatID: actReq.ChatID,
			Action: "typing",
		}
		err = s.Telegram.SendChatAction(c.Request().Context(), reqBody)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}
