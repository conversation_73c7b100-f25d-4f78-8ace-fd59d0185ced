package distribution

import (
	"bufio"
	"database/sql"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/labstack/echo/v4"
)

func FetchDistribution(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type distRequest struct {
			FileName string `query:"file_name" validate:"required"`
		}
		distReq := new(distRequest)

		err := c.Bind(distReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}
		var values []float64

		f, err := os.OpenFile(fmt.Sprintf("%s/%s.txt", s.Config.StoragePath.Distribution, distReq.FileName), os.O_RDONLY, os.ModePerm)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		defer f.Close()

		sc := bufio.NewScanner(f)
		for sc.Scan() {
			splittedStr := strings.Split(sc.Text(), " ")
			if s, err := strconv.ParseFloat(splittedStr[1], 64); err == nil {
				values = append(values, s)
			} else {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}
		if err := sc.Err(); err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		return c.JSON(http.StatusOK, values)
	}
}

func FetchVarianceOfStock(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type stockRequest struct {
			StockCode string `query:"code" validate:"required"`
		}
		stockReq := new(stockRequest)

		err := c.Bind(stockReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		db := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
		stmt, err := db.Prepare("SELECT price, dividend, sd_price_52w, mean_52w FROM stocks WHERE ID = $1")
		if err != nil {
			return err
		}
		r := stmt.QueryRowContext(c.Request().Context(), stockReq.StockCode)
		var (
			price     float64
			dividend  float64
			sdPrice   float64
			meanPrice float64
		)
		if err := r.Scan(&price, &dividend, &sdPrice, &meanPrice); err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"price":      price / 1000,
			"dividend":   dividend / 1000,
			"sd_price":   sdPrice,
			"mean_price": meanPrice,
		})
	}
}
