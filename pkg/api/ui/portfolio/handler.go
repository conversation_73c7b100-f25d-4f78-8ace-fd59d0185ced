package portfolioui

import (
	"database/sql"
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/007lock/simon-homestead/pkg/util"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
	"golang.org/x/net/context"
	"gonum.org/v1/gonum/mat"
)

func GetAssets(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		user := c.Request().Context().Value(constants.ContextUserKey).(*model.UserInfo)

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
		// Query list assets
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock_id,category,buy_at,quantity,price,tax FROM portfolios WHERE user_id = $1 AND category = $2")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs, err := stmt.QueryContext(c.Request().Context(), user.ID, constants.AssetCategories.STOCK)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		type Assets struct {
			ID       string  `json:"code"`
			Category string  `json:"category"`
			Date     string  `json:"date"`
			Quantity float64 `json:"quantity"`
			Price    float64 `json:"price"`
			Tax      float64 `json:"tax"`
		}
		var assets []*Assets
		for rs.Next() {
			asset := &Assets{}
			err := rs.Scan(&asset.ID, &asset.Category, &asset.Date, &asset.Quantity, &asset.Price, &asset.Tax)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			assets = append(assets, asset)
		}
		defer rs.Close()

		return c.JSON(http.StatusOK, assets)
	}
}

func GetAssetSelling(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		user := c.Request().Context().Value(constants.ContextUserKey).(*model.UserInfo)

		var stocks []string
		type AssetBookValue struct {
			quantity  float64
			bookValue float64
			dividend  float64
			buyAt     time.Time
		}
		stockMap := make(map[string]*AssetBookValue)

		// Query Recently Stocks
		err := func(ctx context.Context, stockDict map[string]*AssetBookValue) error {
			tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
			stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock, quantity, book_value, buy_at FROM current_portfolios WHERE user_id = $1")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			rs, err := stmt.QueryContext(c.Request().Context(), user.ID)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			defer rs.Close()
			for rs.Next() {
				var (
					assetID   string
					quantity  float64
					bookValue float64
					buyAt     time.Time
				)
				err := rs.Scan(&assetID, &quantity, &bookValue, &buyAt)
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				stocks = append(stocks, assetID)
				stockDict[assetID] = &AssetBookValue{
					quantity:  quantity,
					bookValue: bookValue,
					buyAt:     buyAt,
				}
			}
			return nil
		}(c.Request().Context(), stockMap)
		if err != nil {
			return err
		}

		// Query dividend
		err = func(ctx context.Context, stockDict map[string]*AssetBookValue) error {
			tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
			for stock, asset := range stockDict {
				stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT sum(profits) FROM pl_portfolios WHERE user_id = $1 AND stock = $2 AND record_at >= $3 AND source = $4 GROUP BY stock")
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				r := stmt.QueryRowContext(c.Request().Context(), user.ID, stock, asset.buyAt, constants.AssetCategories.DIVIDEND)
				var totalDiv float64
				err = r.Scan(&totalDiv)
				if err != nil {
					if errors.Is(err, sql.ErrNoRows) {
						continue
					}
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				asset.dividend = totalDiv
			}
			return nil
		}(c.Request().Context(), stockMap)
		if err != nil {
			return err
		}

		// query bank interest
		bankInterest, err := func(ctx context.Context) (float64, error) {
			tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
			r := tx.QueryRowContext(c.Request().Context(), "SELECT six_month_interest FROM bank_interests ORDER BY created_at DESC LIMIT 1")
			sixMonthInterest := 0.0
			err := r.Scan(&sixMonthInterest)
			if err != nil && !errors.Is(err, sql.ErrNoRows) {
				return 0, echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			return sixMonthInterest, nil
		}(c.Request().Context())
		if err != nil {
			return err
		}

		type Assets struct {
			ID          string    `json:"code"`
			Price       float64   `json:"price"`
			OpenPrice   float64   `json:"open_price"`
			BookPrice   float64   `json:"book_price"`
			Dividend    float64   `json:"dividend"`
			MeanPrice   float64   `json:"mean_price"`
			SDPrice     float64   `json:"sd_price"`
			PeakPrice   float64   `json:"peak_price"`
			BottomPrice float64   `json:"bottom_price"`
			BuyAt       time.Time `json:"buy_at"`
		}

		// Query prices
		assets, err := func(ctx context.Context, stockDict map[string]*AssetBookValue, bInterest float64) ([]*Assets, error) {
			tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
			var possessions []*Assets
			stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT id, price, open_price, mean_52w, sd_price_52w FROM stocks WHERE id LIKE ANY($1)")
			if err != nil {
				return nil, echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			rs, err := stmt.QueryContext(c.Request().Context(), pq.StringArray(stocks))
			if err != nil {
				return nil, echo.NewHTTPError(http.StatusInternalServerError, err)
			}

			defer rs.Close()
			for rs.Next() {
				asset := &Assets{}
				err := rs.Scan(&asset.ID, &asset.Price, &asset.OpenPrice, &asset.MeanPrice, &asset.SDPrice)
				if err != nil {
					return nil, echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				asset.Dividend = stockDict[asset.ID].dividend
				asset.BookPrice = (stockDict[asset.ID].bookValue - stockDict[asset.ID].dividend) / stockMap[asset.ID].quantity
				asset.BuyAt = stockDict[asset.ID].buyAt
				// Calculate threshold prices
				asset.PeakPrice = asset.BookPrice * (1 + bankInterest)
				asset.BottomPrice = asset.OpenPrice - asset.MeanPrice
				if asset.Price >= asset.PeakPrice || asset.Price <= asset.BottomPrice {
					possessions = append(possessions, asset)
				}
			}
			return possessions, nil
		}(c.Request().Context(), stockMap, bankInterest)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, assets)
	}
}

func GetAssetFairValues(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		user := c.Request().Context().Value(constants.ContextUserKey).(*model.UserInfo)

		var stocks []string
		type AssetBookValue struct {
			quantity  float64
			bookValue float64
			dividend  float64
			buyAt     time.Time
		}
		stockMap := make(map[string]*AssetBookValue)

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
		// Query Recently Stocks
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock, quantity, book_value, buy_at FROM current_portfolios WHERE user_id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs1, err := stmt.QueryContext(c.Request().Context(), user.ID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		defer rs1.Close()
		for rs1.Next() {
			var (
				assetID   string
				quantity  float64
				bookValue float64
				buyAt     time.Time
			)
			err := rs1.Scan(&assetID, &quantity, &bookValue, &buyAt)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			stocks = append(stocks, assetID)
			stockMap[assetID] = &AssetBookValue{
				quantity:  quantity,
				bookValue: bookValue,
				buyAt:     buyAt,
			}
		}

		// Query dividend
		for stock, asset := range stockMap {
			stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT sum(profits) FROM pl_portfolios WHERE user_id = $1 AND stock = $2 AND record_at >= $3 AND source = $4 GROUP BY stock")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			r := stmt.QueryRowContext(c.Request().Context(), user.ID, stock, asset.buyAt, constants.AssetCategories.DIVIDEND)
			var totalDiv float64
			err = r.Scan(&totalDiv)
			if err != nil {
				if err == sql.ErrNoRows {
					continue
				}
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			asset.dividend = totalDiv
		}

		// Query prices
		stmt, err = tx.PrepareContext(c.Request().Context(), "SELECT id, price, dividend, mean_dividend FROM stocks WHERE id LIKE ANY($1)")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs2, err := stmt.QueryContext(c.Request().Context(), pq.StringArray(stocks))
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		type Assets struct {
			ID             string    `json:"code"`
			Price          float64   `json:"price"`
			Dividend       float64   `json:"dividend"`
			MeanDividend   float64   `json:"mean_dividend"`
			NormalBuyPrice float64   `json:"normal_buy_price"`
			Amount         float64   `json:"amount"`
			FairValue      float64   `json:"fair_value"`
			BookValue      float64   `json:"book_value"`
			TotalDividend  float64   `json:"total_dividend"`
			BuyAt          time.Time `json:"buy_at"`
		}
		var assets []*Assets
		defer rs2.Close()
		for rs2.Next() {
			asset := &Assets{}
			err := rs2.Scan(&asset.ID, &asset.Price, &asset.Dividend, &asset.MeanDividend)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			asset.Amount = stockMap[asset.ID].quantity
			asset.BookValue = stockMap[asset.ID].bookValue
			asset.TotalDividend = stockMap[asset.ID].dividend
			asset.FairValue = asset.Price * stockMap[asset.ID].quantity
			asset.BuyAt = stockMap[asset.ID].buyAt
			if asset.Amount > 0 {
				asset.NormalBuyPrice = asset.BookValue / asset.Amount
			}
			assets = append(assets, asset)
		}
		// Create cash asset
		stmt, err = tx.PrepareContext(c.Request().Context(), "SELECT SUM(profits) FROM pl_portfolios WHERE user_id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		r := stmt.QueryRowContext(c.Request().Context(), user.ID)
		var totalCash float64
		err = r.Scan(&totalCash)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		cash := &Assets{
			ID:        strings.ToUpper(constants.AssetCategories.CASH),
			FairValue: totalCash,
			BookValue: 0.0,
		}
		assets = append(assets, cash)

		return c.JSON(http.StatusOK, assets)
	}
}

func GetEventTimeline(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		user := c.Request().Context().Value(constants.ContextUserKey).(*model.UserInfo)

		type eventRequest struct {
			Code string `query:"code" validate:"required"`
		}
		eventReq := new(eventRequest)

		err := c.Bind(eventReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}
		type Events struct {
			StockCode string    `json:"code"`
			Category  string    `json:"category"`
			Quantity  float64   `json:"quantity"`
			Price     float64   `json:"price"`
			Tax       float64   `json:"tax"`
			BuyAt     time.Time `json:"buy_at"`
		}
		var events []*Events

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
		var rs *sql.Rows
		// Query Stock EVENTs
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock_id, category, quantity, price, tax, buy_at FROM portfolios WHERE user_id = $1 AND stock_id = $2 ORDER BY buy_at DESC")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs, err = stmt.QueryContext(c.Request().Context(), user.ID, eventReq.Code)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		defer rs.Close()
		for rs.Next() {
			event := new(Events)
			err := rs.Scan(&event.StockCode, &event.Category, &event.Quantity, &event.Price, &event.Tax, &event.BuyAt)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			events = append(events, event)
		}

		return c.JSON(http.StatusOK, events)
	}
}

func GetCashEventTimeline(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		user := c.Request().Context().Value(constants.ContextUserKey).(*model.UserInfo)
		type Events struct {
			StockCode string    `json:"code"`
			Category  string    `json:"category"`
			Profit    float64   `json:"quantity"`
			RecordAt  time.Time `json:"record_at"`
		}
		var events []*Events

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
		var rs *sql.Rows
		// Query Stock EVENTs
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock, profits, source, record_at FROM pl_portfolios WHERE user_id = $1 ORDER BY record_at DESC")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs, err = stmt.QueryContext(c.Request().Context(), user.ID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		defer rs.Close()
		for rs.Next() {
			event := new(Events)
			err := rs.Scan(&event.StockCode, &event.Profit, &event.Category, &event.RecordAt)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			events = append(events, event)
		}

		return c.JSON(http.StatusOK, events)
	}
}

func GetAssetDistribution(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type assetRequest struct {
			Code string `json:"code" validate:"required"`
		}
		assReq := new(assetRequest)

		err := c.Bind(assReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(assReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		now := time.Now()
		before52w := now.AddDate(-1, 0, 0)

		codes := []string{assReq.Code}
		X, err := util.AccumulatePriceXSampling(s.DB, s.PriceFileDB, codes, before52w, now)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		n, _ := X.Dims()
		var accRates []float64
		for i := 0; i < n; i++ {
			accRates = append(accRates, X.At(i, 0))
		}

		mean, sd, zscore := util.CalSlopeSigma(accRates, false)
		return c.JSON(http.StatusOK, map[string]interface{}{
			"return":             accRates[n-1],
			"mean":               mean,
			"standard_deviation": sd,
			"zscore":             zscore,
		})
	}
}

func GetPortfolioVariance(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type assetDetail struct {
			Code   string  `json:"code" validate:"required"`
			Weight float64 `json:"weight"`
		}
		assReq := make([]assetDetail, 0)

		// Bug https://github.com/labstack/echo/issues/1565
		// Use custom binding
		if err := (&echo.DefaultBinder{}).BindBody(c, &assReq); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		var assets []string
		W := mat.NewVecDense(len(assReq), nil)
		for i, ad := range assReq {
			if err := c.Validate(ad); err != nil {
				return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
			}
			assets = append(assets, ad.Code)
			W.SetVec(i, ad.Weight)
		}

		now := time.Now()
		before52w := now.AddDate(-1, 0, 0)
		X, err := util.AccumulatePriceXSampling(s.DB, s.PriceFileDB, assets, before52w, now)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Calculate covariance
		coVar := util.CalCovariance(X, W)

		// fWX := mat.Formatted(&Var, mat.Prefix("    "), mat.Squeeze())
		// fmt.Printf("WX = %v\n", fWX)
		return c.JSON(http.StatusOK, map[string]interface{}{
			"var": coVar,
		})
	}
}
