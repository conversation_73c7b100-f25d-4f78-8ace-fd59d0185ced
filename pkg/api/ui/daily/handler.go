package dailyui

import (
	"database/sql"
	"encoding/json"
	"net/http"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/google/uuid"

	"github.com/labstack/echo/v4"
)

func UserDailyList(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		user := c.Request().Context().Value(constants.ContextUserKey).(*model.UserInfo)
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT id, time, description, command, dates FROM daily WHERE user_id=$1 ORDER BY time ASC")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs, err := stmt.QueryContext(c.Request().Context(), user.ID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		type DailyModel struct {
			ID          string `json:"id"`
			Time        string `json:"time"`
			Description string `json:"description"`
			Command     bool   `json:"command"`
			Dates       []int  `json:"dates"`
		}
		items := make([]DailyModel, 0)
		for rs.Next() {
			var dateStr []byte
			item := DailyModel{}
			if err := rs.Scan(&item.ID, &item.Time, &item.Description, &item.Command, &dateStr); err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			err := json.Unmarshal(dateStr, &item.Dates)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			items = append(items, item)
		}
		defer rs.Close()

		return c.JSON(http.StatusOK, items)
	}
}

func DeleteDaily(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		user := c.Request().Context().Value(constants.ContextUserKey).(*model.UserInfo)
		type scheduleRequest struct {
			ID string `json:"id" validate:"required"`
		}
		schReq := new(scheduleRequest)
		err := c.Bind(schReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(schReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		stmt, err := tx.PrepareContext(c.Request().Context(), "DELETE FROM daily WHERE id=$1 AND user_id=$2")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		_, err = stmt.ExecContext(c.Request().Context(), schReq.ID, user.ID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}

func ModifyDaily(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		user := c.Request().Context().Value(constants.ContextUserKey).(*model.UserInfo)
		type scheduleRequest struct {
			Time        string `json:"time" validate:"required"`
			Description string `json:"description" validate:"required"`
			IsCommand   bool   `json:"is_command"`
			Dates       []int  `json:"dates"`
		}
		schReq := new(scheduleRequest)

		err := c.Bind(schReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(schReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		stmt, err := tx.PrepareContext(c.Request().Context(), "DELETE FROM daily WHERE time=$1 AND user_id=$2")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		_, err = stmt.ExecContext(c.Request().Context(), schReq.Time, user.ID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		stmt, err = tx.PrepareContext(c.Request().Context(), "INSERT INTO daily(id, user_id, time, description, command, dates) VALUES ($1,$2,$3,$4,$5,$6)")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		dateStr, err := json.Marshal(schReq.Dates)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		_, err = stmt.ExecContext(c.Request().Context(), uuid.New().String(), user.ID, schReq.Time, schReq.Description, schReq.IsCommand, dateStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}
