package portfolio

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/labstack/echo/v4"
)

func RecentlyUpsert(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type Activity struct {
			ChatID         string                                    `json:"chat_id" validate:"required"`
			Accumulation   map[string]*model.PorfolioAccumulateStock `json:"accumulation"`
			ProfitsNLosses []model.PortfolioProfitNLoss              `json:"pnl"`
		}

		activityReq := new(Activity)

		err := c.Bind(activityReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(activityReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		stmt, err := tx.PrepareContext(c.Request().Context(), "DELETE FROM current_portfolios WHERE user_id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		_, err = stmt.ExecContext(c.Request().Context(), activityReq.ChatID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		for code, stock := range activityReq.Accumulation {
			if stock.Quantity != 0 {
				stmt, err = tx.PrepareContext(c.Request().Context(), "INSERT INTO current_portfolios(user_id,stock,quantity,book_value,taxed,buy_at) VALUES($1,$2,$3,$4,$5,$6)")
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				_, err = stmt.ExecContext(c.Request().Context(), activityReq.ChatID, code, stock.Quantity, stock.BookValue, stock.Taxed, stock.LatestBoughtAt)
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
			}
		}
		return c.JSON(http.StatusOK, map[string]interface{}{
			"accumulation": activityReq.Accumulation,
			"pnl":          activityReq.ProfitsNLosses,
		})
	}
}

func ProfitsAndLossesCreateJob(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type Portfolio struct {
			ChatID string `json:"chat_id" query:"chat_id" validate:"required"`
		}

		portfolioReq := new(Portfolio)

		err := c.Bind(portfolioReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(portfolioReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT id, buy_at FROM portfolios WHERE user_id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		rs, err := stmt.QueryContext(c.Request().Context(), portfolioReq.ChatID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		type PortfolioActivity struct {
			ID   string
			Date time.Time
		}
		var activities []*PortfolioActivity
		for rs.Next() {
			activity := new(PortfolioActivity)
			err := rs.Scan(&activity.ID, &activity.Date)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			activities = append(activities, activity)
		}
		sort.Slice(activities, func(i, j int) bool {
			return activities[i].Date.Before(activities[j].Date)
		})

		err = rs.Close()
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Build map
		jobMap := make(map[int]string, len(activities))
		for i, activity := range activities {
			intentParams, err := json.Marshal(map[string]interface{}{
				"portfolio_id": activity.ID,
			})
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			jobMap[i] = fmt.Sprintf("portfolio_gain_loss_activity(%s)", intentParams)
		}

		// Create empty data avoid crash job
		return c.JSON(http.StatusOK, map[string]interface{}{
			constants.JobDefination.EXPANDING: jobMap,
			"accumulation":                    make(map[string]*model.PorfolioAccumulateStock),
			"pnl":                             make([]model.PortfolioProfitNLoss, 0),
		})
	}
}

func ProfitAndLostOfActivity(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type Activity struct {
			ChatID           string                                    `json:"chat_id" validate:"required"`
			PortfolioID      string                                    `json:"portfolio_id" validate:"required"`
			PrevAccumulation map[string]*model.PorfolioAccumulateStock `json:"accumulation"`
			ProfitsNLosses   []model.PortfolioProfitNLoss              `json:"pnl"`
		}

		activityReq := new(Activity)

		err := c.Bind(activityReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(activityReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock_id, category, buy_at, quantity, price, tax FROM portfolios WHERE id = $1 AND user_id = $2")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		r := stmt.QueryRowContext(c.Request().Context(), activityReq.PortfolioID, activityReq.ChatID)

		type PortfolioActivity struct {
			StockCode string
			Category  string
			Date      time.Time
			Quantity  float64
			UnitPrice float64
			Tax       float64
		}
		activity := new(PortfolioActivity)
		err = r.Scan(&activity.StockCode, &activity.Category, &activity.Date, &activity.Quantity, &activity.UnitPrice, &activity.Tax)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		newPNL := activityReq.ProfitsNLosses
		if newPNL == nil {
			newPNL = make([]model.PortfolioProfitNLoss, 0)
		}
		newContext := activityReq.PrevAccumulation
		if newContext == nil {
			newContext = make(map[string]*model.PorfolioAccumulateStock)
		}
		if activity.Category == constants.AssetCategories.DIVIDEND {
			dividendBeforeTax := activity.Quantity * activity.UnitPrice
			newPNL = append(newPNL, model.PortfolioProfitNLoss{
				Code:     activity.StockCode,
				Profit:   dividendBeforeTax * (1 - activity.Tax/100),
				Taxed:    dividendBeforeTax * (activity.Tax / 100),
				Recorded: activity.Date,
				Source:   constants.AssetCategories.DIVIDEND,
			})
		} else {
			if accumulatedStock, ok := newContext[activity.StockCode]; ok {
				prevQuantity := accumulatedStock.Quantity
				accumulatedStock.Quantity += activity.Quantity
				if activity.Quantity < 0 {
					// Cal profit
					sellValueBeforeTax := -activity.Quantity * activity.UnitPrice
					newContext[activity.StockCode].BookValue -= sellValueBeforeTax * (1 - activity.Tax/100)
					newContext[activity.StockCode].Taxed += sellValueBeforeTax * (activity.Tax / 100)
				} else {
					buyValueBeforeTax := activity.Quantity * activity.UnitPrice
					newContext[activity.StockCode].BookValue += buyValueBeforeTax * (1 + activity.Tax/100)
					newContext[activity.StockCode].Taxed += buyValueBeforeTax * (activity.Tax / 100)
				}
				if prevQuantity == 0 && activity.Quantity > 0 {
					newContext[activity.StockCode].LatestBoughtAt = activity.Date
				}
				if accumulatedStock.Quantity <= 0 || accumulatedStock.BookValue <= 0 {
					// Reset the value and write the profit
					newPNL = append(newPNL, model.PortfolioProfitNLoss{
						Code:     activity.StockCode,
						Profit:   -accumulatedStock.BookValue,
						Taxed:    accumulatedStock.Taxed,
						Recorded: activity.Date,
						Source:   constants.AssetCategories.STOCK,
					})
					newContext[activity.StockCode].BookValue = 0
					newContext[activity.StockCode].Taxed = 0
					newContext[activity.StockCode].LatestBoughtAt = activity.Date
				}
			} else {
				buyValueBeforeTax := activity.Quantity * activity.UnitPrice
				newContext[activity.StockCode] = &model.PorfolioAccumulateStock{
					Quantity:       activity.Quantity,
					BookValue:      buyValueBeforeTax * (1 + activity.Tax/100),
					Taxed:          buyValueBeforeTax * (activity.Tax / 100),
					LatestBoughtAt: activity.Date,
				}
			}
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"accumulation": newContext,
			"pnl":          newPNL,
		})
	}
}

func ProfitAndLostUpsert(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type Activity struct {
			ChatID         string                                    `json:"chat_id" validate:"required"`
			Accumulation   map[string]*model.PorfolioAccumulateStock `json:"accumulation"`
			ProfitsNLosses []model.PortfolioProfitNLoss              `json:"pnl"`
		}

		activityReq := new(Activity)

		err := c.Bind(activityReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(activityReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		stmt, err := tx.PrepareContext(c.Request().Context(), "DELETE FROM pl_portfolios WHERE user_id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		_, err = stmt.ExecContext(c.Request().Context(), activityReq.ChatID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		for _, stock := range activityReq.ProfitsNLosses {
			stmt, err = tx.PrepareContext(c.Request().Context(), "INSERT INTO pl_portfolios(user_id,stock,profits,taxed,source,record_at) VALUES($1,$2,$3,$4,$5,$6)")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			_, err = stmt.ExecContext(c.Request().Context(), activityReq.ChatID, stock.Code, stock.Profit, stock.Taxed, stock.Source, stock.Recorded)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"accumulation": activityReq.Accumulation,
			"pnl":          activityReq.ProfitsNLosses,
		})
	}
}

func ProfitAndLostInsert(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type Activity struct {
			ChatID         string                                    `json:"chat_id" validate:"required"`
			Accumulation   map[string]*model.PorfolioAccumulateStock `json:"accumulation"`
			ProfitsNLosses []*model.PortfolioProfitNLoss             `json:"pnl"`
		}

		activityReq := new(Activity)

		err := c.Bind(activityReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(activityReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		for _, stock := range activityReq.ProfitsNLosses {
			stmt, err := tx.PrepareContext(c.Request().Context(), "INSERT INTO pl_portfolios(user_id,stock,profits,taxed,source,record_at) VALUES($1,$2,$3,$4,$5,$6)")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			_, err = stmt.ExecContext(c.Request().Context(), activityReq.ChatID, stock.Code, stock.Profit, stock.Taxed, stock.Source, stock.Recorded)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"accumulation": activityReq.Accumulation,
			"pnl":          activityReq.ProfitsNLosses,
		})
	}
}
