package portfolio

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"net/http"
	"regexp"
	"strconv"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/labstack/echo/v4"
)

func DownloadPortfolioDividendRecently(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type Portfolio struct {
			ChatID string `json:"chat_id" query:"chat_id" validate:"required"`
		}

		portfolioReq := new(Portfolio)

		err := c.Bind(portfolioReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(portfolioReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock,buy_at FROM current_portfolios WHERE user_id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs, err := stmt.QueryContext(c.Request().Context(), portfolioReq.ChatID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		defer rs.Close()
		type RequestDownloadDivend struct {
			ChatID   string     `json:"chat_id"`
			Code     string     `json:"code"`
			FromDate *time.Time `json:"from_date"`
			ToDate   *time.Time `json:"to_date"`
		}

		var jobs []*model.NanoJob

		for rs.Next() {
			var (
				stock string
				buyAt time.Time
			)
			if err = rs.Scan(&stock, &buyAt); err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			req := &RequestDownloadDivend{
				ChatID:   portfolioReq.ChatID,
				Code:     stock,
				FromDate: &buyAt,
			}
			b, err := json.Marshal(req)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			// Insert new job fetch dividend
			job := &model.NanoJob{
				Url:    fmt.Sprintf("http://localhost:%s/v1/dividend/download", s.Config.HTTPPort),
				Params: string(b),
				Method: http.MethodPost,
				Owner:  portfolioReq.ChatID,
			}
			jobs = append(jobs, job)
		}

		for _, job := range jobs {
			err := s.Pacman.IngestJob(c.Request().Context(), job)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}
		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}

func DividendAggregation(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type PortfolioYear struct {
			ChatID string `json:"chat_id" query:"chat_id" validate:"required"`
			Year   string `json:"year" query:"year" validate:"required"`
		}

		portfolioYearReq := new(PortfolioYear)

		err := c.Bind(portfolioYearReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(portfolioYearReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		type StockDividend struct {
			Stock          string  `json:"code"`
			Dividend       float64 `json:"dividend"`
			Year           string  `json:"year"`
			DividendFormat string  `json:"dividend_format"`
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)

		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock, profits FROM pl_portfolios WHERE user_id = $1 AND EXTRACT(YEAR FROM record_at) = $2 AND source = $3")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs, err := stmt.QueryContext(c.Request().Context(), portfolioYearReq.ChatID, portfolioYearReq.Year, constants.AssetCategories.DIVIDEND)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		defer rs.Close()
		stocks := make(map[string]float64)
		for rs.Next() {
			var (
				id    string
				total float64
			)
			err := rs.Scan(&id, &total)
			if err != nil {
				if err != sql.ErrNoRows {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				return c.JSON(http.StatusOK, map[string]interface{}{
					"divs":         []StockDividend{},
					"total":        0,
					"total_format": 0,
				})
			}
			stocks[id] += total
		}

		total := 0.0
		var stockDivs []StockDividend
		for id, dividend := range stocks {
			stockDivs = append(stockDivs, StockDividend{
				Stock:          id,
				Dividend:       dividend,
				Year:           portfolioYearReq.Year,
				DividendFormat: s.Telegram.NumberFormat(int64(dividend)),
			})
			total += dividend
		}
		return c.JSON(http.StatusOK, map[string]interface{}{
			"divs": stockDivs,
			"all": []map[string]interface{}{
				{
					"total":        total,
					"total_format": s.Telegram.NumberFormat(int64(total)),
				},
			},
		})
	}
}

func DownloadStockDividends(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type Stocks struct {
			ChatID   string     `json:"chat_id" validate:"required"`
			Code     string     `json:"code" validate:"required"`
			FromDate *time.Time `json:"from_date" validate:"required"`
			ToDate   *time.Time `json:"to_date"`
		}

		stockReq := new(Stocks)

		err := c.Bind(stockReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(stockReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)

		// Finding last dividend downloaded
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock_id, buy_at FROM portfolios WHERE user_id = $1 AND category = $2 AND stock_id = $3 ORDER BY buy_at DESC LIMIT 1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		r := stmt.QueryRowContext(c.Request().Context(), stockReq.ChatID, constants.AssetCategories.DIVIDEND, stockReq.Code)

		var (
			id       string
			date     time.Time
			lastDate time.Time
		)
		err = r.Scan(&id, &date)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		if errors.Is(err, sql.ErrNoRows) {
			lastDate = *stockReq.FromDate
		} else {
			lastDate = date
		}

		newDivs := make(map[string]float64)
		page := 1
		for {
			events, err := s.Vietstock.FetchDividendStock(c.Request().Context(), stockReq.Code, page)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
			}

			minTime := int64(0)
			regDate := regexp.MustCompile(`Date\(([^\)]+)`)
			for _, event := range events {
				subCutOffMatch := regDate.FindStringSubmatch(event.GDKHQDate)
				if len(subCutOffMatch) <= 1 {
					continue
				}
				cutOffTime, err := strconv.ParseInt(subCutOffMatch[1], 10, 64)
				if err != nil {
					return err
				}
				cutOffTime /= 1000
				subTimeMatch := regDate.FindStringSubmatch(event.Time)
				if len(subTimeMatch) <= 1 {
					continue
				}
				eventTime, err := strconv.ParseInt(subTimeMatch[1], 10, 64)
				if err != nil {
					return err
				}
				eventTime /= 1000
				rate, err := strconv.ParseFloat(event.Rate, 64)
				if err != nil {
					return err
				}
				div := rate / 100 * 10000

				if minTime == 0 || cutOffTime < minTime {
					minTime = cutOffTime
				}
				// fmt.Println(minTime, eventTime, lastDate.Unix(), rate)
				if minTime >= lastDate.Unix() {
					if stockReq.ToDate != nil && minTime > stockReq.ToDate.Unix() {
						continue
					}
					// Count total shares
					totalShares := 0
					stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock_id, quantity FROM portfolios WHERE stock_id = $1 AND user_id = $2 AND category = $3 AND buy_at < $4")
					if err != nil {
						return echo.NewHTTPError(http.StatusInternalServerError, err)
					}
					rs, err := stmt.QueryContext(c.Request().Context(), stockReq.Code, stockReq.ChatID, constants.AssetCategories.STOCK, time.Unix(eventTime, 0))
					if err != nil {
						return echo.NewHTTPError(http.StatusInternalServerError, err)
					}
					for rs.Next() {
						var assets string
						var quantity int
						err = rs.Scan(&assets, &quantity)
						if err != nil {
							return echo.NewHTTPError(http.StatusInternalServerError, err)
						}
						totalShares += quantity
					}
					rs.Close()
					// Insert portfolio
					stmt, err = tx.PrepareContext(c.Request().Context(), "INSERT INTO portfolios(id, stock_id, user_id, category, quantity, price, tax, buy_at) VALUES($1,$2,$3,$4,$5,$6,$7,$8)")
					if err != nil {
						return echo.NewHTTPError(http.StatusInternalServerError, err)
					}
					uuidKey, err := uuid.NewV7()
					_, err = stmt.ExecContext(c.Request().Context(), uuidKey.String(), stockReq.Code, stockReq.ChatID, constants.AssetCategories.DIVIDEND, totalShares, int64(div), 5, time.Unix(eventTime, 0))
					if err != nil {
						return echo.NewHTTPError(http.StatusInternalServerError, err)
					}
					// Inform
					newDivs[stockReq.Code] += float64(totalShares) * div * (1.0 - 5.0/100.0)
					// Insert portfolio gain & loss
					stmt, err = tx.PrepareContext(c.Request().Context(), "INSERT INTO pl_portfolios(user_id,stock,profits,source,record_at) VALUES($1,$2,$3,$4,$5)")
					if err != nil {
						return echo.NewHTTPError(http.StatusInternalServerError, err)
					}
					_, err = stmt.ExecContext(c.Request().Context(), stockReq.ChatID, stockReq.Code, newDivs[stockReq.Code], constants.AssetCategories.DIVIDEND, time.Unix(eventTime, 0))
					if err != nil {
						return echo.NewHTTPError(http.StatusInternalServerError, err)
					}
				}
			}
			if minTime < stockReq.FromDate.Unix() {
				break
			}
			page++
		}
		var jobs []*model.NanoJob
		for code, div := range newDivs {
			params := map[string]string{
				"chat_id": stockReq.ChatID,
				"message": fmt.Sprintf("Bạn có %sđ tiền cổ cổ tức từ %s", s.Telegram.NumberFormat(int64(div)), code),
			}
			paramData, err := json.Marshal(params)
			if err != nil {
				return err
			}
			job := &model.NanoJob{
				Url:    fmt.Sprintf("http://localhost:%s/v1/channel/ask", s.Config.HTTPPort),
				Params: string(paramData),
				Method: http.MethodPost,
				Owner:  stockReq.ChatID,
			}
			jobs = append(jobs, job)

		}
		for _, job := range jobs {
			// Insert new job inform user dividend
			err = s.Pacman.IngestJob(c.Request().Context(), job)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}

		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}
