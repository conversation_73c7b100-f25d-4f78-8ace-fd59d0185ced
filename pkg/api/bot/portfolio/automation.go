package portfolio

import (
	"context"
	"database/sql"
	"errors"
	"math"
	"net/http"
	"sort"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/007lock/simon-homestead/pkg/util"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
	"gonum.org/v1/gonum/mat"
)

func CalculateWeight(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type ChatModel struct {
			ChatID string `json:"chat_id" query:"chat_id" validate:"required"`
		}

		chatReq := new(ChatModel)

		err := c.Bind(chatReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(chatReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		type AssetBookValue struct {
			quantity  float64
			dividend  float64
			bookValue float64
			taxed     float64
			buyAt     string
		}
		stockMap := make(map[string]*AssetBookValue)

		// Query fair value assets
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock, quantity, book_value, taxed, buy_at FROM current_portfolios WHERE user_id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs, err := stmt.QueryContext(c.Request().Context(), chatReq.ChatID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		defer rs.Close()

		for rs.Next() {
			var (
				stockCode string
				quantity  float64
				bookValue float64
				taxed     float64
				buyAt     string
			)
			err := rs.Scan(&stockCode, &quantity, &bookValue, &taxed, &buyAt)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}

			stockMap[stockCode] = &AssetBookValue{
				bookValue: bookValue,
				quantity:  quantity,
				taxed:     taxed,
				buyAt:     buyAt,
			}
		}
		total := 0.0
		// Decrease price by dividend
		for stock, asset := range stockMap {
			total += asset.bookValue
			stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT sum(profits), sum(taxed) FROM pl_portfolios WHERE user_id = $1 AND stock = $2 AND record_at >= $3 AND source = $4 GROUP BY stock")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			r := stmt.QueryRowContext(c.Request().Context(), chatReq.ChatID, stock, asset.buyAt, constants.AssetCategories.DIVIDEND)
			var (
				totalDiv float64
				taxed    float64
			)
			err = r.Scan(&totalDiv, &taxed)
			if err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					continue
				}
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			stockMap[stock].bookValue = asset.bookValue - totalDiv
			stockMap[stock].taxed = asset.taxed + taxed
			stockMap[stock].dividend = totalDiv
			total -= totalDiv
		}
		var assets []*model.PortfolioWeighted
		totalW := 0.0
		for stock, asset := range stockMap {
			assets = append(assets, &model.PortfolioWeighted{
				Code:      stock,
				Quantity:  asset.quantity,
				Weight:    asset.bookValue / total,
				BookValue: asset.bookValue,
				Taxed:     asset.taxed,
				BookPrice: asset.bookValue / asset.quantity,
				BoughtAt:  asset.buyAt,
				Dividend:  asset.dividend,
			})
			totalW += asset.bookValue / total
		}
		return c.JSON(http.StatusOK, map[string]interface{}{
			"assets":       assets,
			"total_weight": totalW,
			"total_value":  total,
		})
	}
}

func CalculateSale(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type PortfolioAsset struct {
			ChatID string                     `json:"chat_id" required:"true"`
			Assets []*model.PortfolioWeighted `json:"assets" required:"true"`
		}
		assReq := new(PortfolioAsset)

		err := c.Bind(assReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(assReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		var fassets []*model.PortfolioFairAsset

		// query bank interest
		bankInterest, err := func(ctx context.Context) (float64, error) {
			tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
			r := tx.QueryRowContext(c.Request().Context(), "SELECT six_month_interest FROM bank_interests ORDER BY created_at DESC LIMIT 1")
			sixMonthInterest := 0.0
			err := r.Scan(&sixMonthInterest)
			if err != nil && !errors.Is(err, sql.ErrNoRows) {
				return 0, echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			return sixMonthInterest, nil
		}(c.Request().Context())
		if err != nil {
			return err
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		for _, asset := range assReq.Assets {
			stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT price, open_price, current_lowest_price, current_highest_price, expected_value_1y, dividend, mean_52w, sd_price_52w FROM stocks WHERE id = $1")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			row := stmt.QueryRowContext(c.Request().Context(), asset.Code)
			var (
				price               float64
				openPrice           float64
				currentLowestPrice  float64
				currentHighestPrice float64
				meanPrice           float64
				sdPrice             float64
				expectedValue       float64
				dividend            float64
			)
			err = row.Scan(&price, &openPrice, &currentLowestPrice, &currentHighestPrice, &expectedValue, &dividend, &meanPrice, &sdPrice)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			peakPrice := max(asset.BookPrice*(1+bankInterest), currentHighestPrice)
			bottomPrice := max(asset.BookPrice-meanPrice, currentLowestPrice)

			sellTaxRatioPredict := asset.Taxed / asset.BookValue
			// calculate tax loss of predicted price
			profitPredict := peakPrice - asset.BookPrice
			sellTaxPredict := peakPrice * sellTaxRatioPredict
			sellTaxRatioAtPeak := sellTaxPredict * 2 / profitPredict // buy & sell taxed prediction

			fassets = append(fassets, &model.PortfolioFairAsset{
				Code:                asset.Code,
				Weight:              s.Telegram.NumberFormat(asset.Weight * 100),
				Quantity:            s.Telegram.NumberFormat(int64(asset.Quantity)),
				PeakPrice:           s.Telegram.NumberFormat(int64(peakPrice)),
				TaxedAtPeak:         s.Telegram.NumberFormat(sellTaxRatioAtPeak * 100),
				PeakPriceNumber:     peakPrice,
				BottomPrice:         s.Telegram.NumberFormat(int64(bottomPrice)),
				BottomPriceNumber:   bottomPrice,
				BookValue:           s.Telegram.NumberFormat(int64(asset.BookValue)),
				BookPriceNumber:     asset.BookPrice,
				BookPrice:           s.Telegram.NumberFormat(int64(asset.BookPrice)),
				PEx:                 s.Telegram.NumberFormat(price / (expectedValue + dividend*(1-0.05))),
				Dividend:            s.Telegram.NumberFormat(int64(asset.Dividend)),
				Profit:              s.Telegram.NumberFormat(((price / asset.BookPrice) - 1) * 100),
				OpenPrice:           s.Telegram.NumberFormat(int64(openPrice)),
				HighestPrice:        s.Telegram.NumberFormat(int64(currentHighestPrice)),
				LowestPrice:         s.Telegram.NumberFormat(int64(currentLowestPrice)),
				FairPrice:           s.Telegram.NumberFormat(price),
				MeanPriceVolatility: s.Telegram.NumberFormat(meanPrice),
				PriceRisk:           s.Telegram.NumberFormat(sdPrice),
				IsGain:              asset.BookPrice < price,
			})
		}
		return c.JSON(http.StatusOK, map[string]interface{}{
			"suggestion": fassets,
		})
	}
}

func CalculateVariance(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type PortfolioWeighted struct {
			Code      string  `json:"code" required:"true"`
			Weight    float64 `json:"weight" required:"true"`
			BookValue float64 `json:"book_value"`
		}

		type PortfolioAsset struct {
			Assets []*PortfolioWeighted `json:"assets" required:"true"`
		}
		assReq := new(PortfolioAsset)

		err := c.Bind(assReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(assReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		var assets []string
		W := mat.NewVecDense(len(assReq.Assets), nil)
		for i, ad := range assReq.Assets {
			if err := c.Validate(ad); err != nil {
				return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
			}
			assets = append(assets, ad.Code)
			W.SetVec(i, ad.Weight)
		}

		now := time.Now()
		before52w := now.AddDate(-1, 0, 0)
		X, err := util.AccumulatePriceXSampling(s.DB, s.PriceFileDB, assets, before52w, now)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Calculate return
		m, n := X.Dims()
		returnRate := 0.0
		for i := 0; i < n; i++ {
			returnRate += (W.AtVec(i) * X.At(m-1, i))
		}

		// Calculate covariance
		coVar := util.CalCovariance(X, W)

		// fWX := mat.Formatted(&Var, mat.Prefix("    "), mat.Squeeze())
		// fmt.Printf("WX = %v\n", fWX)
		return c.JSON(http.StatusOK, map[string]interface{}{
			"assets":        assReq.Assets,
			"return":        returnRate,
			"return_format": s.Telegram.NumberFormat(int64(returnRate * 100)),
			"var":           coVar,
			"var_format":    s.Telegram.NumberFormat(int64(coVar * 100)),
		})
	}
}

func CalculateNewVelocity(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type PortfolioWeighted struct {
			Code      string  `json:"code" required:"true"`
			Weight    float64 `json:"weight" required:"true"`
			Velocity  float64 `json:"velocity"`
			BookValue float64 `json:"book_value" required:"true"`
			BookPrice float64 `json:"book_price" required:"true"`
			BoughtAt  string  `json:"bought_at" required:"true"`
			Dividend  float64 `json:"dividend"`
		}

		type PortfolioVar struct {
			Assets []PortfolioWeighted `json:"assets" required:"true"`
		}
		assReq := new(PortfolioVar)

		err := c.Bind(assReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(assReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		sixMonthInterested := 0.0
		r := tx.QueryRowContext(c.Request().Context(), "SELECT six_month_interest FROM bank_interests ORDER BY created_at DESC LIMIT 1")
		err = r.Scan(&sixMonthInterested)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		var assets []string
		total := 0.0
		for _, stock := range assReq.Assets {
			assets = append(assets, stock.Code)
			total += stock.BookValue
		}
		now := time.Now()
		before52w := now.AddDate(-1, 0, 0)
		X, err := util.AccumulatePriceXSampling(s.DB, s.PriceFileDB, assets, before52w, now)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		m, _ := X.Dims()
		Y := mat.NewVecDense(m, nil)
		yoyRate := sixMonthInterested / float64(m) // Default bank interest
		for i := 0; i < m; i++ {
			// Calculate cumulative return.
			if i == 0 {
				// Initial rate
				Y.SetVec(i, yoyRate)
			} else {
				Y.SetVec(i, Y.AtVec(i-1)+yoyRate)
			}
		}
		// fY := mat.Formatted(Y, mat.Prefix("    "), mat.Squeeze())
		// fmt.Printf("WX = %v\n", fY)
		weights := util.RegressionWeight(X, Y)
		// Scale weights to 100%
		scaledWeights := util.PropotionWeightVector(weights, 1)
		// Scale velocity
		velocities := util.StandardizedWeightVector(X, Y, weights)

		for i := range scaledWeights {
			assReq.Assets[i].Velocity = velocities[i]
		}
		// Sort velocity desc
		sort.SliceStable(assReq.Assets, func(i, j int) bool {
			return assReq.Assets[i].Velocity > assReq.Assets[j].Velocity
		})
		// fWX := mat.Formatted(&Var, mat.Prefix("    "), mat.Squeeze())
		// fmt.Printf("WX = %v\n", fWX)
		return c.JSON(http.StatusOK, map[string]interface{}{
			"assets": assReq.Assets,
		})
	}
}

func CalculateBalance(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type PortfolioBalanceSuggest struct {
			Code         string `json:"code"`
			Velocity     string `json:"velocity"`
			Weight       string `json:"weight"`
			Price        string `json:"price"`
			Amount       string `json:"amount"`
			Remain       string `json:"remain"`
			IsShort      bool   `json:"is_short"`
			GrahamValue  string `json:"graham_value"`
			ProfitMargin string `json:"profit_margin"`
		}
		type PortfolioWeighted struct {
			Code      string  `json:"code" required:"true"`
			Velocity  float64 `json:"velocity" required:"true"`
			Weight    float64 `json:"weight" required:"true"`
			BookValue float64 `json:"book_value" required:"true"`
		}
		type PortfolioVar struct {
			Assets     []PortfolioWeighted `json:"assets" required:"true"`
			NewWeights []PortfolioWeighted `json:"new_weights" required:"true"`
			Var        float64             `json:"var" required:"true"`
		}
		assReq := new(PortfolioVar)

		err := c.Bind(assReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(assReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		var assets []string
		oldAssets := make(map[string]float64)
		for _, stock := range assReq.Assets {
			assets = append(assets, stock.Code)
			oldAssets[stock.Code] = stock.BookValue
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		// Query prices
		stmtDB, err := tx.Prepare("SELECT id, price, graham_value FROM stocks WHERE id LIKE ANY($1)")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rsDB, err := stmtDB.QueryContext(c.Request().Context(), pq.StringArray(assets))
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		mapAssetPrice := make(map[string]float64)
		mapAssetGrahamValue := make(map[string]float64)
		for rsDB.Next() {
			var (
				code   string
				price  float64
				gValue float64
			)
			err := rsDB.Scan(&code, &price, &gValue)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			mapAssetPrice[code] = price
			mapAssetGrahamValue[code] = gValue
		}
		defer rsDB.Close()
		var suggestion []PortfolioBalanceSuggest
		for _, nw := range assReq.NewWeights {
			remain := (oldAssets[nw.Code] - nw.BookValue) / mapAssetPrice[nw.Code]
			suggestion = append(suggestion, PortfolioBalanceSuggest{
				Code:     nw.Code,
				Velocity: s.Telegram.NumberFormat(int64(nw.Velocity * 100)),
				Weight:   s.Telegram.NumberFormat(int64(nw.Weight * 100)),
				Price:    s.Telegram.NumberFormat(int64(mapAssetPrice[nw.Code])),
				Amount:   s.Telegram.NumberFormat(int64(nw.BookValue / mapAssetPrice[nw.Code])),
				// Negative: long, Positive: short
				Remain:       s.Telegram.NumberFormat(int64(math.Abs(remain))),
				IsShort:      remain > 0,
				GrahamValue:  s.Telegram.NumberFormat(int64(mapAssetGrahamValue[nw.Code])),
				ProfitMargin: s.Telegram.NumberFormat(int64(((mapAssetGrahamValue[nw.Code] - mapAssetPrice[nw.Code]) / mapAssetPrice[nw.Code]) * 100)),
			})
		}

		// fWX := mat.Formatted(&Var, mat.Prefix("    "), mat.Squeeze())
		// fmt.Printf("WX = %v\n", fWX)
		return c.JSON(http.StatusOK, map[string]interface{}{
			"assets":      assReq.Assets,
			"new_weights": assReq.NewWeights,
			"suggestion":  suggestion,
			"var":         assReq.Var,
		})
	}
}
