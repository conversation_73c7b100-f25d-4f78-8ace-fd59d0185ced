package stock

import (
	"database/sql"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/analysis"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/007lock/simon-homestead/pkg/util"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
	"gonum.org/v1/gonum/floats"
	"gonum.org/v1/gonum/mat"
)

func FetchPriceStock(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type stockReqModel struct {
			StockCode string `json:"stock" validate:"required"`
		}
		stockReq := new(stockReqModel)

		err := c.Bind(stockReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(stockReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		stmt, err := tx.Prepare("SELECT id, updated_at FROM stocks WHERE id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		r := stmt.QueryRowContext(c.Request().Context(), stockReq.StockCode)
		var (
			id        string
			updatedAt time.Time
		)
		if err = r.Scan(&id, &updatedAt); err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		now := time.Now()
		if now.Sub(updatedAt) > s.Config.StockPriceCache*time.Hour {
			stock, err := s.Vietstock.FetchPriceStock(c.Request().Context(), stockReq.StockCode)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}

			if stock.AvrPrice != 0 && stock.OpenPrice != 0 && stock.LowestPrice != 0 && stock.HighestPrice != 0 {
				stmt, err = tx.Prepare("UPDATE stocks SET price = $1, open_price = $2, market_cap = $3, current_lowest_price = $4, current_highest_price =$5, shares = $6, pe = $7, pb = $8, dividend = $9, updated_at = $10 WHERE id = $11")
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				_, err = stmt.ExecContext(c.Request().Context(), stock.LastPrice, stock.OpenPrice, stock.MarketCapital/1000000000, stock.LowestPrice, stock.HighestPrice, stock.TotalRoom, stock.Pe, stock.Pb, stock.Dividend, time.Now(), stock.StockCode)
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
			}
		}
		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}

func FetchPriceStockList(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type FetchPriceRequest struct {
			ChatID string `json:"chat_id" validate:"required"`
		}

		fetchPriceReq := new(FetchPriceRequest)

		err := c.Bind(fetchPriceReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(fetchPriceReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		stocks, err := analysis.StocksFunnel(c.Request().Context())
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		now := time.Now()
		// Get latest year
		latestYear := 0
		for _, stock := range stocks {
			if stock.Year > latestYear {
				latestYear = stock.Year
			}
		}
		// Filter out stock does not have the latest year
		var filtered []*model.StockFilter
		for _, stock := range stocks {
			if stock.Year == latestYear {
				filtered = append(filtered, stock)
			}
		}
		for _, stock := range filtered {
			// Update old stock price
			if now.Sub(stock.UpdatedAt) > s.Config.StockPriceCache*time.Hour {
				// Insert new job fetch price
				jobCtx := fmt.Sprintf(`{"stock":"%s", "chat_id":"%s"}`, stock.Code, fetchPriceReq.ChatID)
				job := &model.NanoJob{
					Url:            fmt.Sprintf("http://localhost:%s/v1/stock/fetch-price", s.Config.HTTPPort),
					Params:         jobCtx,
					Method:         http.MethodPost,
					RequestContext: jobCtx,
					Owner:          fetchPriceReq.ChatID,
				}
				err := s.Pacman.IngestJobWithPriority(c.Request().Context(), job, true)
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
			}
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"stocks": filtered,
		})
	}
}

func FetchGradeStocks(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type gradeReqModel struct {
			Grade string `json:"grade" query:"grade" validate:"required,numeric"`
		}
		gradeReq := new(gradeReqModel)

		err := c.Bind(gradeReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(gradeReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)

		stmt, err := tx.Prepare("SELECT id, updated_at FROM stocks WHERE grade = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		var stocks []string
		rs, err := stmt.QueryContext(c.Request().Context(), gradeReq.Grade)
		if err != nil {
			if err == sql.ErrNoRows {
				return echo.NewHTTPError(http.StatusNotFound, constants.CommonError.ERROR_NOT_FOUND)
			}
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		defer rs.Close()
		for rs.Next() {
			var (
				stock        string
				updatedAtStr string
			)
			if err := rs.Scan(&stock, &updatedAtStr); err != nil {
				if err == sql.ErrNoRows {
					break
				}
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			stocks = append(stocks, stock)
		}
		return c.JSON(http.StatusOK, map[string]interface{}{
			"stocks": strings.Join(stocks, ","),
		})
	}
}

func FilterAndSendStock(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type filterReqModel struct {
			ChatID string              `json:"chat_id" validate:"required"`
			Stocks []model.StockFilter `json:"stocks" validate:"required"`
		}
		filterReq := new(filterReqModel)

		err := c.Bind(filterReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(filterReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}
		var stockCodes []string
		for _, stock := range filterReq.Stocks {
			stockCodes = append(stockCodes, stock.Code)
		}
		stocks, err := analysis.StockDetailsFunnel(c.Request().Context(), stockCodes, s.Telegram)
		sort.SliceStable(stocks, func(i, j int) bool {
			return stocks[i].PriceVolatilityNumber > stocks[j].PriceVolatilityNumber
		})
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"stock_list": stocks,
		})
	}
}

func CalculateStocksVar(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type filteredStocks struct {
			Stocklist []struct {
				StockCode              string `json:"stock_code"`
				NetSmooth              bool   `json:"net_smooth"`
				Stars                  string `json:"stars"`
				Price                  string `json:"price"`
				CurrentPercentDividend string `json:"current_percent_dividend"`
				PercentDividend        string `json:"percent_dividend"`
				ProfitMarginGraham     string `json:"profit_margin_graham"`
			} `json:"stock_list"`
		}
		filterReq := new(filteredStocks)

		err := c.Bind(filterReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		var assets []string
		for _, stock := range filterReq.Stocklist {
			assets = append(assets, stock.StockCode)
		}
		// fmt.Println(assets)
		now := time.Now()
		before52w := now.AddDate(-1, 0, 0)
		X, err := util.AccumulatePriceXSampling(s.DB, s.PriceFileDB, assets, before52w, now)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		_, n := X.Dims()
		wPercent := 1.0 / float64(n)
		W := mat.NewVecDense(n, nil)
		for i := 0; i < n; i++ {
			W.SetVec(i, wPercent)
		}
		// Calculate covariance
		coVar := util.CalCovariance(X, W)

		return c.JSON(http.StatusOK, map[string]interface{}{
			"var":        coVar,
			"stock_list": filterReq.Stocklist,
		})
	}
}

func CalculateStocksWeight(s *service.Service) echo.HandlerFunc {
	return func(ec echo.Context) error {
		type filteredStocks struct {
			Stocklist []struct {
				StockCode              string `json:"stock_code"`
				NetSmooth              bool   `json:"net_smooth"`
				Stars                  string `json:"stars"`
				Price                  string `json:"price"`
				CurrentPercentDividend string `json:"current_percent_dividend"`
				PercentDividend        string `json:"percent_dividend"`
				ProfitMarginGraham     string `json:"profit_margin_graham"`
				Weight                 string `json:"weight"`
				weightDecimal          float64
			} `json:"stock_list"`
			Var float64 `json:"var"`
		}
		filterReq := new(filteredStocks)

		err := ec.Bind(filterReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		var assets []string
		for _, stock := range filterReq.Stocklist {
			assets = append(assets, stock.StockCode)
		}
		now := time.Now()
		before52w := now.AddDate(-1, 0, 0)
		X, err := util.AccumulatePriceXSampling(s.DB, s.PriceFileDB, assets, before52w, now)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		r, c := X.Dims()

		tx := ec.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		sixMonthInterested := 0.0
		row := tx.QueryRowContext(ec.Request().Context(), "SELECT six_month_interest FROM bank_interests ORDER BY created_at DESC LIMIT 1")
		err = row.Scan(&sixMonthInterested)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		Y := mat.NewVecDense(r, nil)
		yoyRate := sixMonthInterested / float64(r)
		for i := 0; i < r; i++ {
			// Calculate cumulative return.
			if i == 0 {
				// Initial rate
				Y.SetVec(i, yoyRate)
			} else {
				Y.SetVec(i, Y.AtVec(i-1)+yoyRate)
			}
		}
		// fY := mat.Formatted(Y, mat.Prefix("    "), mat.Squeeze())
		// fmt.Printf("WX = %v\n", fY)
		// Check weight is zero
		sums := make([]float64, c)
		col := make([]float64, r)
		for i := 0; i < c; i++ {
			mat.Col(col, i, X)
			sums[i] = floats.Sum(col)
		}
		// Check sum zero
		var weightAssets []int
		for i, sum := range sums {
			if sum == 0 {
				filterReq.Stocklist[i].Weight = "0"
				filterReq.Stocklist[i].weightDecimal = 0
			} else {
				weightAssets = append(weightAssets, i)
			}
		}
	SLICEMATRIX:
		for i, sum := range sums {
			if sum == 0 {
				X = util.RemoveCol(i, X)
				sums = util.RemoveIndex(sums, i)
				goto SLICEMATRIX
			}
		}
		// Calculate weight
		weights := util.RegressionWeight(X, Y)
		scaledWeights := util.StandardizedWeightVector(X, Y, weights)

		for i, weight := range scaledWeights {
			// fmt.Println(filterReq.Stocklist[weightAssets[i]].StockCode)
			filterReq.Stocklist[weightAssets[i]].Weight = s.Telegram.NumberFormat(weight * 100)
			filterReq.Stocklist[weightAssets[i]].weightDecimal = weight
		}

		sort.SliceStable(filterReq.Stocklist, func(i, j int) bool {
			return filterReq.Stocklist[i].weightDecimal > filterReq.Stocklist[j].weightDecimal
		})

		return ec.JSON(http.StatusOK, map[string]interface{}{
			"var":        filterReq.Var,
			"stock_list": filterReq.Stocklist,
		})
	}
}

func SendStockList(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type stockListReqModel struct {
			Stocks string `json:"stocks" validate:"required"`
		}
		listReq := new(stockListReqModel)

		err := c.Bind(listReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(listReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
		// filter best grade stock

		stmt, err := tx.Prepare("SELECT id, company_name, grade, price, mean_dividend, graham_value FROM stocks WHERE id = ANY($1)")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs, err := stmt.QueryContext(c.Request().Context(), pq.StringArray(strings.Split(listReq.Stocks, ",")))
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		type StockGrades struct {
			Stock              string `json:"stock_code"`
			Name               string `json:"name"`
			Stars              string `json:"stars"`
			Price              string `json:"price"`
			PercentDividend    string `json:"percent_dividend"`
			ProfitmarginGraham string `json:"profit_margin_graham"`
		}
		var stocks []*StockGrades
		for rs.Next() {
			var (
				stock       string
				name        string
				grade       float64
				price       float64
				dividend    float64
				grahamValue float64
			)
			if err := rs.Scan(&stock, &name, &grade, &price, &dividend, &grahamValue); err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			stocks = append(stocks, &StockGrades{
				Stock:              stock,
				Name:               name,
				Stars:              s.Telegram.StarFormat(int(grade)),
				Price:              s.Telegram.NumberFormat(int(price)),
				PercentDividend:    s.Telegram.NumberFormat((dividend / price) * 100),
				ProfitmarginGraham: s.Telegram.NumberFormat((grahamValue/price - 1) * 100),
			})
		}
		defer rs.Close()

		return c.JSON(http.StatusOK, map[string]interface{}{
			"stock_list": stocks,
		})
	}
}

func GetStockCode(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type stockRequest struct {
			StockCode string `json:"stock" query:"stock" validate:"required"`
		}
		stockReq := new(stockRequest)

		err := c.Bind(stockReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(stockReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		db := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
		stmt, err := db.Prepare("SELECT id FROM stocks WHERE id LIKE UPPER($1)")
		if err != nil {
			return err
		}
		r := stmt.QueryRowContext(c.Request().Context(), stockReq.StockCode)
		var id string
		if err := r.Scan(&id); err != nil {
			if err == sql.ErrNoRows {
				return echo.NewHTTPError(http.StatusInternalServerError, constants.CommonError.ERROR_NOT_FOUND)
			}
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		return c.JSON(http.StatusOK, map[string]string{
			"stock": id,
		})
	}
}

func StockDetail(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type stockRequest struct {
			ChatID    string `json:"chat_id" query:"chat_id" validate:"required"`
			StockCode string `json:"stock" query:"stock" validate:"required"`
		}
		stockReq := new(stockRequest)

		err := c.Bind(stockReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(stockReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		db := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
		stmt, err := db.Prepare("SELECT id, company_name, rev_ratio_yoy, rev_ratio_qoq, rev_last_quarter, years, market_cap, grade, eps, bvps, expected_value_1y, dividend, mean_dividend, sd_price_52w, mean_52w, gross_profit_margin, interest_expense_ratio, admin_expense_ratio, cash_ratio, debt_cover_ratio, graham_value, price, open_price, net_smooth FROM stocks WHERE ID = $1")
		if err != nil {
			return err
		}
		if err != nil {
			return err
		}
		r := stmt.QueryRowContext(c.Request().Context(), stockReq.StockCode)
		var (
			id                   string
			name                 string
			revRatioYOY          float64
			revRatioQOQ          float64
			revQuarterly         string
			years                string
			marketCap            float64
			grade                int
			eps                  float64
			bvps                 float64
			expectedValue        float64
			dividend             float64
			mDividend            float64
			SDPrice52w           float64
			meanPrice52w         float64
			grossProfitMargin    float64
			interestExpenseRatio float64
			adminExpenseRatio    float64
			cashRatio            float64
			debtCoverRatio       float64
			grahamValue          float64
			price                float64
			openPrice            float64
			netSmooth            bool
		)
		if err := r.Scan(&id, &name, &revRatioYOY, &revRatioQOQ, &revQuarterly, &years, &marketCap, &grade, &eps, &bvps, &expectedValue, &dividend, &mDividend, &SDPrice52w, &meanPrice52w, &grossProfitMargin, &interestExpenseRatio, &adminExpenseRatio, &cashRatio, &debtCoverRatio, &grahamValue, &price, &openPrice, &netSmooth); err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Process years
		var yearsList []string
		for _, y := range strings.Split(years, ",") {
			yearsList = append(yearsList, strings.ReplaceAll(y, "`", ""))
		}

		token, err := s.JWTs.Sign(&model.UserInfo{ID: stockReq.ChatID})
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Price is up or down
		priceUp := openPrice < price

		// Red flags
		isNotGrowth := revRatioYOY < 0.05
		isNotGrowthQuarter := revRatioQOQ < 0.05
		PEx := price / (expectedValue + dividend)
		isPExRF := PEx > 20 || PEx < 0
		PE := price / eps
		isPERF := PE > 20
		PB := price / bvps
		isPBRF := PB > 2
		safeMargin := 1 - price/grahamValue
		isSMRF := safeMargin < 0
		isPFRF := grossProfitMargin < 0.2
		isAERF := adminExpenseRatio > 0.25
		isIERF := interestExpenseRatio > 0.4
		isCRRF := cashRatio < 1
		isDCRF := debtCoverRatio < 4

		return c.JSON(
			http.StatusOK,
			map[string]interface{}{
				"vars": []map[string]interface{}{
					{
						"stock_code":            id,
						"company_name":          name,
						"rev_ratio_yoy":         s.Telegram.NumberFormat(revRatioYOY * 100),
						"is_not_growth":         isNotGrowth,
						"rev_ratio_qoq":         s.Telegram.NumberFormat(revRatioQOQ * 100),
						"is_not_growth_quarter": isNotGrowthQuarter,
						"latest_quarter":        revQuarterly,
						"stars":                 s.Telegram.StarFormat(grade),
						"price":                 s.Telegram.NumberFormat(int64(price)),
						"price_up":              priceUp,
						"price_volatility":      s.Telegram.NumberFormat(int64(meanPrice52w)),
						"price_risk":            s.Telegram.NumberFormat(int64(SDPrice52w)),
						"graham_value":          s.Telegram.NumberFormat(int64(grahamValue)),
						"safe_margin":           s.Telegram.NumberFormat(safeMargin * 100),
						"safe_margin_rf":        isSMRF,
						"pex_rf":                isPExRF,
						"p_ex":                  s.Telegram.NumberFormat(PEx),
						"dividend":              s.Telegram.NumberFormat(int64(dividend)),
						"dividend_percent":      s.Telegram.NumberFormat(dividend / price * 100),
						"mean_dividend":         s.Telegram.NumberFormat(int64(mDividend)),
						"mean_dividend_percent": s.Telegram.NumberFormat(mDividend / price * 100),
						"capitalize":            s.Telegram.NumberFormat(int64(marketCap)),
						"pe":                    s.Telegram.NumberFormat(price / eps),
						"pe_rf":                 isPERF,
						"pb":                    s.Telegram.NumberFormat(price / bvps),
						"pb_rf":                 isPBRF,
						"profit_margin":         s.Telegram.NumberFormat(grossProfitMargin * 100),
						"profit_margin_rf":      isPFRF,
						"interest_expense":      s.Telegram.NumberFormat(interestExpenseRatio * 100),
						"interest_expense_rf":   isIERF,
						"admin_expense":         s.Telegram.NumberFormat(adminExpenseRatio * 100),
						"admin_expense_rf":      isAERF,
						"cash_ratio":            s.Telegram.NumberFormat(cashRatio),
						"cash_ratio_rf":         isCRRF,
						"debt_cover":            s.Telegram.NumberFormat(debtCoverRatio),
						"debt_cover_rf":         isDCRF,
						"year":                  yearsList[len(yearsList)-1],
						"net_smooth":            netSmooth,
						"token":                 token,
						"frontend_url":          s.Config.FrontendURL,
					},
				},
			})
	}
}
