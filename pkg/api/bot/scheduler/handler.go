package scheduler

import (
	"crypto/sha256"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/araddon/dateparse"
	"github.com/labstack/echo/v4"
)

func SendMessageToSubscribers(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		rs, err := tx.QueryContext(c.Request().Context(), "SELECT id, schedule_date, description, chat_id, command FROM scheduler WHERE schedule_date <= NOW()")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		defer rs.Close()
		loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")

		type scheduler struct {
			id          string
			date        time.Time
			description string
			command     sql.NullString
			chatID      string
		}
		var jobs []*model.NanoJob
		var commands []*scheduler
		for rs.Next() {
			sche := new(scheduler)
			if err := rs.Scan(&sche.id, &sche.date, &sche.description, &sche.chatID, &sche.command); err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					break
				}
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			params := map[string]string{
				"chat_id": sche.chatID,
				"message": fmt.Sprintf("Bạn có lời nhắc lúc %s\n%s", sche.date.In(loc).Format("15:04:05"), s.Telegram.EscapeMarkdown(sche.description)),
			}
			paramData, err := json.Marshal(params)
			if err != nil {
				return err
			}
			job := &model.NanoJob{
				Url:    fmt.Sprintf("http://localhost:%s/v1/channel/ask", s.Config.HTTPPort),
				Params: string(paramData),
				Method: http.MethodPost,
				Owner:  sche.chatID,
			}
			jobs = append(jobs, job)
			if sche.command.Valid {
				commands = append(commands, sche)
			}
		}

		for _, job := range jobs {
			// Insert new job inform user calendar
			err = s.Pacman.IngestJobWithPriority(c.Request().Context(), job, true)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}

		for _, command := range commands {
			chatObject, err := s.Chatbot.LoadUserChatObject(c.Request().Context(), command.chatID)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			job, err := s.Chatbot.DetectUserContext(c.Request().Context(), chatObject, command.description)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			err = s.Pacman.IngestJob(c.Request().Context(), job)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}

		// delete prompt scheduler
		_, err = tx.ExecContext(c.Request().Context(), "DELETE FROM scheduler WHERE schedule_date <= NOW()")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// update user routine at 12am
		now := time.Now().In(loc)
		rst, err := tx.QueryContext(c.Request().Context(), "SELECT id, routine_updated_at FROM chatbot")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		defer rst.Close()
		var dailyJobs []*model.NanoJob
		var dailyIDs []string
		for rst.Next() {
			var (
				id   string
				date time.Time
			)
			if err := rst.Scan(&id, &date); err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					break
				}
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			if date.In(loc).Day() != now.Day() {
				job := &model.NanoJob{
					Url:    fmt.Sprintf("http://localhost:%s/v1/schedule/daily", s.Config.HTTPPort),
					Params: fmt.Sprintf(`{"chat_id":"%s"}`, id),
					Method: http.MethodPost,
					Owner:  id,
				}
				dailyJobs = append(dailyJobs, job)
				dailyIDs = append(dailyIDs, id)
			}
		}
		for _, job := range dailyJobs {
			// Insert new job inform user calender
			err = s.Pacman.IngestJob(c.Request().Context(), job)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}
		eod := time.Date(now.Year(), now.Month(), now.Day(), 11, 59, 00, 0, loc)
		// Update routine time
		for _, id := range dailyIDs {
			stmt, err := tx.Prepare("UPDATE chatbot SET routine_updated_at = $1 WHERE id = $2")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			_, err = stmt.ExecContext(c.Request().Context(), eod.UTC(), id)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}

		// nudge job
		s.Pacman.NudgeQueue()

		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}

func SubscribeNotification(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type scheduleRequest struct {
			ChatID       string `json:"chat_id" validate:"required"`
			ScheduleDate string `json:"schedule_date" validate:"required"`
			Description  string `json:"description" validate:"required"`
		}
		schReq := new(scheduleRequest)

		err := c.Bind(schReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}
		// Check sum
		h := sha256.New()
		h.Write([]byte(schReq.ChatID + schReq.ScheduleDate + schReq.Description))
		reqSum := fmt.Sprintf("%x", h.Sum(nil))

		if err := c.Validate(schReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)

		stmt, err := tx.Prepare("SELECT id FROM scheduler WHERE id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		r := stmt.QueryRowContext(c.Request().Context(), reqSum)
		var id string

		loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
		// Detect user input hours
		scheduleTime := time.Now().In(loc)
		validHours := regexp.MustCompile(`^(\d{1,2})\s*(:\s*(\d{1,2})\s*)?(:\s*(\d{1,2}))?$`)
		if validHours.MatchString(schReq.ScheduleDate) {
			groupMatch := validHours.FindStringSubmatch(schReq.ScheduleDate)
			hour, err := strconv.Atoi(groupMatch[1])
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			minute, err := strconv.Atoi(groupMatch[3])
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			second := 0
			if groupMatch[5] != "" {
				second, err = strconv.Atoi(groupMatch[5])
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
			}
			scheduleTime = time.Date(scheduleTime.Year(), scheduleTime.Month(), scheduleTime.Day(), hour, minute, second, scheduleTime.Nanosecond(), loc)
		} else {
			// DD-MM-YYYY hh:mm:ss
			scheduleTime, err = dateparse.ParseIn(schReq.ScheduleDate, loc, dateparse.PreferMonthFirst(false), dateparse.RetryAmbiguousDateWithSwap(true))
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}
		if scheduleTime.Before(time.Now().In(loc)) {
			return echo.NewHTTPError(http.StatusInternalServerError, constants.CommonError.ERROR_BAD_PARAM_INPUT)
		}
		if err = r.Scan(&id); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				stmt, err = tx.Prepare("INSERT INTO scheduler(id,schedule_date,description,chat_id) VALUES($1,$2,$3,$4)")
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				_, err = stmt.ExecContext(c.Request().Context(), reqSum, scheduleTime.UTC(), schReq.Description, schReq.ChatID)
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
			} else {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		} else {
			stmt, err = tx.Prepare("UPDATE scheduler SET schedule_date = $1, description = $2, chat_id = $3 WHERE id = $4")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			_, err = stmt.ExecContext(c.Request().Context(), scheduleTime.UTC(), schReq.Description, schReq.ChatID, reqSum)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}
		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}

func SubscribeCommandNotification(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type scheduleRequest struct {
			ChatID       string `json:"chat_id" validate:"required"`
			ScheduleDate string `json:"schedule_date" validate:"required"`
			Description  string `json:"description" validate:"required"`
		}
		schReq := new(scheduleRequest)

		err := c.Bind(schReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}
		if err := c.Validate(schReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)

		workflow, err := s.Chatbot.DetectWorkflow(c.Request().Context(), schReq.ChatID, schReq.Description)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		if workflow == "scheduler/default" {
			return echo.NewHTTPError(http.StatusBadRequest, errors.New("invalid command"))
		}
		// Check sum
		h := sha256.New()
		h.Write([]byte(schReq.ChatID + schReq.ScheduleDate + schReq.Description + workflow))
		reqSum := fmt.Sprintf("%x", h.Sum(nil))

		stmt, err := tx.Prepare("SELECT id FROM scheduler WHERE id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		r := stmt.QueryRowContext(c.Request().Context(), reqSum)
		var id string

		loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
		// Detect user input hours
		timeNow := time.Now().In(loc)
		scheduleTime := timeNow
		validHours := regexp.MustCompile(`^(\d{1,2})\s*(:\s*(\d{1,2})\s*)?(:\s*(\d{1,2}))?$`)
		if validHours.MatchString(schReq.ScheduleDate) {
			groupMatch := validHours.FindStringSubmatch(schReq.ScheduleDate)
			hour, err := strconv.Atoi(groupMatch[1])
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			minute, err := strconv.Atoi(groupMatch[3])
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			second := 0
			if groupMatch[5] != "" {
				second, err = strconv.Atoi(groupMatch[5])
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
			}
			scheduleTime = time.Date(scheduleTime.Year(), scheduleTime.Month(), scheduleTime.Day(), hour, minute, second, scheduleTime.Nanosecond(), loc)
		} else {
			// DD-MM-YYYY hh:mm:ss
			scheduleTime, err = dateparse.ParseAny(schReq.ScheduleDate, dateparse.PreferMonthFirst(false), dateparse.RetryAmbiguousDateWithSwap(true))
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}
		if scheduleTime.Before(timeNow) {
			return echo.NewHTTPError(http.StatusBadRequest, errors.New("invalid time"))
		}

		if err = r.Scan(&id); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				stmt, err = tx.Prepare("INSERT INTO scheduler(id,schedule_date,description,chat_id,command) VALUES($1,$2,$3,$4,$5)")
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
				_, err = stmt.ExecContext(c.Request().Context(), reqSum, scheduleTime.UTC(), schReq.Description, schReq.ChatID, workflow)
				if err != nil {
					return echo.NewHTTPError(http.StatusInternalServerError, err)
				}
			} else {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		} else {
			stmt, err = tx.Prepare("UPDATE scheduler SET schedule_date = $1, description = $2, chat_id = $3, command = $4 WHERE id = $5")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			_, err = stmt.ExecContext(c.Request().Context(), scheduleTime.UTC(), schReq.Description, schReq.ChatID, workflow, reqSum)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}
		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}

func SubscribeList(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type scheduleRequest struct {
			ChatID string `json:"chat_id" validate:"required"`
		}
		schReq := new(scheduleRequest)

		err := c.Bind(schReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(schReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)

		stmt, err := tx.Prepare("SELECT count(*) FROM scheduler WHERE chat_id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		row := stmt.QueryRowContext(c.Request().Context(), schReq.ChatID)
		var totalDailyScheduler int
		if err := row.Scan(&totalDailyScheduler); err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		stmt, err = tx.Prepare("SELECT username, first_name, last_name FROM chatbot WHERE id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		userRow := stmt.QueryRowContext(c.Request().Context(), schReq.ChatID)
		user := &model.UserInfo{ID: schReq.ChatID}
		if err := userRow.Scan(&user.User, &user.FirstName, &user.LastName); err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Create token
		token, err := s.JWTs.Sign(user)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		params := map[string]string{
			"chat_id": schReq.ChatID,
			"message": fmt.Sprintf("Bạn có %d lời nhắc hằng ngày.\nBấm [vào đây](%s/?token=%s) để đặt lịch cho bạn.", totalDailyScheduler, s.Config.FrontendURL, token),
		}
		paramData, err := json.Marshal(params)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		job := &model.NanoJob{
			Url:    fmt.Sprintf("http://localhost:%s/v1/channel/ask", s.Config.HTTPPort),
			Params: string(paramData),
			Method: http.MethodPost,
			Owner:  schReq.ChatID,
		}
		// Insert new job inform user calendar
		err = s.Pacman.IngestJob(c.Request().Context(), job)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)

		}
		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}

func SetupUserDailyList(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type scheduleRequest struct {
			ChatID string `json:"chat_id" validate:"required"`
		}
		schReq := new(scheduleRequest)

		err := c.Bind(schReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(schReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
		today := time.Now().In(loc).Weekday()

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT time, description, command, dates FROM daily WHERE user_id=$1 ORDER BY time ASC")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs, err := stmt.QueryContext(c.Request().Context(), schReq.ChatID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		var jobs []*model.NanoJob
		for rs.Next() {
			var (
				timeDB  string
				desc    string
				command bool
				dateStr []byte
				dates   []time.Weekday
			)
			if err := rs.Scan(&timeDB, &desc, &command, &dateStr); err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}

			err := json.Unmarshal(dateStr, &dates)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}

			// Check today is valid date
			isValidDay := false

			for _, day := range dates {
				if day == today {
					isValidDay = true
					break
				}
			}

			if !isValidDay {
				continue
			}
			params := map[string]interface{}{
				"chat_id":       schReq.ChatID,
				"schedule_date": timeDB,
				"description":   desc,
			}

			paramStr, err := json.Marshal(params)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			if command {
				job := &model.NanoJob{
					Url:    fmt.Sprintf("http://localhost:%s/v1/schedule/command/set", s.Config.HTTPPort),
					Params: string(paramStr),
					Method: http.MethodPost,
					Owner:  schReq.ChatID,
				}
				jobs = append(jobs, job)

			} else {
				job := &model.NanoJob{
					Url:    fmt.Sprintf("http://localhost:%s/v1/schedule/set", s.Config.HTTPPort),
					Params: string(paramStr),
					Method: http.MethodPost,
				}
				jobs = append(jobs, job)
			}

		}
		rs.Close()
		for _, job := range jobs {
			err = s.Pacman.IngestJob(c.Request().Context(), job)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}

		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}
