package communicate

import (
	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/labstack/echo/v4"
	"net/http"
)

func AskUser(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type AskUserRequest struct {
			ChatID        string `json:"chat_id"`
			ChatChannelID string `json:"chat_channel_id"`
			ChatChannel   string `json:"chat_channel"`
			Message       string `json:"message"`
		}
		askReq := new(AskUserRequest)
		err := c.Bind(askReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if askReq.ChatChannel == constants.ChatChannels.TELEGRAM {
			// Create the request body struct
			reqBody := &model.SendMessageReqBody{
				ChatID:    askReq.ChatChannelID,
				ParseMode: "MarkdownV2",
				Text:      s.Telegram.EscapeMarkdown(askReq.Message),
			}
			err = s.Telegram.SendMessage(c.Request().Context(), reqBody)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		} else if askReq.ChatChannel == constants.ChatChannels.FACEBOOK {
			err = s.Facebook.SendMessage(c.Request().Context(), &model.FaceBookSendMessageReqBody{
				RecipientID: askReq.ChatChannelID,
				Message:     askReq.Message,
			})
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}

		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}
