package communicate

import (
	"net/http"
	"regexp"
	"strings"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/labstack/echo/v4"
)

func AskUser(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type AskUserRequest struct {
			ChatID        string `json:"chat_id"`
			ChatChannelID string `json:"chat_channel_id"`
			ChatChannel   string `json:"chat_channel"`
			Message       string `json:"message"`
		}
		askReq := new(AskUserRequest)
		err := c.Bind(askReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if askReq.ChatChannel == constants.ChatChannels.TELEGRAM {
			// Create the request body struct
			reqBody := &model.SendMessageReqBody{
				ChatID:    askReq.ChatChannelID,
				ParseMode: "MarkdownV2",
				Text:      s.Telegram.EscapeMarkdown(askReq.Message),
			}
			err = s.Telegram.SendMessage(c.Request().Context(), reqBody)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		} else if askReq.ChatChannel == constants.ChatChannels.FACEBOOK {
			// Extract markdown links from the message
			cleanText, links := extractMarkdownLinks(askReq.Message)

			if len(links) > 0 {
				// Send button template message if links are found
				buttons := convertLinksToFacebookButtons(links)
				err = s.Facebook.SendButtonMessage(c.Request().Context(), &model.FaceBookSendButtonMessageReqBody{
					RecipientID: askReq.ChatChannelID,
					Text:        cleanText,
					Buttons:     buttons,
				})
			} else {
				// Send regular text message if no links
				err = s.Facebook.SendMessage(c.Request().Context(), &model.FaceBookSendMessageReqBody{
					RecipientID: askReq.ChatChannelID,
					Message:     askReq.Message,
				})
			}
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}

		return c.JSON(http.StatusOK, &model.SuccessResponse{Message: "ok"})
	}
}

// MarkdownLink represents a parsed markdown link
type MarkdownLink struct {
	Text string
	URL  string
}

// extractMarkdownLinks extracts markdown links from text and returns them along with cleaned text
func extractMarkdownLinks(text string) (string, []MarkdownLink) {
	// Regex to match markdown links: [text](url)
	linkRegex := regexp.MustCompile(`\[([^\]]+)\]\(([^)]+)\)`)
	matches := linkRegex.FindAllStringSubmatch(text, -1)

	var links []MarkdownLink
	cleanText := text

	for _, match := range matches {
		if len(match) >= 3 {
			links = append(links, MarkdownLink{
				Text: match[1],
				URL:  match[2],
			})
			// Remove the markdown link from the text
			cleanText = strings.Replace(cleanText, match[0], "", 1)
		}
	}

	// Clean up extra whitespace
	cleanText = strings.TrimSpace(cleanText)

	return cleanText, links
}

// convertLinksToFacebookButtons converts markdown links to Facebook button format
func convertLinksToFacebookButtons(links []MarkdownLink) []model.FaceBookMessageButton {
	var buttons []model.FaceBookMessageButton

	for _, link := range links {
		buttons = append(buttons, model.FaceBookMessageButton{
			Type:  "web_url",
			Title: link.Text,
			URL:   link.URL,
		})
	}

	return buttons
}
