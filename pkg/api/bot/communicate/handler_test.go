package communicate

import (
	"testing"

	"github.com/007lock/simon-homestead/pkg/model"
)

func TestExtractMarkdownLinks(t *testing.T) {
	tests := []struct {
		name           string
		input          string
		expectedText   string
		expectedLinks  []MarkdownLink
	}{
		{
			name:          "No links",
			input:         "This is a simple message with no links",
			expectedText:  "This is a simple message with no links",
			expectedLinks: []MarkdownLink{},
		},
		{
			name:         "Single link",
			input:        "Check out this [awesome website](https://example.com) for more info",
			expectedText: "Check out this  for more info",
			expectedLinks: []MarkdownLink{
				{Text: "awesome website", URL: "https://example.com"},
			},
		},
		{
			name:         "Multiple links",
			input:        "Visit [Google](https://google.com) and [GitHub](https://github.com) today",
			expectedText: "Visit  and  today",
			expectedLinks: []MarkdownLink{
				{Text: "Google", URL: "https://google.com"},
				{Text: "GitHub", URL: "https://github.com"},
			},
		},
		{
			name:         "Link with Vietnamese text",
			input:        "Xem chi tiết: [Bấm vào đây](https://example.com/portfolio?token=abc123)",
			expectedText: "Xem chi tiết:",
			expectedLinks: []MarkdownLink{
				{Text: "Bấm vào đây", URL: "https://example.com/portfolio?token=abc123"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cleanText, links := extractMarkdownLinks(tt.input)
			
			if cleanText != tt.expectedText {
				t.Errorf("extractMarkdownLinks() cleanText = %v, want %v", cleanText, tt.expectedText)
			}
			
			if len(links) != len(tt.expectedLinks) {
				t.Errorf("extractMarkdownLinks() links count = %v, want %v", len(links), len(tt.expectedLinks))
				return
			}
			
			for i, link := range links {
				if link.Text != tt.expectedLinks[i].Text {
					t.Errorf("extractMarkdownLinks() link[%d].Text = %v, want %v", i, link.Text, tt.expectedLinks[i].Text)
				}
				if link.URL != tt.expectedLinks[i].URL {
					t.Errorf("extractMarkdownLinks() link[%d].URL = %v, want %v", i, link.URL, tt.expectedLinks[i].URL)
				}
			}
		})
	}
}

func TestConvertLinksToFacebookButtons(t *testing.T) {
	tests := []struct {
		name            string
		links           []MarkdownLink
		expectedButtons []model.FaceBookMessageButton
	}{
		{
			name:            "Empty links",
			links:           []MarkdownLink{},
			expectedButtons: []model.FaceBookMessageButton{},
		},
		{
			name: "Single link",
			links: []MarkdownLink{
				{Text: "Visit Website", URL: "https://example.com"},
			},
			expectedButtons: []model.FaceBookMessageButton{
				{Type: "web_url", Title: "Visit Website", URL: "https://example.com"},
			},
		},
		{
			name: "Multiple links",
			links: []MarkdownLink{
				{Text: "Google", URL: "https://google.com"},
				{Text: "GitHub", URL: "https://github.com"},
			},
			expectedButtons: []model.FaceBookMessageButton{
				{Type: "web_url", Title: "Google", URL: "https://google.com"},
				{Type: "web_url", Title: "GitHub", URL: "https://github.com"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			buttons := convertLinksToFacebookButtons(tt.links)
			
			if len(buttons) != len(tt.expectedButtons) {
				t.Errorf("convertLinksToFacebookButtons() buttons count = %v, want %v", len(buttons), len(tt.expectedButtons))
				return
			}
			
			for i, button := range buttons {
				if button.Type != tt.expectedButtons[i].Type {
					t.Errorf("convertLinksToFacebookButtons() button[%d].Type = %v, want %v", i, button.Type, tt.expectedButtons[i].Type)
				}
				if button.Title != tt.expectedButtons[i].Title {
					t.Errorf("convertLinksToFacebookButtons() button[%d].Title = %v, want %v", i, button.Title, tt.expectedButtons[i].Title)
				}
				if button.URL != tt.expectedButtons[i].URL {
					t.Errorf("convertLinksToFacebookButtons() button[%d].URL = %v, want %v", i, button.URL, tt.expectedButtons[i].URL)
				}
			}
		})
	}
}
