package interest

import (
	"database/sql"
	"errors"
	"net/http"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/labstack/echo/v4"
)

func FetchBankInterest(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		sixMonthInterest := 0.0

		var createdAt time.Time
		r := tx.QueryRowContext(c.Request().Context(), "SELECT six_month_interest, created_at FROM bank_interests ORDER BY created_at DESC LIMIT 1")
		err := r.<PERSON>an(&sixMonthInterest, &createdAt)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		now := time.Now()
		prevDay := now.AddDate(0, 0, -10)
		//fmt.Println(createdAt.Format(time.RFC3339), prevDay.Format(time.RFC3339))
		if prevDay.After(createdAt) {
			sixMonthInterest, err = s.Cafef.FetchBankInterest(c.Request().Context(), 6)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}

			stmt, err := tx.PrepareContext(c.Request().Context(), `INSERT INTO bank_interests(monthly, six_month_interest, created_at) VALUES($1, $2, $3) ON CONFLICT (monthly) 
												DO 
											   UPDATE SET six_month_interest = $2, created_at = $3`)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			_, err = stmt.ExecContext(c.Request().Context(), now.Format("012006"), sixMonthInterest, now)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}
		return c.JSON(http.StatusOK, map[string]interface{}{
			"vars": []map[string]interface{}{
				{
					"interest": sixMonthInterest * 100,
				},
			},
		})
	}
}
