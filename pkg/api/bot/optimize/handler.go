package optimize

import (
	"database/sql"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/007lock/simon-homestead/pkg/util"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
	"gonum.org/v1/gonum/mat"
)

func RecognizeStocks(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type assReqModel struct {
			Stocks string `json:"stock_codes" validate:"required"`
		}

		assReq := new(assReqModel)

		err := c.Bind(assReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(assReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		stocks := strings.Split(assReq.Stocks, ",")
		for i, stock := range stocks {
			stocks[i] = strings.ToUpper(strings.TrimSpace(stock))
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)

		stmt, err := tx.Prepare("SELECT id FROM stocks WHERE id LIKE ANY($1)")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs, err := stmt.QueryContext(c.Request().Context(), pq.StringArray(stocks))
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		var recStocks []string
		for rs.Next() {
			var id string
			err := rs.Scan(&id)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			recStocks = append(recStocks, id)
		}
		rs.Close()
		n := len(recStocks)
		weight := 1.0 / float64(n)

		type StockWeighted struct {
			Code   string  `json:"code" required:"true"`
			Weight float64 `json:"weight" required:"true"`
		}
		var stockWeighted []*StockWeighted
		for _, stock := range recStocks {
			stockWeighted = append(stockWeighted, &StockWeighted{
				Code:   stock,
				Weight: weight,
			})
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"stocks": stockWeighted,
		})
	}
}

func CalculateVariance(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type StockWeighted struct {
			Code   string  `json:"code" required:"true"`
			Weight float64 `json:"weight" required:"true"`
		}

		type StockAsset struct {
			Stocks []*StockWeighted `json:"stocks" required:"true"`
		}

		assReq := new(StockAsset)

		err := c.Bind(assReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(assReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		n := len(assReq.Stocks)

		var codes []string
		W := mat.NewVecDense(n, nil)
		for i, ad := range assReq.Stocks {
			if err := c.Validate(ad); err != nil {
				return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
			}
			codes = append(codes, ad.Code)
			W.SetVec(i, ad.Weight)
		}

		now := time.Now()
		before52w := now.AddDate(-1, 0, 0)
		X, err := util.AccumulatePriceXSampling(s.DB, s.PriceFileDB, codes, before52w, now)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Calculate return
		m, _ := X.Dims()
		returnRate := 0.0
		for i := 0; i < n; i++ {
			returnRate += (W.AtVec(i) * X.At(m-1, i))
		}

		// Calculate covariance
		coVar := util.CalCovariance(X, W)

		// fX := mat.Formatted(X, mat.Prefix("    "), mat.Squeeze())
		// fmt.Printf("X = %v\n", fX)
		return c.JSON(http.StatusOK, map[string]interface{}{
			"stocks":        assReq.Stocks,
			"return":        returnRate,
			"return_format": s.Telegram.NumberFormat(int64(returnRate * 100)),
			"var":           coVar,
			"var_format":    s.Telegram.NumberFormat(int64(coVar * 100)),
		})
	}
}

func OptimizeStocks(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type StockWeighted struct {
			Code         string  `json:"code" required:"true"`
			Weight       float64 `json:"weight" required:"true"`
			WeightFormat string  `json:"weight_format"`
		}

		type StockAsset struct {
			Stocks []*StockWeighted `json:"stocks" required:"true"`
			Var    float64          `json:"var" required:"true"`
		}

		assReq := new(StockAsset)

		err := c.Bind(assReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(assReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		var codes []string
		for _, stock := range assReq.Stocks {
			codes = append(codes, stock.Code)
		}

		now := time.Now()
		before52w := now.AddDate(-1, 0, 0)
		X, err := util.AccumulatePriceXSampling(s.DB, s.PriceFileDB, codes, before52w, now)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		m, _ := X.Dims()
		Y := mat.NewVecDense(m, nil)
		yoyRate := assReq.Var / float64(m)
		for i := 0; i < m; i++ {
			// Calculate cumulative return.
			if i == 0 {
				// Initial rate
				Y.SetVec(i, yoyRate)
			} else {
				Y.SetVec(i, Y.AtVec(i-1)+yoyRate)
			}
		}
		weights := util.RegressionWeight(X, Y)
		scaledWeights := util.StandardizedWeightVector(X, Y, weights)

		var newWeights []StockWeighted
		for i, w := range scaledWeights {
			newWeights = append(newWeights, StockWeighted{
				Code:         codes[i],
				Weight:       w,
				WeightFormat: s.Telegram.NumberFormat(int64(w * 100)),
			})
		}
		sort.SliceStable(newWeights, func(i, j int) bool {
			return newWeights[i].Weight > newWeights[j].Weight
		})
		// fWX := mat.Formatted(&Var, mat.Prefix("    "), mat.Squeeze())
		// fmt.Printf("WX = %v\n", fWX)
		return c.JSON(http.StatusOK, map[string]interface{}{
			"stocks":      assReq.Stocks,
			"new_weights": newWeights,
			"var":         assReq.Var,
		})
	}
}
