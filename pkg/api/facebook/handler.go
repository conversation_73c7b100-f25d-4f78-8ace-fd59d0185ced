// Package facebook pkg/api/facebook/webhook.go
package facebook

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"errors"
	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/goombaio/namegenerator"
	"github.com/labstack/echo/v4"
)

// VerifyWebhook handles the initial webhook verification from Facebook
//
//	will make a GET request with certain query parameters to verify the webhook
func VerifyWebhook(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		// Facebook sends these parameters for verification
		mode := c.QueryParam("hub.mode")
		token := c.QueryParam("hub.verify_token")
		challenge := c.QueryParam("hub.challenge")

		// Verify the token matches your configured token
		// You should store this in your configuration
		verifyToken := s.Config.Facebook.VerifyToken

		if mode == "subscribe" && token == verifyToken {
			// Return the challenge to confirm verification
			return c.String(http.StatusOK, challenge)
		}

		return c.String(http.StatusForbidden, "Verification failed")
	}
}

// ReceiveMessage handles the webhook POST requests from Facebook Messenger
func ReceiveMessage(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		// Verify signature if you've configured app secret
		if s.Config.Facebook.AppSecret != "" {
			signature := c.Request().Header.Get("X-Hub-Signature")
			if signature == "" {
				return c.String(http.StatusForbidden, "No signature")
			}

			// Read the body
			body, err := io.ReadAll(c.Request().Body)
			if err != nil {
				return c.String(http.StatusInternalServerError, "Failed to read body")
			}

			// Reset body for later use
			c.Request().Body = io.NopCloser(bytes.NewBuffer(body))

			// Calculate expected signature
			mac := hmac.New(sha1.New, []byte(s.Config.Facebook.AppSecret))
			mac.Write(body)
			expectedSignature := "sha1=" + hex.EncodeToString(mac.Sum(nil))

			if !hmac.Equal([]byte(signature), []byte(expectedSignature)) {
				return c.String(http.StatusForbidden, "Invalid signature")
			}
		}

		// Parse the request body
		var webhook struct {
			Object string `json:"object"`
			Entry  []struct {
				ID        string `json:"id"`
				Time      int64  `json:"time"`
				Messaging []struct {
					Sender struct {
						ID string `json:"id"`
					} `json:"sender"`
					Recipient struct {
						ID string `json:"id"`
					} `json:"recipient"`
					Timestamp int64 `json:"timestamp"`
					Message   struct {
						Mid  string `json:"mid"`
						Text string `json:"text"`
					} `json:"message,omitempty"`
					// Add the messaging_customer_information field
					MessagingCustomerInformation struct {
						Screens []struct {
							ScreenID  string `json:"screen_id"`
							Responses []struct {
								Key   string `json:"key"`
								Value string `json:"value"`
							} `json:"responses"`
						} `json:"screens"`
					} `json:"messaging_customer_information,omitempty"`
					// Add other event types as needed (postbacks, etc.)
				} `json:"messaging"`
			} `json:"entry"`
		}

		if err := c.Bind(&webhook); err != nil {
			return c.String(http.StatusBadRequest, "Invalid webhook payload")
		}

		// Verify this is a page webhook
		if webhook.Object != "page" {
			return c.String(http.StatusOK, "Ignoring non-page webhook")
		}

		// Get database transaction
		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)

		// Process each messaging event
		for _, entry := range webhook.Entry {
			for _, event := range entry.Messaging {
				// Process the message
				if event.Message.Text != "" {
					// Handle the message text
					chatObj, err := processMessage(c.Request().Context(), tx, s, event.Sender.ID, event.Message.Text)
					if err != nil {
						return echo.NewHTTPError(http.StatusInternalServerError, err)
					}
					err = processChatObject(c.Request().Context(), s, chatObj, event.Message.Text)
					if err != nil {
						return echo.NewHTTPError(http.StatusInternalServerError, err)
					}
				}
				// Process customer information if available
				if len(event.MessagingCustomerInformation.Screens) > 0 {
					err := processCustomerInformation(c.Request().Context(), tx, s, event.Sender.ID, event.MessagingCustomerInformation)
					if err != nil {
						return echo.NewHTTPError(http.StatusInternalServerError, err)
					}
				}
				// Handle other event types as needed
			}
		}

		// Facebook requires a 200 OK response to acknowledge receipt
		return c.String(http.StatusOK, "EVENT_RECEIVED")
	}
}

// processCustomerInformation handles the customer information received from Facebook
func processCustomerInformation(ctx context.Context, tx *sql.Tx, s *service.Service, senderID string, info struct {
	Screens []struct {
		ScreenID  string `json:"screen_id"`
		Responses []struct {
			Key   string `json:"key"`
			Value string `json:"value"`
		} `json:"responses"`
	} `json:"screens"`
}) error {
	// Check if a user exists in the database
	stmt, err := tx.Prepare("SELECT id FROM chatbot WHERE external_id = $1 AND channel = $2")
	if err != nil {
		s.Logger.Error("Failed to prepare statement", zap.Error(err))
		return err
	}
	defer stmt.Close()

	var chatbotID string
	err = stmt.QueryRowContext(ctx, senderID, constants.ChatChannels.FACEBOOK).Scan(&chatbotID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// User doesn't exist yet, we need to create a new user
			// This should not normally happen as the user should have been created when they first messaged
			return errors.New("user not found in database")
		}
		s.Logger.Error("Failed to query user", zap.Error(err))
		return err
	}

	// Process and update user information based on the received data
	for _, screen := range info.Screens {
		s.Logger.Info("Processing customer information",
			zap.String("senderID", senderID),
			zap.String("screenID", screen.ScreenID))

		// Extract information from the responses
		var firstName, lastName, fullName string

		for _, response := range screen.Responses {
			switch response.Key {
			case "name":
				fullName = response.Value
				// Split name into first and last name
				nameParts := strings.Fields(fullName)
				if len(nameParts) > 0 {
					firstName = nameParts[0]
					if len(nameParts) > 1 {
						lastName = strings.Join(nameParts[1:], " ")
					}
				}
			}
		}

		// Update user information in the database
		updateStmt, err := tx.Prepare(`
            UPDATE chatbot 
            SET first_name = $1, 
                last_name = $2, 
                username = $3
            WHERE id = $4
        `)
		if err != nil {
			s.Logger.Error("Failed to prepare update statement", zap.Error(err))
			return err
		}
		defer updateStmt.Close()

		// If we have a full name but no first/last name split, use the full name as username
		if firstName == "" && lastName == "" && fullName != "" {
			firstName = fullName
		}

		// Set username to combined name or first name if available
		username := fullName
		if username == "" {
			username = firstName
		}

		_, err = updateStmt.ExecContext(ctx, firstName, lastName, username, chatbotID)
		if err != nil {
			s.Logger.Error("Failed to update user information", zap.Error(err))
			return err
		}

		// Log the successful update
		s.Logger.Info("Updated user information from Facebook form",
			zap.String("chatbotID", chatbotID),
			zap.String("name", fullName),
			zap.String("firstName", firstName),
			zap.String("lastName", lastName),
		)

		// Send a confirmation message to the user
		err = s.Facebook.SendMessage(ctx, &model.FaceBookSendMessageReqBody{
			RecipientID: senderID,
			Message:     "Cảm ơn vì thông tin của bạn đã được cập nhật.",
		})
		if err != nil {
			s.Logger.Error("Failed to send confirmation message", zap.Error(err))
			// Don't return error here, as we've already updated the database
		}
	}

	return nil
}

func processChatObject(ctx context.Context, s *service.Service, chatObject *model.ChatObject, messageText string) error {
	if chatObject.State.Valid {
		// user either cancel or input variable
		workflowStr, err := s.Chatbot.DetectWorkflowByChatObj(chatObject, messageText)
		if err != nil {
			return err
		}
		if workflowStr == "scheduler/cancel" {
			job, err := s.Chatbot.CancelUserState(ctx, chatObject.ID)
			if err != nil {
				return err
			}
			err = s.Pacman.IngestJobWithPriority(ctx, job, true)
			return nil
		}
		// Merge input to context
		job, err := s.Chatbot.UserInput(ctx, chatObject, messageText)
		if err != nil {
			return err
		}
		err = s.Pacman.IngestJob(ctx, job)
		if err != nil {
			return err
		}
		return nil
	}
	job, err := s.Chatbot.DetectUserContext(ctx, chatObject, messageText)
	if err != nil {
		return err
	}
	err = s.Pacman.IngestJob(ctx, job)
	if err != nil {
		return err
	}
	return nil
}

func processMessage(ctx context.Context, tx *sql.Tx, s *service.Service, senderID, messageText string) (*model.ChatObject, error) {
	// Check if a user exists in the database
	stmt, err := tx.Prepare("SELECT id, state, scopes FROM chatbot WHERE external_id = $1 AND channel = $2")
	if err != nil {
		s.Facebook.SendMessage(ctx, &model.FaceBookSendMessageReqBody{
			RecipientID: senderID,
			Message:     "Sorry, there was an error processing your message.",
		})
		return nil, err
	}

	id, err := uuid.NewV7() // Generate a new UUID
	if err != nil {
		s.Facebook.SendMessage(ctx, &model.FaceBookSendMessageReqBody{
			RecipientID: senderID,
			Message:     "Sorry, there was an error creating a new ID. Please try again later.",
		})
		return nil, err
	}
	chatObject := &model.ChatObject{
		ID:         id.String(),
		ExternalID: senderID,
		Channel:    constants.ChatChannels.FACEBOOK,
	}

	r := stmt.QueryRowContext(ctx, chatObject.ExternalID, chatObject.Channel)
	var scopeNull sql.NullString
	if err = r.Scan(&chatObject.ID, &chatObject.State, &scopeNull); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// User isn't found - send a request to get user info
			err = s.Facebook.SendReqCustomerInfo(ctx, &model.FaceBookSendMessageReqBody{
				RecipientID: senderID,
			})
			if err != nil {
				s.Logger.Error("Failed to send request to get user info", zap.Error(err))
				return nil, err
			}
			// Generate random name since we couldn't get info from Facebook
			randomName := generateRandomName()
			s.Logger.Info("Failed to get user info from Facebook, using random name",
				zap.String("senderID", senderID),
				zap.String("randomName", randomName),
				zap.Error(err))
			// Populate chat object with user info
			chatObject.User = randomName
			chatObject.FirstName = randomName
			chatObject.Owner = 0
			chatObject.Scopes = []string{"scheduler"}

			// Insert new user
			stmt, err = tx.Prepare("INSERT INTO chatbot(id,external_id,username,first_name,last_name,owner,scopes,routine_updated_at,channel) VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9)")
			if err != nil {
				s.Facebook.SendMessage(ctx, &model.FaceBookSendMessageReqBody{
					RecipientID: senderID,
					Message:     "Sorry, there was an error processing your message.",
				})
				return nil, err
			}

			loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
			now := time.Now()
			eod := time.Date(now.Year(), now.Month(), now.Day(), 0, 30, 0, 0, loc)

			scopesByte, err := json.Marshal(chatObject.Scopes)
			if err != nil {
				s.Facebook.SendMessage(ctx, &model.FaceBookSendMessageReqBody{
					RecipientID: senderID,
					Message:     "Sorry, there was an error processing your message.",
				})
				return nil, err
			}

			_, err = stmt.ExecContext(ctx, chatObject.ID, chatObject.ExternalID, chatObject.User, chatObject.FirstName, chatObject.LastName, chatObject.Owner, string(scopesByte), eod.UTC(), "facebook")
			if err != nil {
				s.Facebook.SendMessage(ctx, &model.FaceBookSendMessageReqBody{
					RecipientID: senderID,
					Message:     "Sorry, there was an error processing your message.",
				})
				return nil, err
			}
			// Log the new user's message
			s.Logger.Info("Received message from new user",
				zap.String("senderID", senderID),
				zap.String("name", chatObject.User),
				zap.String("firstName", chatObject.FirstName),
				zap.String("lastName", chatObject.LastName),
				zap.String("message", messageText))
		} else {
			s.Facebook.SendMessage(ctx, &model.FaceBookSendMessageReqBody{
				RecipientID: senderID,
				Message:     "Sorry, there was an error processing your message.",
			})
			return nil, err
		}
	}

	// Parse scopes if they exist
	if scopeNull.Valid {
		err := json.Unmarshal([]byte(scopeNull.String), &chatObject.Scopes)
		if err != nil {
			s.Logger.Error("Failed to unmarshal scopes", zap.Error(err))
		}
	}

	return chatObject, nil
}

func generateRandomName() string {

	seed := time.Now().UTC().UnixNano()
	nameGenerator := namegenerator.NewNameGenerator(seed)

	return nameGenerator.Generate()
}
