package http

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/007lock/simon-homestead/test"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
)

func TestHealth(t *testing.T) {
	e := echo.New()
	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()

	// config
	cfg, err := test.CreateConfig()
	if err != nil {
		t.Fatal(err)
	}
	// Services
	s := &service.Service{
		Config: cfg,
	}
	h := Health(s)
	type args struct {
		c echo.Context
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "Health Success",
			args: args{
				c: e.New<PERSON>ontext(req, rec),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if assert.NoError(t, h(tt.args.c)) {
				assert.Equal(t, http.StatusOK, rec.Code)
			}
		})
	}
}
