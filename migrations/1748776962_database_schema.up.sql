DROP TABLE IF EXISTS "chatbot";
CREATE TABLE "chatbot"
(
    "id"                 character varying(255)                    NOT NULL,
    "external_id"        character varying(255)                    NOT NULL,
    "username"           character varying(255)                    NOT NULL,
    "first_name"         character varying(255)                    NOT NULL,
    "last_name"          character varying(255)                    NOT NULL,
    "owner"              integer                                   NOT NULL,
    "routine_updated_at" timestamp(0)           DEFAULT CURRENT_TIMESTAMP,
    "scopes"             text,
    "channel"            character varying(255) DEFAULT 'telegram' NOT NULL,
    "state"              text,
    CONSTRAINT "telegram_id" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "telegram_routine_updated_at" ON "chatbot" USING btree ("routine_updated_at");

DROP TABLE IF EXISTS "portfolios";
CREATE TABLE portfolios
(
    id       character varying(36),
    stock_id character varying(20)       NOT NULL,
    user_id  character varying(255)       NOT NULL,
    category character varying(20)       NOT NULL,
    quantity double precision            NOT NULL,
    price    double precision            NOT NULL,
    tax      double precision            NOT NULL,
    buy_at   timestamp without time zone NOT NULL,
    CONSTRAINT "portfolios_id" PRIMARY KEY ("id")
);
CREATE INDEX portfolios_category ON portfolios USING btree (category);
CREATE INDEX portfolios_stock_id ON portfolios USING btree (stock_id);
CREATE INDEX portfolios_user_id ON portfolios USING btree (user_id);

DROP TABLE IF EXISTS "current_portfolios";
CREATE TABLE "current_portfolios"
(
    "user_id"    character varying(255) NOT NULL,
    "stock"      character varying(20) NOT NULL,
    "quantity"   double precision      NOT NULL,
    "book_value" double precision      NOT NULL,
    "taxed"      double precision      NOT NULL DEFAULT '0',
    "buy_at"     timestamp(0)          NOT NULL,
    CONSTRAINT "current_portfolios_user_id_stock" PRIMARY KEY ("user_id", "stock")
) WITH (oids = false);

DROP TABLE IF EXISTS "pl_portfolios";
CREATE TABLE "pl_portfolios"
(
    "user_id"   character varying(255) NOT NULL,
    "stock"     character varying(20) NOT NULL,
    "profits"   double precision      NOT NULL,
    "taxed"     double precision      NOT NULL DEFAULT '0',
    "source"    character varying(20) NOT NULL,
    "record_at" timestamp(0)          NOT NULL
) WITH (oids = false);

CREATE INDEX "pl_portfolios_index" ON "pl_portfolios" USING btree ("user_id");
CREATE INDEX "pl_portfolios_source" ON "pl_portfolios" ("source");

DROP TABLE IF EXISTS "commodities";
CREATE TABLE "commodities"
(
    "id"         character varying(255) NOT NULL,
    "buy_price"  double precision       NOT NULL,
    "sell_price" double precision       NOT NULL,
    "updated_at" timestamp(0)           NOT NULL,
    CONSTRAINT "commodities_id" PRIMARY KEY ("id")
) WITH (oids = false);


DROP TABLE IF EXISTS "jobs";
CREATE TABLE "jobs"
(
    "id"         character varying(255) NOT NULL,
    "url"        character varying(255) NOT NULL,
    "params"     text,
    "method"     character varying(10)  NOT NULL,
    "created_by" character varying(255) NOT NULL,
    "created_at" timestamp(0)           NOT NULL,
    "ctx"        text,
    "map"        text,
    "step"       integer                NOT NULL DEFAULT '-1',
    "namespace"  character varying(50)  NOT NULL,
    CONSTRAINT "jobs_id" PRIMARY KEY ("id")
) WITH (oids = false);


DROP TABLE IF EXISTS "scheduler";
CREATE TABLE "scheduler"
(
    "id"            character varying(255) NOT NULL,
    "schedule_date" timestamp(0)           NOT NULL,
    "description"   text                   NOT NULL,
    "chat_id"       character varying(255) NOT NULL,
    "command"       character varying(255),
    CONSTRAINT "scheduler_id" PRIMARY KEY ("id")
) WITH (oids = false);

CREATE INDEX "scheduler_chat_id" ON "scheduler" USING btree ("chat_id");
CREATE INDEX "scheduler_schedule_date" ON "scheduler" USING btree ("schedule_date");

CREATE TABLE "daily"
(
    "id"          character varying(36) NOT NULL,
    "user_id"     character varying(20) NOT NULL,
    "time"        character varying(5)  NOT NULL,
    "description" text                  NOT NULL,
    "command"     boolean               NOT NULL DEFAULT '0',
    "dates"       character varying(50) NOT NULL,
    CONSTRAINT "daily_id" PRIMARY KEY ("id")
);

CREATE INDEX "daily_user_id" ON "daily" ("user_id");
CREATE INDEX "daily_time" ON "daily" ("time");


DROP TABLE IF EXISTS stocks;
CREATE TABLE stocks
(
    id                     varchar(20)                                    NOT NULL,
    price                  float8                                         NOT NULL,
    open_price             float8      DEFAULT 0                          NOT NULL,
    current_lowest_price   float8      DEFAULT '0'::double precision      NOT NULL,
    current_highest_price  float8      DEFAULT '0'::double precision      NOT NULL,
    market_cap             float8                                         NULL,
    expected_value_1y      float8      DEFAULT '0'::double precision      NOT NULL,
    shares                 float8                                         NULL,
    pe                     float8                                         NULL,
    roe                    float8                                         NULL,
    dividend               float8                                         NOT NULL,
    years                  text                                           NULL,
    rev_ratio_yoy          float8      DEFAULT '0'::double precision      NOT NULL,
    rev_ratio_qoq          float8      DEFAULT '0'::double precision      NOT NULL,
    rev_last_quarter       varchar(20)                                    NULL,
    gross_profit_margin    float8                                         NULL,
    interest_expense_ratio float8                                         NULL,
    admin_expense_ratio    float8                                         NULL,
    cash_ratio             float8                                         NULL,
    debt_cover_ratio       float8                                         NULL,
    graham_value           float8                                         NULL,
    eps                    float8                                         NULL,
    grade                  int4                                           NULL,
    pb                     float8                                         NULL,
    bvps                   float8                                         NULL,
    sd_price_52w           float8      DEFAULT '0'::double precision      NOT NULL,
    mean_52w               float8      DEFAULT '0'::double precision      NOT NULL,
    mean_dividend          float8      DEFAULT '0'::double precision      NOT NULL,
    net_smooth             bool        DEFAULT false                      NOT NULL,
    ssi_code               varchar(20)                                    NULL,
    company_name           varchar(255)                                   NULL,
    group_code             varchar(50)                                    NULL,
    data_source            varchar(20) DEFAULT 'cafef'::character varying NOT NULL,
    updated_at             timestamp(0)                                   NOT NULL
);

CREATE INDEX stocks_updated_at_idx ON stocks (updated_at);
CREATE INDEX "stocks_mean_52w_idx" ON "stocks" ("mean_52w");
CREATE INDEX "stocks_sd_price_52w_idx" ON "stocks" ("sd_price_52w");
CREATE INDEX "stocks_gross_profit_margin_idx" ON "stocks" ("gross_profit_margin");
CREATE INDEX "stocks_interest_expense_ratio_idx" ON "stocks" ("interest_expense_ratio");
CREATE INDEX "stocks_admin_expense_ratio_idx" ON "stocks" ("admin_expense_ratio");
CREATE INDEX "stocks_debt_cover_ratio_idx" ON "stocks" ("debt_cover_ratio");
CREATE INDEX "stocks_graham_value_idx" ON "stocks" ("graham_value");
CREATE INDEX "stocks_dividend_idx" ON "stocks" ("dividend");
CREATE INDEX "stocks_mean_dividend_idx" ON "stocks" ("mean_dividend");
CREATE INDEX "stocks_price_idx" ON "stocks" ("price");
CREATE INDEX "rev_ratio_yoy_idx" ON "stocks" ("rev_ratio_yoy");
CREATE INDEX "rev_ratio_qoq_idx" ON "stocks" ("rev_ratio_qoq");

CREATE TABLE "bank_interests"
(
    "monthly"            character varying(6) PRIMARY KEY,
    "six_month_interest" double precision NOT NULL,
    "created_at"         timestamp(0)     NOT NULL
);