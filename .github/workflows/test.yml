name: Test

on:
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: build/Dockerfile
          cache-from: type=gha
          cache-to: type=gha,mode=max
          push: false
          tags: ${{ vars.DOCKERHUB_USERNAME }}/homestead-app:latest
          labels: ${{ steps.meta.outputs.labels }}
          provenance: false