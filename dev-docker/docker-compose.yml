services:
  homestead:
    image: 007lock/homestead-app:latest
    container_name: homestead
    restart: always
    ports:
      - 8000:8011
    environment:
      DATABASE_URL: ${DATABASE_URL}
      FACEBOOK_APP_SECRET: ${FACEBOOK_APP_SECRET}
      FACEBOOK_PAGE_ACCESS_TOKEN: ${FACEBOOK_PAGE_ACCESS_TOKEN}
      FACEBOOK_VERIFY_TOKEN: ${FACEBOOK_VERIFY_TOKEN}
      TELEGRAM_TOKEN: ${TELEGRAM_TOKEN}
      JWT_SECRET: ${JWT_SECRET}
      DATA_STORAGE: ${DATA_STORAGE}
      ENVIRONMENT: ${ENVIRONMENT}
      TELEGRAM_OWNER: ${TELEGRAM_OWNER}

  db:
    image: postgres
    container_name: db
    restart: always
    volumes:
      - db_data:/var/lib/postgresql/data
    ports:
      - 5432:5432
    environment:
      POSTGRES_PASSWORD: postgres

  adminer:
    image: adminer
    restart: always
    environment:
      ADMINER_DEFAULT_DRIVER: pgsql
      ADMINER_DEFAULT_SERVER: db
      ADMINER_DESIGN: nicu
    ports:
      - 8080:8080

volumes:
  db_data: {}
