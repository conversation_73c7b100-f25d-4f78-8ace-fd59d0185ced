package contract

import (
	"context"
	"database/sql"
	"io"
	"regexp"
	"time"

	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/labstack/echo/v4"
)

// Website server
type WebServer interface {
	Serve() error
}

// Validator
type Validator interface {
	Validate(i interface{}) error
}

// JWT
type JWT interface {
	Sign(userClaim *model.UserInfo) (string, error)
	Verify(token string) (*model.UserInfo, error)
}

// Renderer
type Renderer interface {
	Render(w io.Writer, name string, data interface{}, c echo.Context) error
	CustomHTTPErrorHandler(err error, c echo.Context)
}

// Storage
type UserStorage interface {
	GetListfilesInFolder(token string, folderID string) ([]*model.StorageFile, error)
	CreateFolder(token string, folder string, parent string) (*model.StorageFile, error)
	UploadNewFileIntoFolder(token string, folderID string, fileName string, src string) (*model.StorageFile, error)
	UploadReplaceFile(token string, folderID string, fileID string, src string) (*model.StorageFile, error)
	DownloadFile(token string, fileID string, dest string) error
	GetAuthenURL() string
	AuthoriationCode(ctx context.Context, authCode string) (string, error)
	CreateFolderIfNotExisted(token string, folderName string, parent string) (string, error)
}

type ServerStorage interface {
	GetListfilesInFolder(continuationToken string, folderID string) ([]*model.StorageFile, error)
	CreateFolder(folder string, parent string) (string, error)
	UploadNewFileIntoFolder(folderID string, fileName string, src string) (*model.StorageFile, error)
	UploadReplaceFile(folderID string, fileID string, src string) (*model.StorageFile, error)
	DownloadFile(fileID string, dest string) error
	CreateFolderIfNotExisted(folderName string, parent string) (string, error)
}

// Vietstock service
type Vietstock interface {
	FetchPriceStock(ctx context.Context, stockCode string) (*model.StockTrade, error)
	FetchAllStocks(ctx context.Context) ([]*model.TradingStock, error)
	FetchDividendStock(ctx context.Context, stockCode string, page int) ([]*model.StockEvents, error)
}

// SSI service
type SSI interface {
	DownloadAllStockSinceYear(db *sql.DB, dbFinSSI *sql.DB, cfg *config.Config, year int) error
	DownloadAllStock(db *sql.DB, dbFinSSI *sql.DB, cfg *config.Config) error
	RemoveZeroColumn(db *sql.DB, dbFinSSI *sql.DB, numberOnlyReg *regexp.Regexp) error
	SwitchRustyFinancialStock(db *sql.DB) error
	StoreTradeStock(db *sql.DB, ssiOrg *model.SSIOrganization) error
	StoreFinancialReport(cfg *config.Config, dbFin *sql.DB, stockCode string, bs *model.SSIBalanceSheet, is *model.SSIIncomestatement) (string, error)
	FetchStockCodes() (*model.SSIOrganization, error)
	FetchIncomstatement(ssiStockCode string) (*model.SSIIncomestatement, error)
	FetchBalanceSheet(ssiStockCode string) (*model.SSIBalanceSheet, error)
}

// VSD service
type Vsd interface {
	FetchDelistStocksCache(ctx context.Context, offset int) ([]*model.DelistedStocks, error)
}

// Cophieu68 service
type Cophieu68 interface {
	Login(ctx context.Context) (context.Context, context.CancelFunc, error)
	FetchPriceStock(ctx context.Context, stockCode string, downloadPath string) error
}

// EzData service
type EzData interface {
	FetchAllStocks(ctx context.Context, offset int) (*model.EzStockList, error)
	FetchFinancialReport(ctx context.Context, fType string, year int, period int, ezID string, path string) error
}

// Cafef service
type Cafef interface {
	FetchFinancialReport(ctx context.Context, stock string, fType string, year int, page int, numberOnlyReg *regexp.Regexp) error
	FetchBankInterest(ctx context.Context, month int) (float64, error)
}

// Simp service
type Simplize interface {
	FetchStockPrices(ctx context.Context, stock string, condition string) error
	FetchFinancialQuarterly(ctx context.Context, stockCode string) (*model.SimpLatestQuarterRevRatio, error)
}

// Fireant service
type FireAnt interface {
	FetchVNIStockPrices(ctx context.Context, page int) error
}

// Mihong service
type Mihong interface {
	FetchGoldPrice(ctx context.Context) (*model.GoldResponse, error)
}

// Fialda service
type Fialda interface {
	FetchStockPrices(ctx context.Context, stockCode string, period string) (*model.FialdaPrices, error)
	FetchStockPricePagination(ctx context.Context, stockCode string, fromDate time.Time, toDate time.Time, page string) (*model.FialdaPricePagination, error)
}

// 24Hmoney service
type Hmoney interface {
	FetchBankInterestSixMonthChrome(ctx context.Context) (float64, error)
	FetchBankInterestSixMonth(ctx context.Context) (float64, error)
}

// StateMachine
type StateMachine interface {
	LoadUserChatObject(ctx context.Context, chatID string) (*model.ChatObject, error)
	DetectUserContext(ctx context.Context, chatObj *model.ChatObject, message string) (*model.NanoJob, error)
	BuildNextJob(ctx context.Context, jobResp *model.JobResponse) (*model.NanoJob, error)
	CancelUserState(ctx context.Context, chatID string) (*model.NanoJob, error)
	UserInput(ctx context.Context, chatObj *model.ChatObject, inputVal string) (*model.NanoJob, error)
	DetectWorkflowByChatObj(chatObj *model.ChatObject, message string) (string, error)
	DetectWorkflow(ctx context.Context, chatID string, message string) (string, error)
}

// Pacman
type JobEater interface {
	IngestJobWithPriority(ctx context.Context, job *model.NanoJob, isFirst bool) error
	IngestJob(ctx context.Context, job *model.NanoJob) error
	NudgeQueue()
	ReleaseTheJobEater(sm StateMachine) error
}

// Telegram
type Telegram interface {
	SendMessage(ctx context.Context, message *model.SendMessageReqBody) error
	SendChatAction(ctx context.Context, message *model.SendChatActionReqBody) error
	SetMessageReaction(ctx context.Context, message *model.SendMessageReactionReqBody) error
	NumberFormat(N interface{}, lang ...string) string
	StarFormat(stars int) string
	EscapeMarkdown(originalString string) string
}

// Facebook
type Facebook interface {
	SendMessage(ctx context.Context, message *model.FaceBookSendMessageReqBody) error
	GetUserInfo(ctx context.Context, userID string) (*model.FaceBookUserInfo, error)
	SendReqCustomerInfo(ctx context.Context, message *model.FaceBookSendMessageReqBody) error
}

//go:generate mockery --name=Redis --output=mocks
type Redis interface {
	Get(ctx context.Context, key string) (interface{}, error)
	Set(ctx context.Context, key string, value interface{}) error
	Del(ctx context.Context, key string) error
}
