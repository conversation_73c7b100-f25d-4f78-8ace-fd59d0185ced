package constants

import "errors"

type ERRORS struct {
	ERROR_BAD_PARAM_INPUT      error
	ERROR_INSUFFICIENT_STORAGE error
	ERROR_CONFLICT             error
	ERROR_NOT_FOUND            error
	ERROR_BAD_REQUEST          error
}

type REQUESTERRORS struct {
	SHOULD_RETRY      error
	REQUEST_EXISTED   error
	NEEDED_USER_INPUT error
}

type AUTHENTICATIONERROR struct {
	EXPIRED      error
	UNAUTHORIZED error
}

type BOTERROR struct {
	REQUESTERROR error
}

var (
	CommonError = ERRORS{
		ERROR_BAD_PARAM_INPUT:      errors.New("given param is not valid"),
		ERROR_INSUFFICIENT_STORAGE: errors.New("insufficient_storage"),
		ERROR_CONFLICT:             errors.New("your_item_already_exist"),
		ERROR_NOT_FOUND:            errors.New("your requested item is not found"),
		ERROR_BAD_REQUEST:          errors.New("bad_request"),
	}
	RequestError = REQUESTERRORS{
		SHOULD_RETRY:      errors.New("should retry request"),
		REQUEST_EXISTED:   errors.New("your request has been existed"),
		NEEDED_USER_INPUT: errors.New("your request need user input to fulfill"),
	}
	AuthenticationError = AUTHENTICATIONERROR{
		EXPIRED:      errors.New("authentication expired"),
		UNAUTHORIZED: errors.New("authentication unauthorized"),
	}
	BotError = BOTERROR{
		REQUESTERROR: errors.New("bot request error"),
	}
)
