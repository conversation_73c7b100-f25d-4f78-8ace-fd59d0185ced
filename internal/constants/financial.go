package constants

type FAFIELDS struct {
	Revenue               string
	GrossProfit           string
	InterestExpenses      string
	AdministratorExpenses string
	SaleExpenses          string
	NetIncomes            string
	OwnerEquities         string
	Cash                  string
	ShortTermInvestment   string
	Ebit                  string
	Liabilities           string
	CurrentLiabilities    string
	LongLiabilities       string
	PreferredDivs         string
	CommonShares          string
	PreferredEquities     string
}

type DATASOURCES struct {
	CafeFin string
	SSI     string
	SSC     string
	SSC119  string
	SSC85   string
	SSC107  string
}

var (
	Datasources = DATASOURCES{
		CafeFin: "cafef",
		SSI:     "ssi",
		SSC:     "ssc",
		SSC119:  "ssc119",
		SSC85:   "ssc85",
		SSC107:  "ssc107",
	}
	SSIFields = FAFIELDS{
		Revenue:               "isa1",
		GrossProfit:           "isa5",
		InterestExpenses:      "isa8",
		SaleExpenses:          "isa9",
		AdministratorExpenses: "isa10",
		Ebit:                  "ebit",
		NetIncomes:            "isa20",
		OwnerEquities:         "bsa78",
		Cash:                  "bsa2",
		ShortTermInvestment:   "bsa6",
		Liabilities:           "bsa54",
		CurrentLiabilities:    "bsa55",
		LongLiabilities:       "bsa67",
		PreferredDivs:         "bsa174",
		CommonShares:          "bsa175",
		PreferredEquities:     "bsb120",
	}
	CafeFFields = FAFIELDS{
		Revenue:               "IncSta10",
		GrossProfit:           "IncSta20",
		InterestExpenses:      "IncSta23",
		SaleExpenses:          "IncSta25",
		AdministratorExpenses: "IncSta26",
		Ebit:                  "IncSta50",
		NetIncomes:            "IncSta60",
		OwnerEquities:         "BSheet400",
		Cash:                  "BSheet110",
		ShortTermInvestment:   "BSheet120",
		Liabilities:           "BSheet300",
		CurrentLiabilities:    "BSheet310",
		LongLiabilities:       "BSheet330",
		PreferredDivs:         "BSheet340",
		CommonShares:          "BSheet411a",
		PreferredEquities:     "BSheet411b",
	}
	SSCFields119 = FAFIELDS{
		Revenue:               "IncSta119",
		GrossProfit:           "IncSta123",
		InterestExpenses:      "IncSta126",
		SaleExpenses:          "IncSta128",
		AdministratorExpenses: "IncSta129",
		Ebit:                  "IncSta134",
		NetIncomes:            "IncSta137",
		OwnerEquities:         "BSheet96",
		Cash:                  "BSheet2",
		ShortTermInvestment:   "BSheet5",
		Liabilities:           "BSheet66",
		CurrentLiabilities:    "BSheet67",
		LongLiabilities:       "BSheet82",
		PreferredDivs:         "BSheet92",
		CommonShares:          "BSheet99",
		PreferredEquities:     "BSheet100",
	}
	SSCFields107 = FAFIELDS{
		Revenue:               "IncSta107",
		GrossProfit:           "IncSta116",
		InterestExpenses:      "IncSta113",
		SaleExpenses:          "IncSta111",
		AdministratorExpenses: "IncSta114",
		Ebit:                  "IncSta116",
		NetIncomes:            "IncSta119",
		OwnerEquities:         "BSheet93",
		Cash:                  "BSheet2",
		ShortTermInvestment:   "BSheet5",
		Liabilities:           "BSheet62",
		CurrentLiabilities:    "BSheet63",
		LongLiabilities:       "BSheet83",
		PreferredDivs:         "BSheet91",
		CommonShares:          "BSheet95",
		PreferredEquities:     "BSheet105",
	}
	SSCFields85 = FAFIELDS{
		Revenue:               "IncSta85",
		GrossProfit:           "IncSta99",
		InterestExpenses:      "IncSta86",
		SaleExpenses:          "IncSta89",
		AdministratorExpenses: "IncSta98",
		Ebit:                  "IncSta101",
		NetIncomes:            "IncSta105",
		OwnerEquities:         "BSheet63",
		Cash:                  "BSheet1",
		ShortTermInvestment:   "BSheet2",
		Liabilities:           "BSheet62",
		CurrentLiabilities:    "BSheet49",
		LongLiabilities:       "BSheet50",
		PreferredDivs:         "BSheet92",
		CommonShares:          "BSheet65",
		PreferredEquities:     "BSheet69",
	}
)
