package cafef

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"math/rand"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/PuerkitoBio/goquery"
)

func DownloadFinReportCache(cfg *config.Config, db *sql.DB, cff contract.Cafef, numberOnlyReg *regexp.Regexp, negativeReg *regexp.Regexp, year int) error {
	cursor := ""
	fTypes := []string{"bsheet", "incsta"}
	for {
		stockCode, stopYear, err := getStockInfoFromYear(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil
			}
			return err
		}
		cursor = stockCode
		if stopYear >= year {
			continue
		}

		for _, fType := range fTypes {
			prevYear, err := downloadCafeFinancial(cfg, cff, fType, stockCode, year, 0, numberOnlyReg, negativeReg)
			if err != nil {
				if errors.Is(err, constants.CommonError.ERROR_NOT_FOUND) {
					continue
				}
				if errors.Is(err, constants.RequestError.SHOULD_RETRY) {
					sleepRandom(10)
				} else {
					return err
				}
			}
			for prevYear > 0 && prevYear > stopYear {
				prevYear, err = downloadCafeFinancial(cfg, cff, fType, stockCode, prevYear, year-prevYear, numberOnlyReg, negativeReg)
				if err != nil {
					if errors.Is(err, constants.CommonError.ERROR_NOT_FOUND) {
						continue
					}
					if errors.Is(err, constants.RequestError.SHOULD_RETRY) {
						sleepRandom(10)
					} else {
						return err
					}
				}
			}
		}
	}
}

func sleepRandom(limit int) {
	rd := rand.New(rand.NewSource(time.Now().UnixNano()))
	n := rd.Intn(limit) // n will be between 0 and limit
	fmt.Printf("Sleeping %d seconds...\n", n)
	time.Sleep(time.Duration(n) * time.Second)
}

func findLatestYear(yearString string) (int, error) {
	// 1. Handle empty input
	if strings.TrimSpace(yearString) == "" {
		return 0, fmt.Errorf("input string is empty")
	}

	// 2. Split the string by the comma delimiter
	parts := strings.Split(yearString, ",")

	latestYear := 0         // Initialize with a value lower than any expected year
	foundValidYear := false // Flag to track if we found at least one valid year

	// 3. Iterate through each part
	for _, part := range parts {
		// 4. Trim the backticks (`) from the beginning and end
		trimmedPart := strings.Trim(part, "`")

		// 5. Trim any extra whitespace (optional but good practice)
		trimmedPart = strings.TrimSpace(trimmedPart)

		// 6. Convert the cleaned string part to an integer
		year, err := strconv.Atoi(trimmedPart)
		if err != nil {
			// If conversion fails, log a warning and skip this part
			fmt.Printf("Warning: Skipping invalid entry '%s': %v\n", part, err)
			continue // Move to the next part
		}

		// 7. Compare with the current latest year
		// Update if it's the first valid year found OR if it's greater than the current latest
		if !foundValidYear || year > latestYear {
			latestYear = year
			foundValidYear = true // Mark that we've found at least one valid year
		}
	}

	// 8. Check if any valid year was actually found
	if !foundValidYear {
		return 0, fmt.Errorf("no valid years found in the input string")
	}

	// 9. Return the latest year found
	return latestYear, nil
}

func getStockInfoFromYear(db *sql.DB, cursor string) (string, int, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	stmt, err := db.PrepareContext(ctx, "SELECT id, years FROM stocks WHERE data_source=$1 AND id > $2 ORDER BY id LIMIT 1")
	if err != nil {
		return "", 0, err
	}
	r := stmt.QueryRowContext(ctx, constants.Datasources.CafeFin, cursor)

	var (
		stockCode string
		yearsStr  sql.NullString
	)
	if err := r.Scan(&stockCode, &yearsStr); err != nil {
		return "", 0, err
	}
	if !yearsStr.Valid {
		return stockCode, 0, nil
	}
	latestYear, err := findLatestYear(yearsStr.String)
	if err != nil {
		return "", 0, err
	}
	return stockCode, latestYear, nil
}

func downloadCafeFinancial(cfg *config.Config, cff contract.Cafef, fType string, stock string, year int, page int, numberOnlyReg *regexp.Regexp, negativeReg *regexp.Regexp) (int, error) {
	var files []string
	fileCache := fmt.Sprintf("%s/%s_%s-%d.html", cfg.StoragePath.CafefData, fType, stock, year)
	files, err := filepath.Glob(fileCache)
	if err != nil {
		return 0, err
	}

	if len(files) == 0 {
		fmt.Printf("Download financial report: %s...\n", stock)
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		err := cff.FetchFinancialReport(ctx, stock, fType, year, page, numberOnlyReg)
		if err != nil {
			return 0, err
		}
	}
	// Find for next page
	files, err = filepath.Glob(fmt.Sprintf("%s/%s_%s-*", cfg.StoragePath.CafefData, fType, stock))
	if err != nil {
		return 0, err
	}
	smallestCrawledYear := year
	for _, filePath := range files {
		foundNumbers := numberOnlyReg.FindAllString(filePath, -1)
		yearStr := strings.Join(foundNumbers, "")
		yearNumber, err := strconv.Atoi(yearStr)
		if err != nil {
			return 0, err
		}
		if yearNumber < smallestCrawledYear {
			smallestCrawledYear = yearNumber
		}
	}
	filePath := fmt.Sprintf("%s/%s_%s-%d.html", cfg.StoragePath.CafefData, fType, stock, smallestCrawledYear)
	if _, err = os.Stat(filePath); os.IsNotExist(err) {
		return 0, constants.CommonError.ERROR_NOT_FOUND
	}
	// Find for next page
	f, err := os.OpenFile(filePath, os.O_RDONLY, 0644)
	if err != nil {
		return 0, err
	}
	defer f.Close()
	doc, err := goquery.NewDocumentFromReader(f)
	if err != nil {
		return 0, err
	}

	var years []string
	doc.Find("table#tblGridData tr").Each(func(i int, s *goquery.Selection) {
		// fmt.Println(s.Text())
		s.Find("td").Each(func(i int, s *goquery.Selection) {
			if i > 0 && i < 5 {
				years = append(years, fmt.Sprintf("`%s`", strings.TrimSpace(s.Text())))
			}
		})
	})
	period := len(years)
	if period == 0 {
		return 0, os.Remove(filePath)
	}
	colIndex := make([]int64, period)
	doc.Find("table#tableContent tr").Each(func(i int, s *goquery.Selection) {
		// tID, _ := s.Attr("id")
		_, cols, err := crawlCafefData(s, numberOnlyReg, negativeReg)
		if err != nil {
			return
		}
		// fmt.Println(tID, title, cols)
		for i, v := range cols {
			colIndex[i] += v
		}
	})
	isBreak := false
	for _, v := range colIndex {
		if v == 0 {
			isBreak = true
			break
		}
	}
	if isBreak {
		return 0, nil
	}
	return smallestCrawledYear - period, nil
}

func crawlCafefData(doc *goquery.Selection, numberOnlyReg *regexp.Regexp, negativeReg *regexp.Regexp) (string, []int64, error) {
	var cols []int64
	title := ""
	doc.Find("td").Each(func(i int, s *goquery.Selection) {
		if i == 0 {
			title = strings.TrimSpace(s.Text())
		} else if i < 5 {
			// parse number
			col := s.Text()
			foundNumbers := numberOnlyReg.FindAllString(col, -1)
			if len(foundNumbers) == 0 {
				cols = append(cols, 0)
				return
			}
			number := strings.Join(foundNumbers, "")
			intVar, err := strconv.ParseInt(number, 10, 64)
			if err != nil {
				fmt.Println(err)
				cols = append(cols, 0)
				return
			}
			if negativeReg.MatchString(col) {
				intVar *= -1
			}

			cols = append(cols, intVar)
		}
	})
	if title != "" {
		return title, cols, nil
	}
	return "", nil, constants.CommonError.ERROR_NOT_FOUND
}
