package cafef

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/PuerkitoBio/goquery"
	"github.com/corpix/uarand"
	"github.com/gosimple/slug"
)

type cafefService struct {
	cfg       *config.Config
	transport *http.Transport
	cookies   []*http.Cookie
}

func NewCafefService(cfg *config.Config, trans *http.Transport) contract.Cafef {
	return &cafefService{cfg, trans, nil}
}

func (cs *cafefService) FetchFinancialReport(ctx context.Context, stockCode string, fType string, year int, page int, numberOnlyReg *regexp.Regexp) error {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: cs.transport,
	}
	ua := uarand.GetRandom()

	// Crawl info to build url
	originUrl := "https://cafef.vn"
	slugPrefix := "ket-qua-hoat-dong-kinh-doanh" //IncSta
	if fType == "bsheet" {
		slugPrefix = "bao-cao-tai-chinh"
	}

	// Crawl main page
	mainUrl := fmt.Sprintf("https://cafef.vn/du-lieu/bao-cao-tai-chinh/%s/%s/%d/0/%d/0/%s-.chn", slug.Make(stockCode), fType, year, page, slugPrefix)
	fmt.Println(mainUrl)
	req, err := http.NewRequest("GET", mainUrl, nil)
	if err != nil {
		return err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("origin", originUrl)
	req.Header.Set("referer", originUrl)
	req.Header.Set("Content-Type", "text/html; charset=utf-8")
	for _, cookie := range cs.cookies {
		req.AddCookie(cookie)
	}

	// fmt.Println("=============Request===============")
	// fmt.Printf("%s\n", req.URL.String())
	// fmt.Println(form.Encode())

	var e net.Error
	response, err := client.Do(req)
	if errors.As(err, &e) && e.Timeout() {
		return constants.RequestError.SHOULD_RETRY
	}
	defer response.Body.Close()

	if response.StatusCode == 403 {
		return constants.RequestError.SHOULD_RETRY
	}

	// Read the latest year
	doc, err := goquery.NewDocumentFromReader(response.Body)
	if err != nil {
		return err
	}

	crawledYear := int64(0)
	doc.Find("table#tblGridData tr").Each(func(i int, s *goquery.Selection) {
		s.Find("td").Each(func(i int, s *goquery.Selection) {
			if i > 0 && i < 5 {
				foundNumbers := numberOnlyReg.FindAllString(s.Text(), -1)
				yearStr := strings.Join(foundNumbers, "")
				yearNumber, err := strconv.ParseInt(yearStr, 10, 64)
				if err != nil {
					fmt.Println(err)
				}
				if yearNumber > crawledYear {
					crawledYear = yearNumber
				}
			}
		})
	})

	filePath := fmt.Sprintf("%s/%s_%s-%d.html", cs.cfg.StoragePath.CafefData, fType, stockCode, crawledYear)
	f, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer f.Close()
	ret, err := doc.Html()
	if err != nil {
		return err
	}
	_, err = f.WriteString(ret)
	if err != nil {
		return err
	}

	return nil
}
