package cafef

import (
	"context"
	"database/sql"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/PuerkitoBio/goquery"
)

func CafefExtractFinancialData(cfg *config.Config, db *sql.DB, dbFin *sql.DB, numberOnlyReg *regexp.Regexp, negativeReg *regexp.Regexp, year int) error {
	cursor := ""
	for {
		stockCode, latestYear, err := getStockInfoFromYear(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return err
		}
		cursor = stockCode

		if latestYear == 0 {
			yearCols, err := getAllYears(dbFin, stockCode, numberOnlyReg)
			if err != nil {
				return err
			}
			fmt.Println(yearCols)
			if len(yearCols) == 0 {
				err := os.Remove(fmt.Sprintf("%s/%s.csv", cfg.StoragePath.CafFin, stockCode))
				if err != nil {
					return err
				}
			}
			err = updateYears(db, stockCode, yearCols)
			if err != nil {
				return err
			}
			continue
		}

		if latestYear >= year {
			continue
		}
		fmt.Println(constants.Datasources.CafeFin, "id:", stockCode, "year:", year)

		// Balance sheet
		fTypes := []string{"bsheet", "incsta"}
		for _, fType := range fTypes {
			fileCache := fmt.Sprintf("%s/%s_%s-*", cfg.StoragePath.CafefData, fType, stockCode)
			files, err := filepath.Glob(fileCache)
			if err != nil {
				return err
			}
			for _, filePath := range files {
				err = cafeFCacheToCsv(filePath, dbFin, stockCode, fType, fType == "incsta", numberOnlyReg, negativeReg)
				if err != nil {
					return err
				}
			}
		}

		finPath := fmt.Sprintf("%s/%s.csv", cfg.StoragePath.CafFin, stockCode)
		if _, err := os.Stat(finPath); os.IsNotExist(err) {
			continue
		}

		yearCols, err := getAllYears(dbFin, stockCode, numberOnlyReg)
		if err != nil {
			return err
		}
		fmt.Println(yearCols)
		if len(yearCols) == 0 {
			err := os.Remove(fmt.Sprintf("%s/%s.csv", cfg.StoragePath.CafFin, stockCode))
			if err != nil {
				return err
			}
		}
		err = updateYears(db, stockCode, yearCols)
		if err != nil {
			return err
		}
		err = cafeFPathNullFieldToZero(cfg, stockCode)
		if err != nil {
			return err
		}
	}
	return nil
}

func cafeFCacheToCsv(cachePath string, dbFin *sql.DB, stock string, fType string, isDelCol bool, numberOnlyReg *regexp.Regexp, negativeReg *regexp.Regexp) error {
	f, err := os.OpenFile(cachePath, os.O_RDONLY, 0644)
	if err != nil {
		return err
	}
	defer f.Close()
	doc, err := goquery.NewDocumentFromReader(f)
	if err != nil {
		return err
	}

	var years []string
	doc.Find("table#tblGridData tr").Each(func(i int, s *goquery.Selection) {
		// fmt.Println(s.Text())
		s.Find("td").Each(func(i int, s *goquery.Selection) {
			if i > 0 && i < 5 {
				years = append(years, fmt.Sprintf("`%s`", strings.TrimSpace(s.Text())))
			}
		})
	})
	for _, y := range years {
		// Stop empty years
		if y == "``" {
			return nil
		}
	}
	yearHeaders := strings.Join(years, ",")
	// Start transaction
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	tx, err := dbFin.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
			return
		}
		tx.Commit()
	}()

	ctx = context.WithValue(ctx, constants.ContextTransactionKey, tx)
	if !checkTableExist(ctx, yearHeaders) {
		err := createTableReport(ctx, stock, yearHeaders)
		if err != nil {
			return err
		}
	}
	err = checkFieldsTableExist(ctx, stock, years, numberOnlyReg)
	if err != nil {
		return err
	}
	colIdx := make([]int64, len(years))
	doc.Find("table#tableContent tr").Each(func(i int, s *goquery.Selection) {
		tID, _ := s.Attr("id")
		title, cols, err := crawlCafefData(s, numberOnlyReg, negativeReg)
		if err != nil {
			return
		}
		// fmt.Println(tID, title, cols)
		for i, v := range cols {
			colIdx[i] += v
		}
		genID := fmt.Sprintf("%s%s", fType, tID)
		err = insertData(ctx, stock, years, yearHeaders, genID, title, cols)
		if err != nil {
			fmt.Println(err)
			return
		}
	})
	if isDelCol {
		for i := 0; i < len(colIdx); i++ {
			if colIdx[i] == 0 {
				err = removeColumn(ctx, stock, years[i])
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func removeColumn(ctx context.Context, table string, year string) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	query := "ALTER TABLE `" + table + ".csv` DROP " + year
	fmt.Println(query)
	_, err := tx.ExecContext(ctx, query)
	if err != nil {
		return err
	}
	return nil
}

func createTableReport(ctx context.Context, table string, years string) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	_, err := tx.Query(fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", table))
	if err != nil {
		_, err = tx.ExecContext(ctx, fmt.Sprintf("CREATE TABLE `%s.csv`(id, title, %s)", table, years))
		if err != nil {
			return err
		}
	}
	return nil
}

func checkFieldsTableExist(ctx context.Context, table string, fields []string, r *regexp.Regexp) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)

	maxYear := 0
	rs, err := tx.QueryContext(ctx, fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", table))
	if err != nil && err != sql.ErrNoRows {
		return nil
	}
	defer rs.Close()
	cols, err := rs.Columns()
	if err != nil {
		return nil
	}
	var newFields []string
	prepend := false
	for _, year := range fields {
		isAppend := true
		for _, col := range cols {
			if r.MatchString(col) {
				y, _ := strconv.Atoi(col)
				if maxYear < y {
					maxYear = y
				}
			}
			if year == fmt.Sprintf("`%s`", col) {
				isAppend = false
				break
			}
		}
		if isAppend {
			strY := strings.ReplaceAll(year, "`", "")
			aY, _ := strconv.Atoi(strY)
			if aY > maxYear {
				prepend = true
			}
			newFields = append(newFields, year)
		}
	}

	if len(newFields) > 0 {
		alterQuery := fmt.Sprintf("ALTER TABLE `"+table+".csv` ADD (%s) AFTER title", strings.Join(newFields, ","))
		if !prepend {
			alterQuery = fmt.Sprintf("ALTER TABLE `"+table+".csv` ADD (%s)", strings.Join(newFields, ","))
		}
		fmt.Println(alterQuery)
		rs, err = tx.QueryContext(ctx, alterQuery)
		if err != nil {
			return err
		}
		defer rs.Close()
	}

	return nil
}

func checkTableExist(ctx context.Context, table string) bool {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	_, err := tx.Query(fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", table))
	if err != nil {
		return err == sql.ErrNoRows
	}
	return true
}

func insertData(ctx context.Context, table string, years []string, yearHeaders string, ID string, title string, cols []int64) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	stmt, err := tx.Prepare("SELECT id FROM `" + table + ".csv` WHERE id = ?")
	if err != nil {
		return err
	}
	r := stmt.QueryRowContext(ctx, ID)
	var id string
	if err = r.Scan(&id); err != nil {
		if err == sql.ErrNoRows {
			fmt.Printf("%s No Rows. Inserting\n", ID)
			query := fmt.Sprintf("INSERT INTO `%s.csv`(id,title,%s) VALUES(?,?,%s)", table, yearHeaders, strings.Trim(strings.Join(strings.Fields(fmt.Sprint(cols)), ","), "[]"))
			stmt, err = tx.Prepare(query)
			if err != nil {
				return err
			}
			_, err = stmt.ExecContext(ctx, ID, title)
			if err != nil {
				return err
			}
			return nil
		}
		return err
	} else {
		var statements []string
		for _, year := range years {
			statements = append(statements, fmt.Sprintf("%s=?", year))
		}
		stmt, err = tx.Prepare(fmt.Sprintf("UPDATE `%s.csv` SET %s WHERE id=?", table, strings.Join(statements, ",")))
		if err != nil {
			return err
		}
		var updateParam []interface{}
		for _, col := range cols {
			updateParam = append(updateParam, col)
		}
		updateParam = append(updateParam, ID)
		_, err = stmt.ExecContext(ctx, updateParam...)
		if err != nil {
			return err
		}
	}
	return nil
}

func getAllYears(dbFin *sql.DB, table string, numberOnlyReg *regexp.Regexp) ([]string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	rs, err := dbFin.QueryContext(ctx, fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", table))
	if err != nil {
		return nil, err
	}
	defer rs.Close()
	cols, err := rs.Columns()
	if err != nil {
		return nil, err
	}
	var years []string
	for _, col := range cols {
		if numberOnlyReg.MatchString(col) {
			years = append(years, fmt.Sprintf("`%s`", col))
		}
	}
	sort.Strings(years)
	return years, nil
}

func updateYears(db *sql.DB, stock string, years []string) error {
	// Start transaction
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
			return
		}
		tx.Commit()
	}()

	stmt, err := tx.Prepare("UPDATE stocks SET years=$1, updated_at=NOW() WHERE id=$2")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, strings.Join(years, ","), stock)
	if err != nil {
		return err
	}
	return nil
}

func cafeFPathNullFieldToZero(cfg *config.Config, stock string) error {
	stockFilePath := fmt.Sprintf("%s/%s.csv", cfg.StoragePath.CafFin, stock)
	if _, err := os.Stat(stockFilePath); os.IsNotExist(err) {
		return nil
	}
	stockFilePathW := fmt.Sprintf("%s/%s-temp.csv", cfg.StoragePath.CafFin, stock)
	// Path file
	csvIn, err := os.Open(stockFilePath)
	if err != nil {
		return err
	}
	defer csvIn.Close()
	reader := csv.NewReader(csvIn)

	csvOut, err := os.OpenFile(stockFilePathW, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0755)
	if err != nil {
		return err
	}
	writer := csv.NewWriter(csvOut)
	defer csvOut.Close()

	for {
		rec, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		var records []string
		for _, r := range rec {
			if r == "" {
				records = append(records, "0")
			} else {
				records = append(records, r)
			}
		}
		writer.Write(records)
	}
	writer.Flush()
	// Swap file
	err = os.Remove(stockFilePath)
	if err != nil {
		return err
	}
	err = os.Rename(stockFilePathW, stockFilePath)
	if err != nil {
		return err
	}
	return nil
}
