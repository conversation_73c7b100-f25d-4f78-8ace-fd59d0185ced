package cafef

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/007lock/simon-homestead/pkg/model"
	"net"
	"net/http"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/corpix/uarand"
)

func (cs *cafefService) FetchBankInterest(ctx context.Context, month int) (float64, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: cs.transport,
	}
	ua := uarand.GetRandom()

	infoUrl := "https://s.cafef.vn/ajax/ajaxlaisuatnganhang.ashx"
	req, err := http.NewRequest(http.MethodGet, infoUrl, nil)
	if err != nil {
		return 0, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Referer", "https://s.cafef.vn/lai-suat-ngan-hang.chn")
	req.Header.Set("Content-Type", "application/json, text/plain, */*")

	response, err := client.Do(req)
	var e net.Error
	if errors.As(err, &e) && e.Timeout() {
		return 0, constants.RequestError.SHOULD_RETRY
	}
	defer response.Body.Close()

	interestResp := new(model.CafefBankInterest)
	err = json.NewDecoder(response.Body).Decode(interestResp)
	if err != nil {
		return 0, err
	}

	maxInterest := 0.0
	for _, data := range interestResp.Data {
		for _, deposit := range data.InterestRates {
			if deposit.Deposit == month && maxInterest < deposit.Value {
				maxInterest = deposit.Value
			}
		}
	}
	return maxInterest / 100, nil
}
