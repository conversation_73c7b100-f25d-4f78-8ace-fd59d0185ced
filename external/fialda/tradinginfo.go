package fialda

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/corpix/uarand"
)

type fialdaService struct {
	cfg       *config.Config
	transport *http.Transport
}

func NewFialda(cfg *config.Config, trans *http.Transport) contract.Fialda {
	return &fialdaService{cfg, trans}
}

func (fl *fialdaService) FetchStockPrices(ctx context.Context, stockCode string, period string) (*model.FialdaPrices, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: fl.transport,
	}
	ua := uarand.GetRandom()

	req, err := http.NewRequest(http.MethodGet, "https://fwtapi1.fialda.com/api/services/app/StockInfo/GetChart", nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", "https://fwt.fialda.com")
	req.Header.Set("Referer", "https://fwt.fialda.com/")
	req.Header.Set("Accept", "*/*")
	// req.Header.Set("sa", "134016496634348298523")
	req.Header.Set("appid", "F7335346-0CB8-49A1-B9CB-A59504CBEF14")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")

	q := req.URL.Query()
	q.Add("symbol", stockCode)
	q.Add("chartPedirod", period) // today, sixMonths
	req.URL.RawQuery = q.Encode()

	response, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	if response.StatusCode == 403 {
		return nil, constants.RequestError.SHOULD_RETRY
	}

	// s := strings.TrimSpace(string(content))
	// fmt.Println("=============Response===============")
	// fmt.Println(s)

	stockTrade := new(model.FialdaPrices)
	err = json.NewDecoder(response.Body).Decode(stockTrade)
	if err != nil {
		return nil, err
	}
	return stockTrade, nil
}

func (fl *fialdaService) FetchStockPricePagination(ctx context.Context, stockCode string, fromDate time.Time, toDate time.Time, page string) (*model.FialdaPricePagination, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: fl.transport,
	}
	ua := uarand.GetRandom()

	req, err := http.NewRequest(http.MethodGet, "https://fwtapi2.fialda.com/api/services/app/StockInfo/GetHistoricalData", nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", "https://fwt.fialda.com")
	req.Header.Set("Referer", "https://fwt.fialda.com/")
	req.Header.Set("Accept", "*/*")
	// req.Header.Set("sa", "134016496634348298523")
	req.Header.Set("appid", "F7335346-0CB8-49A1-B9CB-A59504CBEF14")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")

	q := req.URL.Query()
	q.Add("symbol", stockCode)
	q.Add("fromDate", fromDate.Format("2006-01-02T15:04:05.999"))
	q.Add("toDate", toDate.Format("2006-01-02T15:04:05.999"))
	q.Add("pageNumber", page)
	q.Add("pageSize", "25")
	req.URL.RawQuery = q.Encode()
	fmt.Println("=============Request===============")
	fmt.Println(req.URL.String())

	response, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	if response.StatusCode == 403 {
		return nil, constants.RequestError.SHOULD_RETRY
	}

	// s := strings.TrimSpace(string(content))
	// fmt.Println("=============Response===============")
	// fmt.Println(s)

	stockTrade := new(model.FialdaPricePagination)
	err = json.NewDecoder(response.Body).Decode(stockTrade)
	if err != nil {
		return nil, err
	}
	return stockTrade, nil
}
