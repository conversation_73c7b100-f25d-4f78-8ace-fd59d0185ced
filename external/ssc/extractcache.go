package ssc

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/PuerkitoBio/goquery"
)

func ExtractCacheToCSV(cfg *config.Config, db *sql.DB, dbFin *sql.DB) error {
	offset := 0
	for {
		stock, err := getSSCStocks(db, offset)
		if err != nil {
			if err == sql.ErrNoRows {
				break
			}
			return err
		}
		years := ""
		_, _, err = extractCache(cfg, dbFin, stock, "BSheet")
		if err != nil {
			return err
		}
		years, dataSource, err := extractCache(cfg, dbFin, stock, "IncSta")
		if err != nil {
			return err
		}
		err = updateYearHeader(db, stock, years, dataSource)
		if err != nil {
			return err
		}
		offset++
	}
	return nil
}

func updateYearHeader(db *sql.DB, stock string, years string, source string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	stmt, err := db.PrepareContext(ctx, "UPDATE stocks SET years = $1, data_source = $2 WHERE id = $3")
	if err != nil {
		return err
	}
	// fmt.Println(query)
	_, err = stmt.ExecContext(ctx, years, source, stock)
	if err != nil {
		return err
	}
	return nil
}

func getSSCStocks(db *sql.DB, offset int) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	var (
		id string
	)
	stmt, err := db.PrepareContext(ctx, fmt.Sprintf("SELECT id FROM stocks WHERE data_source = $1 OR data_source = $2 OR data_source = $3 OR data_source = $4 ORDER BY id ASC LIMIT 1 OFFSET %d", offset))
	if err != nil {
		return "", err
	}
	err = stmt.QueryRowContext(ctx, constants.Datasources.SSC, constants.Datasources.SSC119, constants.Datasources.SSC107, constants.Datasources.SSC85).Scan(&id)
	if err != nil {
		return "", err
	}

	return id, nil
}

func extractCache(cfg *config.Config, dbFin *sql.DB, stock string, fType string) (string, string, error) {
	// Regex
	numberOnlyReg, err := regexp.Compile(`-*\d+`)
	if err != nil {
		return "", "", err
	}

	spaceReg, err := regexp.Compile(`\s+`)
	if err != nil {
		return "", "", err
	}

	files, err := filepath.Glob(fmt.Sprintf("%s/%s_%s-*.html", cfg.StoragePath.SSCData, fType, stock))
	if err != nil {
		return "", "", err
	}
	f, err := os.OpenFile(files[0], os.O_RDONLY, 0644)
	if err != nil {
		return "", "", err
	}
	defer f.Close()
	doc, err := goquery.NewDocumentFromReader(f)
	if err != nil {
		return "", "", err
	}

	var years []string
	doc.Find(`#r3\:0\:tt1\:\:ch\:\:t tr`).Each(func(i int, s *goquery.Selection) {
		s.Find("th").Each(func(i int, s *goquery.Selection) {
			if numberOnlyReg.MatchString(s.Text()) {
				years = append(years, fmt.Sprintf("`%s`", strings.TrimSpace(s.Text())))
			}
		})
	})
	yearHeaders := strings.Join(years, ",")
	// Start transaction
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	tx, err := dbFin.Begin()
	if err != nil {
		return "", "", err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
			return
		}
		tx.Commit()
	}()

	ctx = context.WithValue(ctx, constants.ContextTransactionKey, tx)
	if !sscCheckTableExist(ctx, yearHeaders) {
		err := sscCreateTableReport(ctx, stock, yearHeaders)
		if err != nil {
			return "", "", err
		}
	}
	err = sscCheckFieldsTableExist(ctx, stock, years, numberOnlyReg)
	if err != nil {
		return "", "", err
	}
	dataSource := constants.Datasources.SSC119
	doc.Find(`table.x14q.x15f tr`).Each(func(i int, s *goquery.Selection) {
		id := s.AttrOr("_afrrk", "")
		IDtable := fmt.Sprintf("%s%s", fType, id)
		if i == 0 {
			if IDtable == "IncSta85" {
				dataSource = constants.Datasources.SSC85
			} else if IDtable == "IncSta107" {
				dataSource = constants.Datasources.SSC107
			}
		}
		title, cols := extractHeaderNumber(s, numberOnlyReg, spaceReg)
		err := insertData(ctx, stock, years, yearHeaders, IDtable, title, cols)
		if err != nil {
			fmt.Println(err)
			return
		}
	})
	return yearHeaders, dataSource, nil
}

func extractHeaderNumber(doc *goquery.Selection, numberOnlyReg *regexp.Regexp, spaceReg *regexp.Regexp) (title string, cols []int64) {
	// Get ID
	doc.Find("td").Each(func(i int, s *goquery.Selection) {
		if i == 0 {
			title = spaceReg.ReplaceAllString(s.Text(), " ")
		} else {
			// parse number
			col := s.Text()
			foundNumbers := numberOnlyReg.FindAllString(col, -1)
			if len(foundNumbers) == 0 {
				cols = append(cols, 0)
				return
			}
			number := strings.Join(foundNumbers, "")
			intVar, err := strconv.ParseInt(number, 10, 64)
			if err != nil {
				fmt.Println(err)
				cols = append(cols, 0)
				return
			}

			cols = append(cols, intVar)
		}
	})
	return title, cols
}

func insertData(ctx context.Context, table string, years []string, yearHeaders string, ID string, title string, cols []int64) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	stmt, err := tx.Prepare("SELECT id FROM `" + table + ".csv` WHERE id = ?")
	if err != nil {
		return err
	}
	r := stmt.QueryRowContext(ctx, ID)
	var id string
	if err = r.Scan(&id); err != nil {
		if err == sql.ErrNoRows {
			fmt.Printf("%s No Rows. Inserting\n", ID)
			query := fmt.Sprintf("INSERT INTO `%s.csv`(id,title,%s) VALUES(?,?,%s)", table, yearHeaders, strings.Trim(strings.Join(strings.Fields(fmt.Sprint(cols)), ","), "[]"))
			stmt, err = tx.Prepare(query)
			if err != nil {
				return err
			}
			_, err = stmt.ExecContext(ctx, ID, title)
			if err != nil {
				return err
			}
			return nil
		}
		return err
	} else {
		var statements []string
		for _, year := range years {
			statements = append(statements, fmt.Sprintf("%s=?", year))
		}
		stmt, err = tx.Prepare(fmt.Sprintf("UPDATE `%s.csv` SET %s WHERE id=?", table, strings.Join(statements, ",")))
		if err != nil {
			return err
		}
		var updateParam []interface{}
		for _, col := range cols {
			updateParam = append(updateParam, col)
		}
		updateParam = append(updateParam, ID)
		_, err = stmt.ExecContext(ctx, updateParam...)
		if err != nil {
			return err
		}
	}
	return nil
}

func sscCheckTableExist(ctx context.Context, table string) bool {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	_, err := tx.Query(fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", table))
	if err != nil {
		return err == sql.ErrNoRows
	}
	return true
}

func sscCreateTableReport(ctx context.Context, table string, years string) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	_, err := tx.Query(fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", table))
	if err != nil {
		_, err = tx.ExecContext(ctx, fmt.Sprintf("CREATE TABLE `%s.csv`(id, title, %s)", table, years))
		if err != nil {
			return err
		}
	}
	return nil
}

func sscCheckFieldsTableExist(ctx context.Context, table string, fields []string, r *regexp.Regexp) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)

	maxYear := 0
	rs, err := tx.QueryContext(ctx, fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", table))
	if err != nil && err != sql.ErrNoRows {
		return nil
	}
	defer rs.Close()
	cols, err := rs.Columns()
	if err != nil {
		return nil
	}
	var newFields []string
	prepend := false
	for _, year := range fields {
		isAppend := true
		for _, col := range cols {
			if r.MatchString(col) {
				y, _ := strconv.Atoi(col)
				if maxYear < y {
					maxYear = y
				}
			}
			if year == fmt.Sprintf("`%s`", col) {
				isAppend = false
				break
			}
		}
		if isAppend {
			strY := strings.ReplaceAll(year, "`", "")
			aY, _ := strconv.Atoi(strY)
			if aY > maxYear {
				prepend = true
			}
			newFields = append(newFields, year)
		}
	}

	if len(newFields) > 0 {
		alterQuery := fmt.Sprintf("ALTER TABLE `"+table+".csv` ADD (%s) AFTER title", strings.Join(newFields, ","))
		if !prepend {
			alterQuery = fmt.Sprintf("ALTER TABLE `"+table+".csv` ADD (%s)", strings.Join(newFields, ","))
		}
		fmt.Println(alterQuery)
		rs, err = tx.QueryContext(ctx, alterQuery)
		if err != nil {
			return err
		}
		defer rs.Close()
	}

	return nil
}
