package ssc

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/PuerkitoBio/goquery"
	"github.com/chromedp/chromedp"
)

func DownloadAllFinReports(cfg *config.Config, db *sql.DB) error {
	offset := 0
	chromeCtx, cancel := newChromedp()
	defer cancel()

	btnSearchComp := `//*[@id="pt9:pt_np2:1:pt_cni3::disclosureAnchor"]`
	if err := chromedp.Run(chromeCtx, chromedp.Tasks{
		chromedp.Navigate("https://congbothongtin.ssc.gov.vn"),
		chromedp.Click(btnSearchComp),
	}); err != nil {
		return err
	}

	for {
		stock, err := getSSCNullYearStocks(db, offset)
		if err != nil {
			if err == sql.ErrNoRows {
				break
			}
			return err
		}
		err = crawlStock(chromeCtx, cfg, stock)
		if err != nil {
			if err == constants.CommonError.ERROR_NOT_FOUND {
				// Remove stock
				err = removeStock(cfg, db, stock)
				if err != nil {
					return err
				}
			} else {
				return err
			}

		}
		// If everything seems ok just update the datasource
		err = updateDatasource(db, stock)
		if err != nil {
			return err
		}
		offset++
	}
	return nil
}

func updateDatasource(db *sql.DB, stock string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	stmt, err := db.PrepareContext(ctx, "UPDATE stocks SET years = NULL, data_source = $1 WHERE id = $2")
	if err != nil {
		return err
	}
	// fmt.Println(query)
	_, err = stmt.ExecContext(ctx, constants.Datasources.SSC, stock)
	if err != nil {
		return err
	}
	return nil
}

func getSSCNullYearStocks(db *sql.DB, offset int) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	var (
		id string
	)
	stmt, err := db.PrepareContext(ctx, fmt.Sprintf("SELECT id FROM stocks WHERE data_source = $1 AND years is NULL ORDER BY id ASC LIMIT 1 OFFSET %d", offset))
	if err != nil {
		return "", err
	}
	err = stmt.QueryRowContext(ctx, constants.Datasources.SSC).Scan(&id)
	if err != nil {
		return "", err
	}

	return id, nil
}

func crawlStock(chromeCtx context.Context, cfg *config.Config, stock string) error {
	var files []string
	files, err := filepath.Glob(fmt.Sprintf("%s/%s_%s-*", cfg.StoragePath.SSCData, "BSheet", stock))
	if err != nil {
		return err
	}
	foundBSheet := len(files) > 0
	files, err = filepath.Glob(fmt.Sprintf("%s/%s_%s-*", cfg.StoragePath.SSCData, "IncSta", stock))
	if err != nil {
		return err
	}
	foundIncSta := len(files) > 0
	// Found cache
	if foundBSheet && foundIncSta {
		return nil
	}

	var body string

	btnSearchComp := `//*[@id="pt9:pt_np2:1:pt_cni3::disclosureAnchor"]`
	btnSearchCode := `//*[@id="pt2:it8::content"]`
	btnSearchSubmit := `//*[@id="pt2:cb1"]/a`

	btnSearchResult := `#pt2\:resId1\:\:db > table a` //Selector

	btnFinReport := `//*[@id="tab5::disAcr"]`
	checkBoxFinYearReport := `//*[@id="r3:0:sor2:_1"]`
	tblFin := `//*[@id="r3:0:tt1::db"]/table`

	checkBoxIncomeStmt := `//*[@id="r3:0:sor1::content"]/label[2]`

	// var result string
	taskSearchStock := chromedp.Tasks{
		chromedp.WaitVisible(btnSearchCode),
		chromedp.Sleep(2 * time.Second),
		chromedp.Reset(btnSearchCode),
		chromedp.SendKeys(btnSearchCode, stock),

		chromedp.WaitVisible(btnSearchSubmit),
		chromedp.Click(btnSearchSubmit),
		chromedp.Sleep(time.Second),

		chromedp.OuterHTML("html", &body),
	}
	taskNavigateReport := chromedp.Tasks{
		chromedp.WaitVisible(btnSearchResult, chromedp.ByQuery),
		chromedp.Sleep(2 * time.Second),
		chromedp.Click(btnSearchResult, chromedp.ByQuery),

		chromedp.WaitVisible(btnFinReport),
		chromedp.Sleep(2 * time.Second),
		chromedp.Click(btnFinReport),

		chromedp.WaitVisible(checkBoxFinYearReport),
		chromedp.Sleep(2 * time.Second),
		chromedp.Click(checkBoxFinYearReport),
		chromedp.Sleep(2 * time.Second),
		chromedp.OuterHTML("html", &body),
	}

	taskDownloadIncSta := chromedp.Tasks{
		chromedp.Click(checkBoxIncomeStmt),
		chromedp.Sleep(2 * time.Second),
		chromedp.WaitVisible(tblFin),
		chromedp.OuterHTML("html", &body),
	}

	if err := chromedp.Run(chromeCtx, taskSearchStock); err != nil {
		return err
	}
	// Have a result?
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(body))
	if err != nil {
		return err
	}
	el := doc.Find(btnSearchResult)
	if len(el.Nodes) == 0 {
		fmt.Printf("%s %s not found \n", stock, btnSearchResult)
		return constants.CommonError.ERROR_NOT_FOUND
	}

	if err := chromedp.Run(chromeCtx, taskNavigateReport); err != nil {
		return err
	}

	if !foundBSheet {
		if err := storeCache(cfg, stock, "BSheet", body, `#r3\:0\:tt1\:\:ch\:\:t`, ""); err != nil {
			return err
		}
		// Scroll to last child
		doc, err = goquery.NewDocumentFromReader(strings.NewReader(body))
		if err != nil {
			return err
		}
		firstID := ""
		doc.Find(`#r3\:0\:tt1\:\:db > table:last-child tr:last-child > td`).Each(func(index int, info *goquery.Selection) {
			firstID = info.AttrOr("id", "")
		})
	SCROLLNEXT:
		if firstID != "" {
			if err := storeCache(cfg, stock, "BSheet", body, `#r3\:0\:tt1\:\:db > table:last-child`, firstID); err != nil {
				return err
			}
			fmt.Println(stock, "Scroll next to ", firstID)
			if err := chromedp.Run(chromeCtx, chromedp.Tasks{
				chromedp.Evaluate(fmt.Sprintf(`var elm = document.getElementById("%s");elm.scrollIntoView(true);`, firstID), nil),
				chromedp.Sleep(2 * time.Second),
				chromedp.OuterHTML("html", &body),
			}); err != nil {
				return err
			}
			doc, err = goquery.NewDocumentFromReader(strings.NewReader(body))
			if err != nil {
				return err
			}
			nextID := ""
			doc.Find(`#r3\:0\:tt1\:\:db > table:nth-child(2) tr:last-child > td`).Each(func(index int, info *goquery.Selection) {
				nextID, _ = info.Attr("id")
			})
			if nextID != firstID {
				firstID = nextID
				goto SCROLLNEXT
			}
		}
	}

	if !foundIncSta {
		if err := storeCache(cfg, stock, "IncSta", body, `#r3\:0\:tt1\:\:ch\:\:t`, ""); err != nil {
			return err
		}
		if err := chromedp.Run(chromeCtx, taskDownloadIncSta); err != nil {
			return err
		}
		if err := storeCache(cfg, stock, "IncSta", body, `#r3\:0\:tt1\:\:db > table:last-child`, ""); err != nil {
			return err
		}
		// end of crawl reload page
		if err := chromedp.Run(chromeCtx, chromedp.Tasks{
			chromedp.Navigate("https://congbothongtin.ssc.gov.vn"),
			chromedp.Click(btnSearchComp),
		}); err != nil {
			return err
		}
	}
	return nil
}

func storeCache(cfg *config.Config, stock string, fType string, body string, selector string, dupSelector string) error {
	year := ""
	tableHtml := ""
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(body))
	if err != nil {
		return err
	}

	doc.Find(`#r3\:0\:tt1\:c6`).Each(func(index int, info *goquery.Selection) {
		year = info.Text()
	})
	doc.Find(selector).Each(func(index int, info *goquery.Selection) {
		tableHtml, _ = info.Parent().Html()
	})
	filePath := fmt.Sprintf("%s/%s_%s-%s.html", cfg.StoragePath.SSCData, fType, stock, year)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		f, err := os.Create(filePath)
		if err != nil {
			return err
		}
		defer f.Close()
		_, err = f.WriteString(tableHtml)
		if err != nil {
			return err
		}
	} else {
		if dupSelector != "" {
			fr, err := os.OpenFile(filePath, os.O_RDONLY, 0644)
			if err != nil {
				return err
			}
			defer fr.Close()
			eDoc, err := goquery.NewDocumentFromReader(fr)
			if err != nil {
				return err
			}
			el := eDoc.Find(dupSelector)
			if len(el.Nodes) > 0 {
				return nil
			}
		}
		f, err := os.OpenFile(filePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			return err
		}

		defer f.Close()
		_, err = f.WriteString(tableHtml)
		if err != nil {
			return err
		}
	}
	return nil
}

func newChromedp() (context.Context, context.CancelFunc) {
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", false),
		chromedp.Flag("start-fullscreen", false),
		chromedp.Flag("disable-gpu", false),
		chromedp.Flag("enable-automation", false),
		chromedp.Flag("disable-extensions", true),
	)
	allocCtx, _ := chromedp.NewExecAllocator(context.Background(), opts...)
	ctx, cancel := chromedp.NewContext(allocCtx, chromedp.WithLogf(log.Printf))

	return ctx, cancel
}

func removeStock(cfg *config.Config, db *sql.DB, stock string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Remove file
	var files []string
	files, err := filepath.Glob(fmt.Sprintf("%s/*_%s-*", cfg.StoragePath.CafefData, stock))
	if err != nil {
		return err
	}
	for _, f := range files {
		if err = os.Remove(f); err != nil {
			return err
		}
	}
	files, err = filepath.Glob(fmt.Sprintf("%s/%s.*", cfg.StoragePath.CafFin, stock))
	if err != nil {
		return err
	}
	for _, f := range files {
		if err = os.Remove(f); err != nil {
			return err
		}
	}
	files, err = filepath.Glob(fmt.Sprintf("%s/%s.*", cfg.StoragePath.StockPrices, stock))
	if err != nil {
		return err
	}
	for _, f := range files {
		if err = os.Remove(f); err != nil {
			return err
		}
	}
	query := fmt.Sprintf("DELETE FROM stocks WHERE id = '%s'", stock)
	// fmt.Println(query)
	_, err = db.ExecContext(ctx, query)
	if err != nil {
		return err
	}
	return nil
}
