package vietstock

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"io"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/PuerkitoBio/goquery"
	"github.com/corpix/uarand"
	"github.com/valyala/fastjson"
)

type vietstockService struct {
	cfg       *config.Config
	logger    *zap.Logger
	transport *http.Transport
}

func NewVietstock(cfg *config.Config, logger *zap.Logger, trans *http.Transport) contract.Vietstock {
	return &vietstockService{cfg, logger, trans}
}

func (vs *vietstockService) FetchPriceStock(ctx context.Context, stockCode string) (*model.StockTrade, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: vs.transport,
	}
	ua := uarand.GetRandom()

	forgeryURL := fmt.Sprintf("https://finance.vietstock.vn/%s/tai-chinh.htm", stockCode)

	req, err := http.NewRequest("GET", forgeryURL, nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", "https://finance.vietstock.vn")
	req.Header.Set("Referer", "https://finance.vietstock.vn/")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")

	resp, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	forgeryCookies := resp.Cookies()

	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}
	token := ""
	doc.Find(`input[name="__RequestVerificationToken"]`).Each(func(i int, s *goquery.Selection) {
		token = s.AttrOr("value", "")
	})

	form := url.Values{}
	form.Add("code", stockCode)
	form.Add("s", "0")
	form.Add("__RequestVerificationToken", token)

	req, err = http.NewRequest("POST", "https://finance.vietstock.vn/company/tradinginfo", strings.NewReader(form.Encode()))
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", "https://finance.vietstock.vn")
	req.Header.Set("Referer", forgeryURL)
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
	req.Header.Add("Content-Length", strconv.Itoa(len(form.Encode())))
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	for _, cookie := range forgeryCookies {
		req.AddCookie(cookie)
	}

	vs.logger.Debug("Request", zap.String("url", req.URL.String()),
		zap.String("form", form.Encode()),
	)

	response, err := client.Do(req)
	var e net.Error
	if errors.As(err, &e) && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	}
	defer response.Body.Close()

	if response.StatusCode == 403 {
		return nil, constants.RequestError.SHOULD_RETRY
	}

	// // Debug
	// content, err := io.ReadAll(resp.Body)
	// if err != nil {
	// 	return nil, err
	// }
	// s := strings.TrimSpace(string(content))
	//vs.logger.Debug("Response", zap.String("url", req.URL.String()),
	//	zap.String("status", strconv.Itoa(response.StatusCode)),
	//	zap.String("response", s),
	//)

	stockTrade := new(model.StockTrade)
	err = json.NewDecoder(response.Body).Decode(stockTrade)
	if err != nil {
		return nil, err
	}
	return stockTrade, nil
}

func (vs *vietstockService) FetchAllStocks(ctx context.Context) ([]*model.TradingStock, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: vs.transport,
	}

	req, err := http.NewRequest(http.MethodGet, "https://api.vietstock.vn/finance/exchangeinfo", nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", uarand.GetRandom())
	req.Header.Set("Origin", "https://finance.vietstock.vn")
	req.Header.Set("Referer", "https://finance.vietstock.vn/")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")

	q := req.URL.Query()
	q.Add("catID", "0")
	q.Add("languageID", "1")
	req.URL.RawQuery = q.Encode()

	vs.logger.Debug("Request",
		zap.String("url", req.URL.String()),
		zap.String("query", req.URL.RawQuery),
	)

	response, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	if response.StatusCode == 403 {
		return nil, constants.RequestError.SHOULD_RETRY
	}

	// content, err := io.ReadAll(response.Body)
	// if err != nil {
	// 	return nil, err
	// }

	// s := strings.TrimSpace(string(content))
	//vs.logger.Debug("Response", zap.String("url", req.URL.String()),
	//	zap.String("status", strconv.Itoa(response.StatusCode)),
	//	zap.String("response", s),
	//)

	var stockTrades []*model.TradingStock
	err = json.NewDecoder(response.Body).Decode(&stockTrades)
	if err != nil {
		return nil, err
	}
	return stockTrades, nil
}

func (vs *vietstockService) FetchDividendStock(ctx context.Context, stockCode string, page int) ([]*model.StockEvents, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: vs.transport,
	}
	ua := uarand.GetRandom()

	forgeryURL := fmt.Sprintf("https://finance.vietstock.vn/lich-su-kien.htm?page=1&tab=1&code=%s&group=13", stockCode)

	req, err := http.NewRequest("GET", forgeryURL, nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", "https://finance.vietstock.vn")
	req.Header.Set("Referer", "https://finance.vietstock.vn/")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")

	resp, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	forgeryCookies := resp.Cookies()

	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}
	token := ""
	doc.Find(`input[name="__RequestVerificationToken"]`).Each(func(i int, s *goquery.Selection) {
		token = s.AttrOr("value", "")
	})

	form := url.Values{}
	form.Add("eventTypeID", "1")
	form.Add("channelID", "13")
	form.Add("code", stockCode)
	form.Add("catID", "")
	form.Add("fDate", "")
	form.Add("tDate", "")
	form.Add("page", strconv.Itoa(page))
	form.Add("pageSize", "20")
	form.Add("orderBy", "Date1")
	form.Add("orderDir", "DESC")
	form.Add("__RequestVerificationToken", token)

	req, err = http.NewRequest("POST", "https://finance.vietstock.vn/data/eventstypedata", strings.NewReader(form.Encode()))
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", "https://finance.vietstock.vn")
	req.Header.Set("Referer", forgeryURL)
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
	req.Header.Add("Content-Length", strconv.Itoa(len(form.Encode())))
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	for _, cookie := range forgeryCookies {
		req.AddCookie(cookie)
	}

	vs.logger.Debug("Request",
		zap.String("url", req.URL.String()),
		zap.String("query", req.URL.RawQuery),
		zap.String("form", form.Encode()),
	)

	response, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	if response.StatusCode == 403 {
		return nil, constants.RequestError.SHOULD_RETRY
	}

	// Debug
	content, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}

	if len(content) == 0 {
		return nil, constants.CommonError.ERROR_NOT_FOUND
	}

	// s := strings.TrimSpace(string(content))
	// fmt.Println("=============Response===============")
	// fmt.Println(s)

	var p fastjson.Parser
	responeJson, err := p.ParseBytes(content)
	if err != nil {
		return nil, err
	}
	respArr, err := responeJson.Array()
	if err != nil {
		return nil, err
	}

	var stockEvents []*model.StockEvents
	err = json.Unmarshal([]byte(respArr[0].String()), &stockEvents)
	if err != nil {
		return nil, err
	}
	return stockEvents, nil
}
