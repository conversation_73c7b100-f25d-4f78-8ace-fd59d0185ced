package hmoney

import (
	"context"
	"log"
	"net"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/PuerkitoBio/goquery"
	"github.com/chromedp/chromedp"
	"github.com/corpix/uarand"
)

type hmoneyService struct {
	cfg       *config.Config
	transport *http.Transport
	cookies   []*http.Cookie
}

func NewHmoneyService(cfg *config.Config, trans *http.Transport) contract.Hmoney {
	return &hmoneyService{cfg, trans, nil}
}

func (cs *hmoneyService) newChromedp(ctx context.Context) (context.Context, context.CancelFunc) {
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),
		chromedp.Flag("start-fullscreen", false),
		chromedp.Flag("disable-gpu", false),
		chromedp.Flag("enable-automation", false),
		chromedp.Flag("disable-extensions", true),
	)
	allocCtx, _ := chromedp.NewExecAllocator(ctx, opts...)
	ctx, cancel := chromedp.NewContext(allocCtx, chromedp.WithLogf(log.Printf))

	return ctx, cancel
}

func (cs *hmoneyService) FetchBankInterestSixMonthChrome(ctx context.Context) (float64, error) {
	chromeCtx, cancel := cs.newChromedp(ctx)
	defer cancel()

	var body string
	bankInterestURL := "https://24hmoney.vn/lai-suat-gui-ngan-hang"
	headerElement := `div.bank-rate-offline > div.vue-table.offline-table`
	if err := chromedp.Run(chromeCtx, chromedp.Tasks{
		chromedp.Navigate(bankInterestURL), // By pass captcha
		chromedp.WaitVisible(headerElement),
		chromedp.Sleep(2 * time.Second),
		chromedp.OuterHTML("html", &body),
	}); err != nil {
		return 0, err
	}

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(body))
	if err != nil {
		return 0, err
	}
	if err != nil {
		return 0, err
	}

	max := 0.0
	doc.Find("div.bank-rate-offline table tr").Each(func(itr int, tr *goquery.Selection) {
		tr.Find("td p.bank-interest-rate").Each(func(itd int, td *goquery.Selection) {
			strNum, _ := td.Html()
			if itd == 3 {
				interestSixMonth, err := strconv.ParseFloat(strNum, 64)
				if err != nil {
					return
				}
				if max < interestSixMonth {
					max = interestSixMonth
				}
			}
		})
	})
	return max / 100, nil
}

func (cs *hmoneyService) FetchBankInterestSixMonth(ctx context.Context) (float64, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: cs.transport,
	}
	ua := uarand.GetRandom()
	// Crawl info to build url
	originUrl := "https://24hmoney.vn"
	req, err := http.NewRequest("GET", "https://24hmoney.vn/lai-suat-gui-ngan-hang", nil)
	if err != nil {
		return 0, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("origin", originUrl)

	response, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return 0, constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return 0, err
	}
	defer response.Body.Close()

	doc, err := goquery.NewDocumentFromReader(response.Body)
	if err != nil {
		return 0, err
	}

	max := 0.0
	doc.Find("div.bank-rate-offline table tr").Each(func(itr int, tr *goquery.Selection) {
		tr.Find("td p.bank-interest-rate").Each(func(itd int, td *goquery.Selection) {
			strNum, _ := td.Html()
			if itd == 3 {
				interestSixMonth, err := strconv.ParseFloat(strNum, 64)
				if err != nil {
					return
				}
				if max < interestSixMonth {
					max = interestSixMonth
				}
			}
		})
	})
	return max / 100, nil
}
