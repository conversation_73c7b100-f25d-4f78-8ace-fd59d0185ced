package simplize

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"sort"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/corpix/uarand"
)

type simpService struct {
	cfg       *config.Config
	transport *http.Transport
	cookies   []*http.Cookie
}

func NewSimpService(cfg *config.Config, trans *http.Transport) contract.Simplize {
	return &simpService{cfg, trans, nil}
}

func (ss *simpService) FetchStockPrices(ctx context.Context, stockCode string, condition string) error {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: ss.transport,
	}
	ua := uarand.GetRandom()

	// Crawl info to build url
	originUrl := "https://simplize.vn"
	infoUrl := fmt.Sprintf("https://api.simplize.vn/api/historical/prices/chart?ticker=%s&period=%s", stockCode, condition)
	req, err := http.NewRequest("GET", infoUrl, nil)
	if err != nil {
		return err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("origin", originUrl)
	req.Header.Set("referer", originUrl)
	req.Header.Set("Content-Type", "application/json, text/plain, */*")

	response, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return err
	}
	defer response.Body.Close()

	stockResp := new(model.SimpStockPrices)
	err = json.NewDecoder(response.Body).Decode(stockResp)
	if err != nil {
		return err
	}

	stockFilePath := fmt.Sprintf("%s/%s.csv", ss.cfg.StoragePath.StockPrices, stockCode)
	records := make(map[string][]string)
	if _, err = os.Stat(stockFilePath); err == nil {
		csvIn, err := os.Open(stockFilePath)
		if err != nil {
			return err
		}
		reader := csv.NewReader(csvIn)
		for {
			rec, err := reader.Read()
			if err == io.EOF {
				break
			}
			if err != nil {
				return err
			}
			if rec[0] == "date" {
				continue
			}
			records[rec[0]] = rec
		}
		csvIn.Close()
	}
	csvOut, err := os.OpenFile(stockFilePath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0755)
	if err != nil {
		return err
	}

	writer := csv.NewWriter(csvOut)
	defer csvOut.Close()
	header := []string{
		"date", "open", "high", "low", "close", "volume",
	}
	err = writer.Write(header)
	if err != nil {
		return err
	}
	l := len(stockResp.Data)

	for i := l - 1; i > 0; i-- {
		var record []string
		data := stockResp.Data[i]
		date := ""
		for j, v := range data {
			record = append(record, fmt.Sprintf("%.0f", v))
			if j == 0 {
				date = fmt.Sprintf("%.0f", data[j])
			}
		}
		// Compare to existing record; not exist insert
		// if _, ok := records[date]; !ok {
		records[date] = record
		// }
	}
	keys := make([]string, 0)
	for k := range records {
		keys = append(keys, k)
	}
	sort.Slice(keys, func(i, j int) bool {
		return keys[j] < keys[i]
	})
	for _, k := range keys {
		err = writer.Write(records[k])
		if err != nil {
			return err
		}
	}
	writer.Flush()
	return nil
}

func (ss *simpService) FetchFinancialQuarterly(ctx context.Context, stockCode string) (*model.SimpLatestQuarterRevRatio, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: ss.transport,
	}
	ua := uarand.GetRandom()

	// Crawl info to build url
	originUrl := "https://simplize.vn"
	infoUrl := fmt.Sprintf("https://api.simplize.vn/api/company/fi/agg-compare/overview/%s?period=Q", stockCode)
	req, err := http.NewRequest("GET", infoUrl, nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("origin", originUrl)
	req.Header.Set("referer", originUrl)
	req.Header.Set("Content-Type", "application/json, text/plain, */*")

	response, err := client.Do(req)
	var e net.Error
	if errors.As(err, &e) && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	}
	defer response.Body.Close()

	stockFinResp := new(model.SimpQuarterCompare)
	err = json.NewDecoder(response.Body).Decode(stockFinResp)
	if err != nil {
		return nil, err
	}

	latestQuarterTime := time.Time{}
	var quarterRatio *model.SimpLatestQuarterRevRatio
	for _, item := range stockFinResp.Data.Items {
		quarterTime, err := time.Parse("2006-01", item.PeriodDate)
		if err != nil {
			return nil, err
		}
		if latestQuarterTime.IsZero() {
			latestQuarterTime = quarterTime
			quarterRatio = &model.SimpLatestQuarterRevRatio{
				QuarterName: item.PeriodDateName,
				Revenue:     item.Revenue / 100,
			}
			continue
		}
		if quarterTime.After(latestQuarterTime) {
			return &model.SimpLatestQuarterRevRatio{
				QuarterName: item.PeriodDateName,
				Revenue:     item.Revenue / 100,
			}, nil
		}
	}

	return quarterRatio, nil
}
