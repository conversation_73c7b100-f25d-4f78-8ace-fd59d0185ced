package simplize

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/model"
)

func DownloadNewestQuarterly(db *sql.DB, simp contract.Simplize, quarter string) error {
	cursor := ""
	for {
		stockCode, err := getOutdatedQuarterStockCursor(db, quarter, cursor)
		if errors.Is(err, sql.ErrNoRows) {
			break
		}
		cursor = stockCode
		resp, err := fetchStockRatio(simp, stockCode)
		if err != nil {
			return err
		}
		if resp != nil {
			fmt.Println("Fetched quarterly", stockCode, resp.QuarterName, resp.Revenue)
			err = insertStockQuarter(db, stockCode, resp)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func getOutdatedQuarterStockCursor(db *sql.DB, quarter string, prevCursor string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	stmt, err := db.PrepareContext(ctx, `SELECT id FROM stocks WHERE rev_last_quarter not like $1 AND id > $2 ORDER BY id LIMIT 1`)
	if err != nil {
		return "", err
	}
	var stockCode string
	if err = stmt.QueryRowContext(ctx, quarter, prevCursor).Scan(&stockCode); err != nil {
		return "", err
	}

	return stockCode, nil
}

func getOutdatedStockCursor(db *sql.DB, prevCursor string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	stmt, err := db.PrepareContext(ctx, `SELECT id FROM stocks WHERE updated_at + interval '1 hour' <= NOW() AND id > $1 ORDER BY id LIMIT 1`)
	if err != nil {
		return "", err
	}
	var stockCode string
	if err = stmt.QueryRowContext(ctx, prevCursor).Scan(&stockCode); err != nil {
		return "", err
	}

	return stockCode, nil
}

func fetchStockRatio(simp contract.Simplize, stockCode string) (*model.SimpLatestQuarterRevRatio, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	return simp.FetchFinancialQuarterly(ctx, stockCode)
}

func insertStockQuarter(db *sql.DB, stock string, ratio *model.SimpLatestQuarterRevRatio) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Insert db
	stmt, err := db.PrepareContext(ctx, "UPDATE stocks SET rev_ratio_qoq = $1, rev_last_quarter = $2, updated_at=NOW() WHERE id = $3")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, ratio.Revenue, ratio.QuarterName, stock)
	if err != nil {
		return err
	}
	return nil
}
