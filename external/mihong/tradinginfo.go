package mihong

import (
	"context"
	"encoding/json"
	"net"
	"net/http"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/corpix/uarand"
)

type mihongService struct {
	cfg       *config.Config
	transport *http.Transport
}

func NewMihong(cfg *config.Config, trans *http.Transport) contract.Mihong {
	return &mihongService{cfg, trans}
}

func (mh *mihongService) FetchGoldPrice(ctx context.Context) (*model.GoldResponse, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: mh.transport,
	}
	ua := uarand.GetRandom()

	req, err := http.NewRequest(http.MethodGet, "https://www.mihong.vn/api/v1/gold/prices/current", nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("authority", "www.mihong.vn")
	req.Header.Set("Referer", "https://www.mihong.vn/vi/gia-vang-trong-nuoc")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")

	response, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	if response.StatusCode == 403 {
		return nil, constants.RequestError.SHOULD_RETRY
	}

	// s := strings.TrimSpace(string(content))
	// fmt.Println("=============Response===============")
	// fmt.Println(s)

	goldTrade := new(model.GoldResponse)
	err = json.NewDecoder(response.Body).Decode(goldTrade)
	if err != nil {
		return nil, err
	}
	return goldTrade, nil
}
