package ssi

import (
	"context"
	"database/sql"
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
)

func (ssi *ssiService) StoreTradeStock(db *sql.DB, ssiOrg *model.SSIOrganization) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
			return
		}
		tx.Commit()
	}()

	for _, item := range ssiOrg.Items {
		var stmt *sql.Stmt
		stmt, err := tx.Prepare("SELECT id, price FROM stocks WHERE id = $1")
		if err != nil {
			return err
		}
		r := stmt.QueryRowContext(ctx, item.Ticker)
		var (
			id    string
			price float64
		)
		if err = r.<PERSON>an(&id, &price); err != nil {
			if err != sql.ErrNoRows {
				return err
			}
		} else {
			stmt, err = tx.Prepare("UPDATE stocks SET ssi_code = $1, company_name = $2, group_code = $3 WHERE id = $4")
			if err != nil {
				return err
			}
			_, err = stmt.ExecContext(ctx, item.OrganCode, item.OrganName, item.ComGroupCode, item.Ticker)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (ssi *ssiService) StoreFinancialReport(cfg *config.Config, dbFin *sql.DB, stockCode string, bs *model.SSIBalanceSheet, is *model.SSIIncomestatement) (string, error) {
	// Validate data
	if len(bs.Items) == 0 || len(is.Items) == 0 {
		return "", nil
	}
	if len(bs.Items[0].Yearly) == 0 || len(is.Items[0].Yearly) == 0 {
		return "", nil
	}
	// Regex
	numberOnlyReg, err := regexp.Compile(`-*\d+`)
	if err != nil {
		return "", err
	}
	ssiDict, err := ssiLoadDict(cfg)
	if err != nil {
		return "", err
	}
	tx, err := dbFin.Begin()
	if err != nil {
		return "", err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
			return
		}
		tx.Commit()
		ssiPatchNullFieldToZero(cfg, stockCode)
	}()

	ctx := context.WithValue(context.Background(), constants.ContextTransactionKey, tx)
	var years []string
	for _, bsItem := range bs.Items {
		for _, bsYear := range bsItem.Yearly {
			years = append(years, fmt.Sprintf("`%d`", bsYear.YearReport))
		}
	}
	sort.Strings(years)
	yearHeaders := strings.Join(years, ",")
	if !ssiCheckTableExist(ctx, stockCode) {
		err := ssiCreateTableReport(ctx, stockCode, yearHeaders)
		if err != nil {
			return "", err
		}
	}
	err = ssiCheckFieldsTableExist(ctx, stockCode, years, numberOnlyReg)
	if err != nil {
		return "", err
	}
	for _, bsItem := range bs.Items {
		for _, bsYear := range bsItem.Yearly {
			insertYear := fmt.Sprintf("`%d`", bsYear.YearReport)
			isInsert := false
			for _, year := range years {
				if year == insertYear {
					isInsert = true
					break
				}
			}
			if isInsert {
				v := reflect.ValueOf(bsYear)
				typeOfS := v.Type()
				for i := 0; i < v.NumField(); i++ {
					ID := typeOfS.Field(i).Tag.Get("json")
					if title, ok := ssiDict[ID]; ok {
						err = insertData(ctx, stockCode, fmt.Sprintf("`%d`", bsYear.YearReport), ID, title, v.Field(i).Interface())
						if err != nil {
							return "", err
						}
					}
				}
			}
		}
	}
	for _, isItem := range is.Items {
		for _, isYear := range isItem.Yearly {
			insertYear := fmt.Sprintf("`%d`", isYear.YearReport)
			isInsert := false
			for _, year := range years {
				if year == insertYear {
					isInsert = true
					break
				}
			}
			if isInsert {
				v := reflect.ValueOf(isYear)
				typeOfS := v.Type()
				for i := 0; i < v.NumField(); i++ {
					ID := typeOfS.Field(i).Tag.Get("json")
					if title, ok := ssiDict[ID]; ok {
						err = insertData(ctx, stockCode, fmt.Sprintf("`%d`", isYear.YearReport), ID, title, v.Field(i).Interface())
						if err != nil {
							return "", err
						}
					}
				}
			}

		}
	}

	return yearHeaders, nil
}

func insertData(ctx context.Context, table string, year string, ID string, title string, val interface{}) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	stmt, err := tx.Prepare("SELECT id FROM `" + table + ".csv` WHERE id = ?")
	if err != nil {
		return err
	}
	r := stmt.QueryRowContext(ctx, ID)
	var id string
	if err = r.Scan(&id); err != nil {
		if err == sql.ErrNoRows {
			fmt.Printf("%s No Rows. Inserting\n", ID)
			query := fmt.Sprintf("INSERT INTO `%s.csv`(id,title,%s) VALUES(?,?,?)", table, year)
			stmt, err = tx.Prepare(query)
			if err != nil {
				return err
			}
			_, err = stmt.ExecContext(ctx, ID, title, val)
			if err != nil {
				return err
			}
			return nil
		}
		return err
	} else {
		stmt, err = tx.Prepare(fmt.Sprintf("UPDATE `%s.csv` SET %s=? WHERE id=?", table, year))
		if err != nil {
			return err
		}
		_, err = stmt.ExecContext(ctx, val, ID)
		if err != nil {
			return err
		}
	}
	return nil
}

func ssiLoadDict(cfg *config.Config) (map[string]string, error) {
	dictFilePath := fmt.Sprintf("%s/ssi.csv", cfg.TemplatePath.SSIDictPath)
	if _, err := os.Stat(dictFilePath); os.IsNotExist(err) {
		return nil, err
	}
	csvIn, err := os.Open(dictFilePath)
	if err != nil {
		return nil, err
	}
	defer csvIn.Close()
	reader := csv.NewReader(csvIn)
	dict := map[string]string{}
	i := 0
	for {
		i++
		rec, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}
		if i <= 1 {
			continue
		}
		if len(rec) >= 2 {
			dict[rec[0]] = rec[1]
		}
	}
	return dict, nil
}

func ssiPatchNullFieldToZero(cfg *config.Config, stock string) error {
	stockFilePath := fmt.Sprintf("%s/%s.csv", cfg.StoragePath.SSIFin, stock)
	if _, err := os.Stat(stockFilePath); os.IsNotExist(err) {
		return nil
	}
	stockFilePathW := fmt.Sprintf("%s/%s-temp.csv", cfg.StoragePath.SSIFin, stock)
	// Path file
	csvIn, err := os.Open(stockFilePath)
	if err != nil {
		return err
	}
	defer csvIn.Close()
	reader := csv.NewReader(csvIn)

	csvOut, err := os.OpenFile(stockFilePathW, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0755)
	if err != nil {
		return err
	}
	writer := csv.NewWriter(csvOut)
	defer csvOut.Close()

	for {
		rec, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		var records []string
		for _, r := range rec {
			if r == "" {
				records = append(records, "0")
			} else {
				records = append(records, r)
			}
		}
		writer.Write(records)
	}
	writer.Flush()
	// Swap file
	err = os.Remove(stockFilePath)
	if err != nil {
		return err
	}
	err = os.Rename(stockFilePathW, stockFilePath)
	if err != nil {
		return err
	}
	return nil
}

func ssiCheckTableExist(ctx context.Context, table string) bool {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	_, err := tx.Query(fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", table))
	if err != nil {
		return err == sql.ErrNoRows
	}
	return true
}

func ssiCreateTableReport(ctx context.Context, table string, years string) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	_, err := tx.Query(fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", table))
	if err != nil {
		_, err = tx.ExecContext(ctx, fmt.Sprintf("CREATE TABLE `%s.csv`(id, title, %s)", table, years))
		if err != nil {
			return err
		}
	}
	return nil
}

func ssiCheckFieldsTableExist(ctx context.Context, table string, fields []string, r *regexp.Regexp) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)

	maxYear := 0
	rs, err := tx.QueryContext(ctx, fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", table))
	if err != nil && err != sql.ErrNoRows {
		return nil
	}
	defer rs.Close()
	cols, err := rs.Columns()
	if err != nil {
		return nil
	}
	var newFields []string
	prepend := false
	for _, year := range fields {
		isAppend := true
		for _, col := range cols {
			if r.MatchString(col) {
				y, _ := strconv.Atoi(col)
				if maxYear < y {
					maxYear = y
				}
			}
			if year == fmt.Sprintf("`%s`", col) {
				isAppend = false
				break
			}
		}
		if isAppend {
			strY := strings.ReplaceAll(year, "`", "")
			aY, _ := strconv.Atoi(strY)
			if aY > maxYear {
				prepend = true
			}
			newFields = append(newFields, year)
		}
	}

	if len(newFields) > 0 {
		alterQuery := fmt.Sprintf("ALTER TABLE `"+table+".csv` ADD (%s) AFTER title", strings.Join(newFields, ","))
		if !prepend {
			alterQuery = fmt.Sprintf("ALTER TABLE `"+table+".csv` ADD (%s)", strings.Join(newFields, ","))
		}
		fmt.Println(alterQuery)
		rs, err = tx.QueryContext(ctx, alterQuery)
		if err != nil {
			return err
		}
		defer rs.Close()
	}

	return nil
}

func ssiCheckYearIsZero(dbFinSSI *sql.DB, table string, yearField string) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	total := 0.0
	rs, err := dbFinSSI.QueryContext(ctx, fmt.Sprintf("SELECT %s FROM `%s.csv`", yearField, table))
	if err != nil {
		return false, err
	}
	for rs.Next() {
		var rowValue float64
		err = rs.Scan(&rowValue)
		if err != nil {
			return false, err
		}
		total += rowValue
	}
	if total == 0 {
		return true, nil
	}
	return false, nil
}

func ssiRemoveColumn(dbFinSSI *sql.DB, table string, year string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	query := "ALTER TABLE `" + table + ".csv` DROP " + year
	fmt.Println(query)
	_, err := dbFinSSI.ExecContext(ctx, query)
	if err != nil {
		return err
	}
	return nil
}
