package ssi

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/http"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/corpix/uarand"
)

type ssiService struct {
	cfg       *config.Config
	transport *http.Transport
}

func NewSSI(cfg *config.Config, trans *http.Transport) contract.SSI {
	return &ssiService{cfg, trans}
}

func (ssi *ssiService) FetchStockCodes() (*model.SSIOrganization, error) {
	timeout := 60 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	client := &http.Client{
		Timeout:   timeout,
		Transport: ssi.transport,
	}
	ua := uarand.GetRandom()

	requestURL := "https://fiin-core.ssi.com.vn/Master/GetListOrganization?language=vi"

	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", "https://iboard.ssi.com.vn")
	req.Header.Set("Referer", "https://iboard.ssi.com.vn/")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("x-fiin-key", "KEY")
	req.Header.Set("x-fiin-seed", "SEED")
	req.Header.Set("x-fiin-user-id", "ID")
	req.Header.Set("x-fiin-user-token", ssi.cfg.SSI.XfiinUserToken)
	req.Header.Set("Connection", "keep-alive")

	fmt.Println("=============Request===============")
	fmt.Printf("%s\n", req.URL.String())

	resp, err := client.Do(req)
	var e net.Error
	if errors.As(err, &e) && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	}
	defer resp.Body.Close()

	// Debug
	// content, err := io.ReadAll(resp.Body)
	// if err != nil {
	// 	return nil, err
	// }

	// s := strings.TrimSpace(string(content))
	// fmt.Println("=============Response===============")
	// fmt.Println(s)

	var ssiOrg *model.SSIOrganization
	err = json.NewDecoder(resp.Body).Decode(&ssiOrg)
	if err != nil {
		return nil, err
	}

	return ssiOrg, nil
}
func (ssi *ssiService) FetchBalanceSheet(ssiStockCode string) (*model.SSIBalanceSheet, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: ssi.transport,
	}
	ua := uarand.GetRandom()

	requestURL := fmt.Sprintf("https://fiin-fundamental.ssi.com.vn/FinancialStatement/GetBalanceSheet?language=vi&OrganCode=%s", ssiStockCode)

	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", "https://iboard.ssi.com.vn")
	req.Header.Set("Referer", "https://iboard.ssi.com.vn/")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("x-fiin-key", "KEY")
	req.Header.Set("x-fiin-seed", "SEED")
	req.Header.Set("x-fiin-user-id", "ID")
	req.Header.Set("x-fiin-user-token", ssi.cfg.SSI.XfiinUserToken)
	req.Header.Set("Connection", "keep-alive")

	fmt.Println("=============Request===============")
	fmt.Printf("%s\n", req.URL.String())

	resp, err := client.Do(req)
	var e net.Error
	if errors.As(err, &e) && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	}
	defer resp.Body.Close()

	// Debug
	// content, err := io.ReadAll(resp.Body)
	// if err != nil {
	// 	return nil, err
	// }

	// s := strings.TrimSpace(string(content))
	// fmt.Println("=============Response===============")
	// fmt.Println(s)

	var bs *model.SSIBalanceSheet
	err = json.NewDecoder(resp.Body).Decode(&bs)
	if err != nil {
		fmt.Println(err)
		return nil, constants.RequestError.SHOULD_RETRY
	}

	return bs, nil
}

func (ssi *ssiService) FetchIncomstatement(ssiStockCode string) (*model.SSIIncomestatement, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: ssi.transport,
	}
	ua := uarand.GetRandom()

	requestURL := fmt.Sprintf("https://fiin-fundamental.ssi.com.vn/FinancialStatement/GetIncomeStatement?language=vi&OrganCode=%s", ssiStockCode)

	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", "https://iboard.ssi.com.vn")
	req.Header.Set("Referer", "https://iboard.ssi.com.vn/")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("x-fiin-key", "KEY")
	req.Header.Set("x-fiin-seed", "SEED")
	req.Header.Set("x-fiin-user-id", "ID")
	req.Header.Set("x-fiin-user-token", ssi.cfg.SSI.XfiinUserToken)
	req.Header.Set("Connection", "keep-alive")

	fmt.Println("=============Request===============")
	fmt.Printf("%s\n", req.URL.String())

	resp, err := client.Do(req)
	var e net.Error
	if errors.As(err, &e) && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	}
	defer resp.Body.Close()

	// Debug
	// content, err := io.ReadAll(resp.Body)
	// if err != nil {
	// 	return err
	// }

	// s := strings.TrimSpace(string(content))
	// fmt.Println("=============Response===============")
	// fmt.Println(s)

	var is *model.SSIIncomestatement
	err = json.NewDecoder(resp.Body).Decode(&is)
	if err != nil {
		fmt.Println(err)
		return nil, constants.RequestError.SHOULD_RETRY
	}

	return is, nil
}
