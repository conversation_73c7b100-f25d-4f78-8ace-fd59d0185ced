package ssi

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"math/rand"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/config"
)

func getAllYears(dbFin *sql.DB, table string, numberOnlyReg *regexp.Regexp) ([]string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	rs, err := dbFin.QueryContext(ctx, fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", table))
	if err != nil {
		return nil, err
	}
	defer rs.Close()
	cols, err := rs.Columns()
	if err != nil {
		return nil, err
	}
	var years []string
	for _, col := range cols {
		if numberOnlyReg.MatchString(col) {
			years = append(years, fmt.Sprintf("`%s`", col))
		}
	}
	sort.Strings(years)
	return years, nil
}

func updateYears(db *sql.DB, stock string, years []string) error {
	// Start transaction
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
			return
		}
		tx.Commit()
	}()

	stmt, err := tx.Prepare("UPDATE stocks SET years=$1, updated_at=NOW() WHERE id=$2")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, strings.Join(years, ","), stock)
	if err != nil {
		return err
	}
	return nil
}

func (ssi *ssiService) RemoveZeroColumn(db *sql.DB, dbFinSSI *sql.DB, numberOnlyReg *regexp.Regexp) error {
	cursor := ""
	for {
		stock, yearStr, err := getSSIStocks(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return err
		}
		cursor = stock
		var newYears []string
		yearFields := strings.Split(yearStr, ",")
		if yearStr == "" {
			// Insert empty years
			yearCols, err := getAllYears(dbFinSSI, stock, numberOnlyReg)
			if err != nil {
				return err
			}
			err = updateYears(db, stock, yearCols)
			if err != nil {
				return err
			}
			yearFields = yearCols
		}
		for _, yearField := range yearFields {
			isRemove, err := ssiCheckYearIsZero(dbFinSSI, stock, yearField)
			if err != nil {
				return err
			}
			if isRemove {
				err = ssiRemoveColumn(dbFinSSI, stock, yearField)
				if err != nil {
					return err
				}
			} else {
				newYears = append(newYears, yearField)
			}
		}
		err = updateStockCrawledYears(db, stock, strings.Join(newYears, ","))
		if err != nil {
			return err
		}
	}
	return nil
}

func (ssi *ssiService) DownloadAllStockSinceYear(db *sql.DB, dbFinSSI *sql.DB, cfg *config.Config, year int) error {
	sleepLimit := 5
	cursor := ""
	for {
		stock, ssiCode, latestYear, err := getStockInfoFromYear(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return err
		}
		cursor = stock
		if latestYear >= year {
			continue
		}
		fmt.Println("Downloading stock: ", stock, "with financial year:", latestYear)
		// I put random sleep here to not stress the ssi server
		bs, err := ssi.FetchBalanceSheet(ssiCode)
		if err != nil {
			if errors.Is(err, constants.RequestError.SHOULD_RETRY) {
				sleepRandom(sleepLimit)
				continue
			}
			fmt.Println("Download balance sheet of", stock, "with error: ", err)
			continue
		}

		is, err := ssi.FetchIncomstatement(ssiCode)
		if err != nil {
			if errors.Is(err, constants.RequestError.SHOULD_RETRY) {
				sleepRandom(sleepLimit)
				continue
			}
			fmt.Println("Download income statement of", stock, "with error: ", err)
			continue
		}
		years, err := ssi.StoreFinancialReport(cfg, dbFinSSI, stock, bs, is)
		if err != nil {
			fmt.Println("Store financial report of", stock, "with error: ", err)
			continue
		}

		err = updateStockCrawledYears(db, stock, years)
		if err != nil {
			return err
		}
		sleepRandom(sleepLimit)
	}
	return nil
}

func (ssi *ssiService) DownloadAllStock(db *sql.DB, dbFinSSI *sql.DB, cfg *config.Config) error {
	sleepLimit := 5
	cursor := ""
	for {
		stock, ssiCode, err := getNullYearStocks(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return err
		}
		cursor = stock

		bs, err := ssi.FetchBalanceSheet(ssiCode)
		if err != nil {
			if errors.Is(err, constants.RequestError.SHOULD_RETRY) {
				sleepRandom(sleepLimit)
				continue
			}
			return err
		}

		is, err := ssi.FetchIncomstatement(ssiCode)
		if err != nil {
			if errors.Is(err, constants.RequestError.SHOULD_RETRY) {
				sleepRandom(sleepLimit)
				continue
			}
			return err
		}
		years, err := ssi.StoreFinancialReport(cfg, dbFinSSI, stock, bs, is)
		if err != nil {
			return err
		}

		err = updateStockCrawledYears(db, stock, years)
		if err != nil {
			return err
		}
		sleepRandom(sleepLimit)
	}
	return nil
}

func sleepRandom(limit int) {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	n := r.Intn(limit) // n will be between 0 and limit
	fmt.Printf("Sleeping %d seconds...\n", n)
	time.Sleep(time.Duration(n) * time.Second)
}

func (ssi *ssiService) SwitchRustyFinancialStock(db *sql.DB) error {
	cursor := ""
	for {
		stock, latestYear, latestYearOfQuarter, err := getStockWithLatestFinYear(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return err
		}
		cursor = stock
		if latestYear < latestYearOfQuarter {
			fmt.Println("Stock: ", stock, "Has financial year:", latestYear, "differed from quarter:", latestYearOfQuarter)
			err = updateStock(db, stock)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func updateStock(db *sql.DB, stock string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
			return
		}
		tx.Commit()
	}()

	stmt, err := tx.Prepare("UPDATE stocks SET years = NULL, data_source = $1 WHERE id = $2")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, constants.Datasources.SSI, stock)
	if err != nil {
		return err
	}
	return nil
}

func updateStockCrawledYears(db *sql.DB, stock string, years string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
			return
		}
		tx.Commit()
	}()

	stmt, err := tx.Prepare("UPDATE stocks SET years = $1, updated_at=NOW() WHERE id = $2")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, years, stock)
	if err != nil {
		return err
	}
	return nil
}

func getStockInfoFromYear(db *sql.DB, cursor string) (string, string, int, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	stmt, err := db.PrepareContext(ctx, "SELECT id, years, ssi_code FROM stocks WHERE data_source=$1 AND id > $2 AND years <> '' ORDER BY id LIMIT 1")
	if err != nil {
		return "", "", 0, err
	}
	r := stmt.QueryRowContext(ctx, constants.Datasources.SSI, cursor)

	var (
		stockCode string
		ssiCode   string
		yearsStr  sql.NullString
	)
	if err := r.Scan(&stockCode, &yearsStr, &ssiCode); err != nil {
		return "", "", 0, err
	}
	if !yearsStr.Valid {
		return stockCode, ssiCode, 0, nil
	}
	latestYear, err := findLatestYear(yearsStr.String)
	if err != nil {
		return "", "", 0, err
	}

	return stockCode, ssiCode, latestYear, nil
}

// extractYear extracts the year (YYYY) from a string format like "Qx/YYYY".
// It returns the extracted year as an integer and an error if the format
// is invalid or the year part cannot be parsed.
func extractYear(dateString string) (int, error) {
	// 1. Check if the string contains the expected delimiter "/"
	if !strings.Contains(dateString, "/") {
		return 0, fmt.Errorf("invalid format: missing '/' delimiter in '%s'", dateString)
	}

	// 2. Split the string into parts based on the "/" delimiter
	parts := strings.Split(dateString, "/")

	// 3. Validate the number of parts (should be exactly 2 for "Qx/YYYY")
	if len(parts) != 2 {
		return 0, fmt.Errorf("invalid format: expected 'Qx/YYYY', got '%s'", dateString)
	}

	// 4. Get the second part, which should be the year
	yearPart := parts[1]

	// 5. Validate the year part is not empty
	if yearPart == "" {
		return 0, fmt.Errorf("invalid format: year part is empty in '%s'", dateString)
	}

	// 6. Convert the year part string to an integer
	year, err := strconv.Atoi(yearPart)
	if err != nil {
		// If conversion fails (e.g., "Q4/ABCD"), return an error
		// We wrap the original error for more context using %w
		return 0, fmt.Errorf("failed to parse year from '%s': %w", yearPart, err)
	}

	// 7. Optional: Add validation for a reasonable year range if needed
	// if year < 1900 || year > 2100 { // Example range
	//     return 0, fmt.Errorf("year %d out of expected range (1900-2100)", year)
	// }

	// 8. Return the successfully parsed year and nil error
	return year, nil
}

func findLatestYear(yearString string) (int, error) {
	// 1. Handle empty input
	if strings.TrimSpace(yearString) == "" {
		return 0, fmt.Errorf("input string is empty")
	}

	// 2. Split the string by the comma delimiter
	parts := strings.Split(yearString, ",")

	latestYear := 0         // Initialize with a value lower than any expected year
	foundValidYear := false // Flag to track if we found at least one valid year

	// 3. Iterate through each part
	for _, part := range parts {
		// 4. Trim the backticks (`) from the beginning and end
		trimmedPart := strings.Trim(part, "`")

		// 5. Trim any extra whitespace (optional but good practice)
		trimmedPart = strings.TrimSpace(trimmedPart)

		// 6. Convert the cleaned string part to an integer
		year, err := strconv.Atoi(trimmedPart)
		if err != nil {
			// If conversion fails, log a warning and skip this part
			fmt.Printf("Warning: Skipping invalid entry '%s': %v\n", part, err)
			continue // Move to the next part
		}

		// 7. Compare with the current latest year
		// Update if it's the first valid year found OR if it's greater than the current latest
		if !foundValidYear || year > latestYear {
			latestYear = year
			foundValidYear = true // Mark that we've found at least one valid year
		}
	}

	// 8. Check if any valid year was actually found
	if !foundValidYear {
		return 0, fmt.Errorf("no valid years found in the input string")
	}

	// 9. Return the latest year found
	return latestYear, nil
}

func getNullYearStocks(db *sql.DB, cursor string) (string, string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	var (
		id      string
		ssiCode string
	)
	stmt, err := db.PrepareContext(ctx, "SELECT id, ssi_code FROM stocks WHERE years is NULL AND data_source = $1 AND id > $2 ORDER BY updated_at LIMIT 1")
	if err != nil {
		return "", "", err
	}
	err = stmt.QueryRowContext(ctx, constants.Datasources.SSI, cursor).Scan(&id, &ssiCode)
	if err != nil {
		return "", "", err
	}

	return id, ssiCode, nil
}

func getSSIStocks(db *sql.DB, cursor string) (string, string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	var (
		id    string
		years string
	)
	stmt, err := db.PrepareContext(ctx, "SELECT id, years FROM stocks WHERE data_source = $1 AND id > $2 ORDER BY id ASC LIMIT 1")
	if err != nil {
		return "", "", err
	}

	err = stmt.QueryRowContext(ctx, constants.Datasources.SSI, cursor).Scan(&id, &years)
	if err != nil {
		return "", "", err
	}

	return id, years, nil
}

func getStockWithLatestFinYear(db *sql.DB, cursor string) (string, int, int, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	var (
		id      string
		years   sql.NullString
		quarter sql.NullString
	)
	stmt, err := db.PrepareContext(ctx, "SELECT id, years, rev_last_quarter FROM stocks WHERE id > $1 ORDER BY id LIMIT 1")
	if err != nil {
		return "", 0, 0, err
	}

	err = stmt.QueryRowContext(ctx, cursor).Scan(&id, &years, &quarter)
	if err != nil {
		return "", 0, 0, err
	}

	if !years.Valid {
		return id, 1, 0, nil
	}
	latestYear, err := findLatestYear(years.String)
	if err != nil {
		return "", 0, 0, err
	}

	if !quarter.Valid {
		return id, 1, 0, nil
	}
	latestYearOfQuarter, err := extractYear(quarter.String)
	if err != nil {
		return "", 0, 0, err
	}

	return id, latestYear, latestYearOfQuarter, nil
}
