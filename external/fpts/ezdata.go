package fpts

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/corpix/uarand"
)

type ezDataService struct {
	cfg       *config.Config
	transport *http.Transport
	cookies   []*http.Cookie
}

func NewEzDataService(cfg *config.Config, trans *http.Transport) contract.EzData {
	return &ezDataService{cfg, trans, nil}
}

func (cp *ezDataService) FetchAllStocks(ctx context.Context, offset int) (*model.EzStockList, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: cp.transport,
	}
	ua := uarand.GetRandom()
	var jsonStr = []byte(`{
		"start": ` + fmt.Sprintf("%d", offset) + `,
		"max": 25,
		"sortColumn": "stock_code",
		"sortOrder": "ASC",
		"strStockCode": "ALL",
		"strExchange": "-1",
		"strMinistryID": "-1",
		"period": "0",
		"year": ` + fmt.Sprintf("%d", time.Now().Year()) + `,
		"language": "VN"
	}`)

	req, err := http.NewRequest("POST", "https://ezsearch.fpts.com.vn/DataService/DataService.asmx/GetProductList", bytes.NewBuffer(jsonStr))
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", "https://ezsearch.fpts.com.vn")
	req.Header.Set("Referer", "https://ezsearch.fpts.com.vn/Services/EzData/Home.aspx")
	req.Header.Set("Content-Type", "application/json; charset=utf-8")

	response, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	if response.StatusCode == 403 {
		return nil, constants.RequestError.SHOULD_RETRY
	}
	// // Debug
	// content, err := ioutil.ReadAll(response.Body)
	// if err != nil {
	// 	return nil, err
	// }
	// s := strings.TrimSpace(string(content))
	// fmt.Println("=============Response===============")
	// fmt.Println(s)

	stockList := new(model.EzStockList)
	err = json.NewDecoder(response.Body).Decode(stockList)
	if err != nil {
		return nil, err
	}
	return stockList, nil
}

func (cp *ezDataService) FetchFinancialReport(ctx context.Context, fType string, year int, period int, ezID string, path string) error {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: cp.transport,
	}
	ua := uarand.GetRandom()

	// Crawl main page to get cookies
	origin := "https://ezsearch.fpts.com.vn"
	mainUrl := fmt.Sprintf("https://ezsearch.fpts.com.vn/Services/EzData/default2.aspx?s=%s", ezID)
	req, err := http.NewRequest("GET", mainUrl, nil)
	if err != nil {
		return err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", origin)
	req.Header.Set("Content-Type", "text/html; charset=utf-8")

	resp, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return err
	}
	resp.Body.Close()
	cookies := resp.Cookies()

	form := url.Values{}
	form.Add("ProcessLoadRuntime.aspx?s", ezID)
	form.Add("cGroup", "Finance")
	form.Add("cPath", "Services/EzData/FinanceReport")
	form.Add("ReportID", fType) // BSheet & IncSta
	form.Add("pd", "0")
	form.Add("dt", "1")
	form.Add("fy", fmt.Sprintf("%d", year))
	form.Add("cn", fmt.Sprintf("%d", period))
	form.Add("un", "1")

	req, err = http.NewRequest("POST", "https://ezsearch.fpts.com.vn/Services/EzData/ProcessLoadRuntime.aspx", strings.NewReader(form.Encode()))
	if err != nil {
		return err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Origin", origin)
	req.Header.Set("Referer", mainUrl)
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Content-Length", strconv.Itoa(len(form.Encode())))
	req.Header.Set("DNT", "1")
	q := req.URL.Query()
	q.Add("s", ezID)
	q.Add("cGroup", "Finance")
	q.Add("cPath", "Services/EzData/FinanceReport")
	q.Add("ReportID", fType)
	q.Add("pd", "0")
	q.Add("dt", "1")
	q.Add("fy", fmt.Sprintf("%d", year))
	q.Add("cn", fmt.Sprintf("%d", period))
	q.Add("un", "1")
	req.URL.RawQuery = q.Encode()

	for _, cookie := range cookies {
		req.AddCookie(cookie)
	}

	// fmt.Println("=============Request===============")
	// fmt.Printf("%s\n", req.URL.String())
	// fmt.Println(form.Encode())

	response, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return err
	}
	defer response.Body.Close()

	if response.StatusCode == 403 {
		return constants.RequestError.SHOULD_RETRY
	}

	// Debug
	// content, err := ioutil.ReadAll(response.Body)
	// if err != nil {
	// 	return err
	// }
	// s := strings.TrimSpace(string(content))
	// fmt.Println("=============Response===============")
	// fmt.Println(s)

	f, err := os.Create(path)
	if err != nil {
		return err
	}
	defer f.Close()
	buf := new(bytes.Buffer)
	_, err = buf.ReadFrom(response.Body)
	if err != nil {
		return err
	}
	_, err = f.Write(buf.Bytes())
	if err != nil {
		return err
	}

	return nil
}
