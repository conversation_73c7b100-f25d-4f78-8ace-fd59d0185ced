package vsd

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/PuerkitoBio/goquery"
	"github.com/corpix/uarand"
)

type vsdService struct {
	cfg       *config.Config
	transport *http.Transport
}

func NewVSD(cfg *config.Config, trans *http.Transport) contract.Vsd {
	return &vsdService{cfg, trans}
}

func (vs *vsdService) FetchDelistStocksCache(ctx context.Context, offset int) ([]*model.DelistedStocks, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: vs.transport,
	}
	ua := uarand.GetRandom()

	forgeryURL := "https://www.vsd.vn/vi/tra-cuu-thong-ke/TK_MACK_HUYDK?tab=5"

	req, err := http.NewRequest("GET", forgeryURL, nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("Accept", "*/*")
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")

	resp, err := client.Do(req)
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	} else if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	forgeryCookies := resp.Cookies()

	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}
	token := ""
	doc.Find(`meta[name="__VPToken"]`).Each(func(i int, s *goquery.Selection) {
		token = s.AttrOr("content", "")
	})

	var jsonStr = []byte(fmt.Sprintf(`{"SearchKey":"|01/01/0001|01/01/0001||VI","CurrentPage":%d,"RecordOnPage":10,"OrderBy":"NgayHieuLuc","OrderType":"DESC"}`, offset))

	req, err = http.NewRequest(http.MethodPost, "https://www.vsd.vn/mack-huydk/search", bytes.NewBuffer(jsonStr))
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	req.Header.Set("__VPToken", token)
	req.Header.Set("Origin", "https://www.vsd.vn")
	req.Header.Set("Referer", forgeryURL)
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	for _, cookie := range forgeryCookies {
		req.AddCookie(cookie)
	}

	fmt.Println("=============Request===============")
	fmt.Printf("%s?%s\n", req.URL.String(), token)

	response, err := client.Do(req)
	var e net.Error
	if errors.As(err, &e) && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	}
	defer response.Body.Close()

	if response.StatusCode == 403 {
		return nil, constants.RequestError.SHOULD_RETRY
	}

	// Debug
	//content, err := io.ReadAll(response.Body)
	//if err != nil {
	//	return nil, err
	//}
	//s := strings.TrimSpace(string(content))
	//fmt.Println("=============Response===============")
	//fmt.Println(s)

	doc, err = goquery.NewDocumentFromReader(response.Body)
	if err != nil {
		return nil, err
	}
	var delStocks []*model.DelistedStocks
	doc.Find("table#tblListMaCKHuyDK tbody tr").Each(func(i int, s *goquery.Selection) {
		delStock := new(model.DelistedStocks)
		s.Find("td").Each(func(j int, ss *goquery.Selection) {
			if j == 1 {
				delStock.ID = ss.Text()
			} else if j == 4 {
				delStock.Type = ss.Text()
			} else if j == 6 {
				delStock.Day, err = time.Parse("02/01/2006", ss.Text())
				if err != nil {
					fmt.Println(err)
				}
			} else if j == 7 {
				delStock.Reason = ss.Text()
			}
		})
		if delStock.Type == "Cổ phiếu" {
			delStocks = append(delStocks, delStock)
		}
	})
	return delStocks, nil
}
