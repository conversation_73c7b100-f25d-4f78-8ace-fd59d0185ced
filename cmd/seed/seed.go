package main

import (
	"context"
	"crypto/tls"
	"database/sql"
	"encoding/json"
	"flag"
	"fmt"
	"github.com/007lock/simon-homestead/pkg/analysis"
	"github.com/007lock/simon-homestead/pkg/chatbot"
	"go.uber.org/zap"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strings"
	"time"

	hmoney "github.com/007lock/simon-homestead/external/24hmoney"
	"github.com/007lock/simon-homestead/external/cafef"
	"github.com/007lock/simon-homestead/external/simplize"
	"github.com/007lock/simon-homestead/external/ssi"
	"github.com/007lock/simon-homestead/external/vietstock"
	"github.com/007lock/simon-homestead/external/vsd"
	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	_ "github.com/lib/pq"
	_ "github.com/mithrandie/csvq-driver"
	"github.com/spf13/viper"
)

func main() {
	// Flag
	downloadFlag := flag.String("download", "", "Download financial, prices and new stock")
	rmCacheFinFlag := flag.Int("rm-fin", 0, "Remove cache of download financial stock")
	downloadFinFlag := flag.Int("download-fin", 0, "Download financial stock in year(e.g: 2025)")
	downloadQuarterlyFinFlag := flag.String("download-q-fin", "", "Download quarter financial stock in the forth of the year(e.g: Q4/2024)")
	switchSSIFlag := flag.Bool("switch-ssi", false, "Switch to ssi for obsolete stock flag")
	calFlag := flag.String("cal", "", "Calculate stock ratio")
	removeFlag := flag.String("rm", "", "Remove stocks")
	migrateFlag := flag.Bool("migrate", false, "Creating  migration")
	flag.Usage = func() {
		fmt.Fprintf(flag.CommandLine.Output(), `usage: %s`, os.Args[0])
		flag.PrintDefaults()
	}
	flag.Parse()

	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err := viper.Unmarshal(&cfg)
	if err != nil {
		panic(err)
	}

	// Logger
	rawJSON := []byte(`{
		"level": "debug",
		"encoding": "json",
		"outputPaths": ["stdout"],
		"errorOutputPaths": ["stderr"],
		"encoderConfig": {
		  "messageKey": "message",
		  "levelKey": "level",
		  "levelEncoder": "lowercase"
		}
	  }`)
	var zapCfg zap.Config
	if err := json.Unmarshal(rawJSON, &zapCfg); err != nil {
		panic(err)
	}
	zapLogger, err := zapCfg.Build()
	if err != nil {
		panic(err)
	}
	defer zapLogger.Sync()

	fmt.Println(cfg.PrettyPrint())

	// dialer, err := proxy.SOCKS5("tcp", "127.0.0.1:12050", nil, proxy.Direct)
	// if err != nil {
	// 	panic(err)
	// }
	trans := &http.Transport{
		MaxIdleConns:        10,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     10 * time.Second,
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
		// Dial:                dialer.Dial,
	}

	// // Telegram
	// tl := telegram.NewTelegram(cfg, trans)

	// }
	// // File storage
	// st, err := storage.NewGoogleDriveStorage(cfg, rc)
	// if err != nil {
	// 	if err != constants.AuthenticationError.EXPIRED {
	// 		panic(err)
	// 	}
	// }

	// Vietstock service
	vs := vietstock.NewVietstock(cfg, zapLogger, trans)

	// Simplize service
	simp := simplize.NewSimpService(cfg, trans)

	// 24hmoney service
	hmoney := hmoney.NewHmoneyService(cfg, trans)

	// Cophieu68 service
	// cp := cophieu68.NewCophieu68(cfg)

	// Cophieu68 service
	sp := simplize.NewSimpService(cfg, trans)

	// Cafef service
	cff := cafef.NewCafefService(cfg, trans)

	// Vsd service
	vsd := vsd.NewVSD(cfg, trans)

	// Fialda service
	// fl := fialda.NewFialda(cfg, trans)

	// SSI service
	ssi := ssi.NewSSI(cfg, trans)

	// Database homestead
	parsedURL, err := url.Parse(cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	db, err := sql.Open(parsedURL.Scheme, cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := db.Close(); err != nil {
			panic(err)
		}
	}()

	// Database financial report
	dbCafeFin, err := sql.Open("csvq", cfg.StoragePath.CafFin)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbCafeFin.Close(); err != nil {
			panic(err)
		}
	}()

	dbSSCFin, err := sql.Open("csvq", cfg.StoragePath.SSCFin)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbSSCFin.Close(); err != nil {
			panic(err)
		}
	}()

	dbSSIFin, err := sql.Open("csvq", cfg.StoragePath.SSIFin)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbSSIFin.Close(); err != nil {
			panic(err)
		}
	}()

	// Database stock prices history
	dbStockPrice, err := sql.Open("csvq", cfg.StoragePath.StockPrices)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbStockPrice.Close(); err != nil {
			panic(err)
		}
	}()

	// Database vsd delist
	dbVsd, err := sql.Open("csvq", cfg.StoragePath.VSD)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbVsd.Close(); err != nil {
			panic(err)
		}
	}()

	now := time.Now()
	loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
	now = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc)

	finAnalytic := analysis.NewAnalysis(zapLogger, db, dbSSIFin, dbCafeFin)
	// Switch obsolete stock to ssi
	if switchSSIFlag != nil && *switchSSIFlag {
		err = ssi.SwitchRustyFinancialStock(db)
		if err != nil {
			panic(err)
		}
	}

	if rmCacheFinFlag != nil && *rmCacheFinFlag > 0 {
		fmt.Println("Remove downloaded financial report in", *rmCacheFinFlag)
		err = updateAllNullYears(db, *rmCacheFinFlag)
		if err != nil {
			panic(err)
		}
	}

	// Get financial report of all stocks in year
	if downloadFinFlag != nil && *downloadFinFlag > 0 {
		downloadYear := *downloadFinFlag
		fmt.Println("Download financial report in", downloadYear)
		numberOnlyReg, err := regexp.Compile(`\d+`)
		if err != nil {
			panic(err)
		}
		negativeReg, err := regexp.Compile(`\(.+?\)`)
		if err != nil {
			panic(err)
		}

		if false {
			err = cafef.DownloadFinReportCache(cfg, db, cff, numberOnlyReg, negativeReg, downloadYear)
			if err != nil {
				panic(err)
			}
			err = cafef.CafefExtractFinancialData(cfg, db, dbCafeFin, numberOnlyReg, negativeReg, downloadYear)
			if err != nil {
				panic(err)
			}
		}
		err = ssi.DownloadAllStockSinceYear(db, dbSSIFin, cfg, downloadYear)
		if err != nil {
			panic(err)
		}

		err = ssi.RemoveZeroColumn(db, dbSSIFin, numberOnlyReg)
		if err != nil {
			panic(err)
		}
	}

	// Download price
	if downloadFlag != nil && strings.Contains(*downloadFlag, "prices") {
		// Download prices from 3pm
		err = downloadPriceSimp(cfg, sp, db)
		if err != nil {
			panic(err)
		}
	}

	// Download org code ssi
	if downloadFlag != nil && strings.Contains(*downloadFlag, "ssi-code") {
		ssiCodes, err := ssi.FetchStockCodes()
		if err != nil {
			panic(err)
		}
		err = ssi.StoreTradeStock(db, ssiCodes)
		if err != nil {
			panic(err)
		}
	}

	// Get all stocks
	if downloadFlag != nil && strings.Contains(*downloadFlag, "new") {
		err = getAllNewStocks(db, vs)
		if err != nil {
			panic(err)
		}
		err = updateNewStockInformation(db, vs)
		if err != nil {
			panic(err)
		}
		// Update ssi code
		ssiCodes, err := ssi.FetchStockCodes()
		if err != nil {
			panic(err)
		}
		err = ssi.StoreTradeStock(db, ssiCodes)
		if err != nil {
			panic(err)
		}
		// Download fin report
		err = ssi.DownloadAllStock(db, dbSSIFin, cfg)
		if err != nil {
			panic(err)
		}

		// Clean up empty stock
		err = cleanUpEmptyYearStock(db)
		if err != nil {
			panic(err)
		}

		// Dump to file
		fmt.Println("Dump all stocks to file...")
		err = chatbot.DumpStockFromDB(db, cfg)
		if err != nil {
			panic(err)
		}
		return
	}

	// Download the latest financial quarterly
	if strings.Contains(*downloadQuarterlyFinFlag, "Q") {
		quarter := *downloadQuarterlyFinFlag
		err = simplize.DownloadNewestQuarterly(db, simp, quarter)
		if err != nil {
			panic(err)
		}
		return
	}

	// Download bank interest in 6 months
	if downloadFlag != nil && strings.Contains(*downloadFlag, "interest") {
		interest, err := fetchBankInterest(db, hmoney)
		if err != nil {
			panic(err)
		}
		fmt.Println("Maximum 6 months interest is", interest)
	}

	// Download dividends
	if downloadFlag != nil && strings.Contains(*downloadFlag, "div") {
		err = downloadDividend(vs, db)
		if err != nil {
			panic(err)
		}
	}

	if removeFlag != nil && strings.Contains(*removeFlag, "delist") {
		err = DelistStocks(cfg, vsd, dbVsd, db, now)
		if err != nil {
			panic(err)
		}
	}

	// Calculate sd prices
	if downloadFlag != nil && strings.Contains(*calFlag, "ratio") {
		err = calculateStandardDeviationPrices(db, dbStockPrice)
		if err != nil {
			panic(err)
		}

		err = calculateExpectedValuePrices(db, dbStockPrice)
		if err != nil {
			panic(err)
		}

		// Calculate financial ratio
		err = calculateFinancialRatio(db, finAnalytic)
		if err != nil {
			panic(err)
		}
	}

	if migrateFlag != nil && *migrateFlag {
		fmt.Println("Creating database migration...")
		err = CreateMigration(cfg)
		if err != nil {
			panic(err)
		}
	}
}

type transaction func(ctx context.Context, params ...interface{}) error

func middewareTransaction(db *sql.DB, ts transaction, params ...interface{}) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	tx, err := db.Begin()
	if err != nil {
		return err
	}
	ctx = context.WithValue(ctx, constants.ContextTransactionKey, tx)
	err = ts(ctx, params...)
	if err != nil {
		fmt.Println(err)
		return tx.Rollback()
	}
	return tx.Commit()
}

func sleepRandom(limit int) {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	n := r.Intn(limit) // n will be between 0 and limit
	fmt.Printf("Sleeping %d seconds...\n", n)
	time.Sleep(time.Duration(n) * time.Second)
}

type retryRequest func(params ...interface{}) (interface{}, error)

func middlewareRetryRequest(rt retryRequest, params ...interface{}) interface{} {
	for {
		result, err := rt(params...)
		if err != nil {
			if err == constants.RequestError.SHOULD_RETRY {
				sleepRandom(20)
			} else {
				fmt.Println(err)
				return nil
			}
			// fmt.Println(err)
			// sleepRandom(20)
		} else {
			return result
		}
	}
}

func requestPriceTradingStock(params ...interface{}) (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	vs := params[0].(contract.Vietstock)
	stockCode := fmt.Sprintf("%s", params[1])
	return vs.FetchPriceStock(ctx, stockCode)
}

func requestAllTradingStocks(params ...interface{}) (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	vs := params[0].(contract.Vietstock)
	return vs.FetchAllStocks(ctx)
}

func cleanUpEmptyYearStock(db *sql.DB) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err := db.ExecContext(ctx, `DELETE FROM stocks WHERE years = ''`)
	if err != nil {
		return err
	}
	return nil
}
