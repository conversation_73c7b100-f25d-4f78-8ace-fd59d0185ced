package main

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
)

func CheckFileCacheExist(dbVsd *sql.DB, year string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err := dbVsd.Query(fmt.Sprintf("SELECT * FROM `%s.csv` LIMIT 1", year))
	if err != nil {
		_, err = dbVsd.ExecContext(ctx, fmt.Sprintf("CREATE TABLE `%s.csv`(id,day,reason,unix_ts)", year))
		if err != nil {
			return err
		}
	}
	return nil
}

func DelistStocks(cfg *config.Config, vsd contract.Vsd, dbVsd *sql.DB, db *sql.DB, now time.Time) error {
	yearNow := now.Year()
	offset := 0

	err := CheckFileCacheExist(dbVsd, now.Format("2006"))
	if err != nil {
		return err
	}

	for {
		err := fetchStockCache(vsd, dbVsd, yearNow, offset)
		if err != nil {
			if errors.Is(err, constants.CommonError.ERROR_NOT_FOUND) {
				break
			} else {
				return err
			}
		}
		offset++
		// sleepRandom(10)
	}
	offset = 0
	// Clean up stocks
	for {
		r := dbVsd.QueryRow(fmt.Sprintf("SELECT id, day, reason FROM `%d.csv` ORDER BY unix_ts DESC LIMIT 1 OFFSET %d", yearNow, offset))
		var (
			stock  string
			day    string
			reason sql.NullString
		)
		err := r.Scan(&stock, &day, &reason)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil
			}
			return err
		}
		offset++
		var stmt *sql.Stmt
		stmt, err = db.Prepare("SELECT id FROM stocks WHERE id = $1")
		if err != nil {
			return err
		}

		r = stmt.QueryRow(stock)
		var (
			id string
		)
		err = r.Scan(&id)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}
		if id != "" {
			err = removeStock(cfg, db, id)
			if err != nil {
				return err
			}
			fmt.Println("Delisted", id, day, reason.String)
		}
	}
}

func fetchStockCache(vsdService contract.Vsd, dbVsd *sql.DB, stop int, offset int) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	delStocks, err := vsdService.FetchDelistStocksCache(ctx, offset)
	if err != nil {
		return err
	}

	for _, stock := range delStocks {
		tableName := strconv.Itoa(stock.Day.Year())
		var countExisting int64
		err := dbVsd.QueryRowContext(ctx, fmt.Sprintf("SELECT count(*) FROM `%s.csv` WHERE id = ? LIMIT 1", tableName), stock.ID).Scan(&countExisting)
		if err != nil {
			return err
		}
		isInsert := countExisting == 0
		upsertSQL := fmt.Sprintf("REPLACE INTO `%s.csv`(id, day, reason, unix_ts) USING(id) VALUES(?,?,?,?)", tableName)
		if isInsert {
			upsertSQL = fmt.Sprintf("INSERT INTO `%s.csv`(id, day, reason, unix_ts) VALUES(?,?,?,?)", tableName)
		}
		stmt, err := dbVsd.PrepareContext(ctx, upsertSQL)
		if err != nil {
			return err
		}

		_, err = stmt.ExecContext(ctx, stock.ID, stock.Day.Format("02/01/2006"), stock.Reason, stock.Day.Unix())
		if err != nil {
			return err
		}
	}
	// Check stop
	for _, stock := range delStocks {
		if stock.Day.Year() < stop {
			return constants.CommonError.ERROR_NOT_FOUND
		}
	}

	return nil
}

func removeStock(cfg *config.Config, db *sql.DB, stock string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Remove file
	var files []string
	files, err := filepath.Glob(fmt.Sprintf("%s/*_%s-*", cfg.StoragePath.CafefData, stock))
	if err != nil {
		return err
	}
	for _, f := range files {
		if err = os.Remove(f); err != nil {
			return err
		}
	}
	files, err = filepath.Glob(fmt.Sprintf("%s/%s.*", cfg.StoragePath.CafFin, stock))
	if err != nil {
		return err
	}
	for _, f := range files {
		if err = os.Remove(f); err != nil {
			return err
		}
	}
	files, err = filepath.Glob(fmt.Sprintf("%s/%s.*", cfg.StoragePath.StockPrices, stock))
	if err != nil {
		return err
	}
	for _, f := range files {
		if err = os.Remove(f); err != nil {
			return err
		}
	}
	stmt, err := db.PrepareContext(ctx, "DELETE FROM stocks WHERE id = $1")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, stock)
	if err != nil {
		return err
	}
	return nil
}
