package main

import (
	"context"
	"database/sql"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
)

func fetchBankInterest(db *sql.DB, hmoney contract.Hmoney) (float64, error) {
	ctx, cancel := context.WithTimeout(context.WithValue(context.Background(), constants.ContextTransactionKey, db), 10*time.Second)
	defer cancel()

	now := time.Now()
	isInit := false
	sixMonthInterest := 0.0
	var createdAt time.Time
	r := db.QueryRowContext(ctx, "SELECT six_month_interest, created_at FROM bank_interests ORDER BY created_at DESC LIMIT 1")
	err := r.<PERSON>an(&sixMonthInterest, &createdAt)
	if err != nil && err != sql.ErrNoRows {
		return 0, err
	}
	isInit = (err == sql.ErrNoRows)
	// 1 day ago
	count := 23 * 60
	then := now.Add(time.Duration(-count) * time.Minute)
	// fmt.Println(createdAt.Format(time.RFC3339), then.Format(time.RFC3339))
	if isInit || createdAt.Before(then) {
		sixMonthInterest, err = hmoney.FetchBankInterestSixMonthChrome(ctx)
		if err != nil {
			return 0, err
		}

		stmt, err := db.PrepareContext(ctx, `INSERT INTO bank_interests(monthly, six_month_interest, created_at) VALUES($1, $2, $3) ON CONFLICT (monthly) 
												DO 
											   UPDATE SET six_month_interest = $2, created_at = $3`)
		if err != nil {
			return 0, err
		}
		_, err = stmt.ExecContext(ctx, now.Format("012006"), sixMonthInterest, now)
		if err != nil {
			return 0, err
		}
	}
	return sixMonthInterest * 100, nil
}
