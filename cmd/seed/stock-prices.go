package main

import (
	"context"
	"database/sql"
	"encoding/csv"
	"errors"
	"fmt"
	"github.com/007lock/simon-homestead/pkg/util"
	"io"
	"os"
	"strconv"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
)

func downloadPriceSimp(cfg *config.Config, sp contract.Simplize, db *sql.DB) error {
	now := time.Now()
	cursor := ""
	for {
		stockCode, err := getOutdatedStockCursor(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return err
		}
		cursor = stockCode

		// check file cache
		isFetchStock := false
		stockFilePath := fmt.Sprintf("%s/%s.csv", cfg.StoragePath.StockPrices, stockCode)
		fileInfo, err := os.Stat(stockFilePath)
		if os.IsNotExist(err) {
			isFetchStock = true
		} else {
			modificationTime := fileInfo.ModTime()
			if now.Sub(modificationTime).Hours() >= 24 {
				isFetchStock = true
			}
		}
		if isFetchStock {
			fmt.Printf("Downloading price %s\n", stockCode)
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			err = sp.FetchStockPrices(ctx, stockCode, "1y")
			if err != nil {
				fmt.Println(err)
			}
			cancel()
		}

	}
	return nil
}

func convertDatetoUnixFast(cfg *config.Config, stock string) error {
	stockFilePath := fmt.Sprintf("%s/%s.csv", cfg.StoragePath.StockPrices, stock)
	stockFilePathW := fmt.Sprintf("%s/%s-temp.csv", cfg.StoragePath.StockPrices, stock)
	// Path file
	csvIn, err := os.Open(stockFilePath)
	if err != nil {
		return err
	}
	defer csvIn.Close()
	reader := csv.NewReader(csvIn)

	csvOut, err := os.OpenFile(stockFilePathW, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0755)
	if err != nil {
		return err
	}
	writer := csv.NewWriter(csvOut)
	defer csvOut.Close()
	var record []string

	for {
		rec, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		record = append(record[:0], rec...)
		if rec[1] == "<DTYYYYMMDD>" {
			record = append(record, "unix_ts")
		} else {
			d, err := time.Parse("20060102", rec[1])
			if err != nil {
				return err
			}
			record = append(record, strconv.FormatInt(d.Unix(), 10))
		}
		writer.Write(record)
	}
	writer.Flush()
	// Swap file
	err = os.Remove(stockFilePath)
	if err != nil {
		return err
	}
	err = os.Rename(stockFilePathW, stockFilePath)
	if err != nil {
		return err
	}
	return nil
}

func calculateStandardDeviationPrices(db *sql.DB, dbStockPrice *sql.DB) error {
	cursor := ""

	now := time.Now()
	later52w := now.AddDate(-1, 0, 0)

	for {
		stock, err := getStockCursor(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return err
		}
		cursor = stock.ID
		prices, lastPrice, err := getClosePrices(dbStockPrice, stock.ID, later52w.Unix(), now.Unix())
		if err != nil {
			fmt.Println(err)
		}
		//mean := stat.Mean(prices, nil)
		//variance := stat.Variance(prices, nil)
		//stdDev := math.Sqrt(variance)

		mean, stdDev, _ := util.CalSlopeSigma(prices, false)
		fmt.Println(stock.ID, mean, stdDev)
		err = middewareTransaction(db, updateSD, stock.ID, stdDev, mean, lastPrice)
		if err != nil {
			fmt.Println(err)
		}
	}
	return nil
}

func getClosePrices(dbStockPrice *sql.DB, stockCode string, from int64, to int64) ([]float64, float64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	stmt, err := dbStockPrice.PrepareContext(ctx, "SELECT `close`, `high`, `low` FROM `"+stockCode+".csv` WHERE date >= ? AND date <= ? ORDER BY date ASC")
	if err != nil {
		return nil, 0, err
	}

	rs, err := stmt.QueryContext(ctx, from, to)
	if err != nil {
		return nil, 0, err
	}
	defer rs.Close()
	var (
		closePrices []float64
		closePrice  float64
		highPrice   float64
		lowPrice    float64
	)
	for rs.Next() {
		if err = rs.Scan(&closePrice, &highPrice, &lowPrice); err != nil {
			return nil, 0, err
		}
		closePrices = append(closePrices, highPrice-lowPrice)
	}
	return closePrices, closePrice, nil
}

func updateSD(ctx context.Context, params ...interface{}) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	stockCode := fmt.Sprintf("%s", params[0])
	sd := params[1].(float64)
	mean := params[2].(float64)
	latestPrice := params[3].(float64)
	stmt, err := tx.Prepare("UPDATE stocks SET price = $1, open_price = $2, sd_price_52w = $3, mean_52w = $4, updated_at=NOW() WHERE id = $5")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, latestPrice, latestPrice, sd, mean, stockCode)
	if err != nil {
		return err
	}

	return nil
}
