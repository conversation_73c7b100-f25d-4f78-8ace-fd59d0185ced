package main

import (
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/pkg/config"
)

func CreateMigration(cfg *config.Config) error {
	rltMigrationPath := strings.Replace(cfg.Database.Migration, "file://", "", 1)

	// Remove file
	numberLatestReg, err := regexp.Compile(`[\/\\]([0-9]+)\_init\_data\.up\.sql`)
	if err != nil {
		return err
	}
	schemaReg, err := regexp.Compile(`database\_schema\.up\.sql`)
	if err != nil {
		return err
	}
	// Switch migration
	var files []string
	files, err = filepath.Glob(fmt.Sprintf("%s/*.up.sql", rltMigrationPath))
	if err != nil {
		return err
	}
	latestSeed := ""
	schemaFile := ""
	seedFile := ""
	for _, f := range files {
		if numberLatestReg.MatchString(f) {
			match := numberLatestReg.FindAllStringSubmatch(f, 1)
			latestSeed = match[0][1]
			seedFile = f
		}
		if schemaReg.MatchString(f) {
			schemaFile = f
		}
	}
	if latestSeed == "" || schemaFile == "" || seedFile == "" {
		return fmt.Errorf("%s", "The old files couldn't found")
	}
	// Copy schema file
	fin, err := os.Open(schemaFile)
	if err != nil {
		return err
	}

	fout, err := os.OpenFile(fmt.Sprintf("%s/%s_database_schema.up.sql", rltMigrationPath, latestSeed), os.O_RDWR|os.O_CREATE|os.O_EXCL, 0666)
	if err != nil {
		return err
	}
	_, err = io.Copy(fout, fin)
	if err != nil {
		return err
	}
	fin.Close()
	fout.Close()

	// Clean up old file
	err = os.Remove(schemaFile)
	if err != nil {
		return err
	}
	err = os.Remove(seedFile)
	if err != nil {
		return err
	}

	// Migration creation
	filename := fmt.Sprintf("%s/%d_%s", rltMigrationPath, time.Now().Unix(), "init_data.up.sql")
	err = createFile(filename)
	if err != nil {
		return err
	}
	absFilename, err := filepath.Abs(filename)
	if err != nil {
		return err
	}
	args := []string{
		cfg.Database.URL,
		absFilename,
	}

	// Execute psql command
	cmd := exec.Command("./pgdump.sh", args...)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return err
	}
	fmt.Printf("%s\n", stdoutStderr)
	return nil
}

func createFile(filename string) error {
	// create exclusive (fails if file already exists)
	// os.Create() specifies 0666 as the FileMode, so we're doing the same
	f, err := os.OpenFile(filename, os.O_RDWR|os.O_CREATE|os.O_EXCL, 0666)

	if err != nil {
		return err
	}

	return f.Close()
}
