package main

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/007lock/simon-homestead/pkg/analysis"
	"regexp"
	"strconv"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/mithrandie/csvq/lib/query"
)

func downloadDividend(vs contract.Vietstock, db *sql.DB) error {
	// Seeding
	cursor := ""
	for {
		stockCode, err := getOutdatedStockCursor(db, cursor)
		if errors.Is(err, sql.ErrNoRows) {
			break
		}
		cursor = stockCode

		fmt.Printf("Downloading dividend %s\n", stockCode)
		yearDiv, minYear, maxYear, err := downloadStockDividends(vs, stockCode)
		if errors.Is(err, sql.ErrNoRows) {
			break
		}
		totalDiv := 0.0
		for year := minYear; year <= maxYear; year++ {
			div := yearDiv[year]
			totalDiv += div
		}
		yearNo := float64(len(yearDiv))
		mDiv := 0.0
		if yearNo > 0 {
			mDiv = totalDiv / yearNo
		}
		err = middewareTransaction(db, updateDividendCrawler, stockCode, mDiv)
		if err != nil {
			return err
		}
		fmt.Println(stockCode, mDiv)
	}
	return nil
}

func downloadStockDividends(vs contract.Vietstock, stockCode string) (map[int]float64, int, int, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	yearDiv := make(map[int]float64)
	maxYear := 0
	minYear := 0
	page := 1
DOWLOADDIV:
	for {
		events, err := vs.FetchDividendStock(ctx, stockCode, page)
		if err != nil {
			if errors.Is(err, constants.CommonError.ERROR_NOT_FOUND) {
				break
			}
			if errors.Is(err, constants.RequestError.SHOULD_RETRY) {
				sleepRandom(10)
				goto DOWLOADDIV
			}
			return nil, minYear, maxYear, err
		}
		page++
		regDate := regexp.MustCompile(`Date\(([^)]+)`)

		for _, event := range events {
			subMatch := regDate.FindStringSubmatch(event.Time)
			if len(subMatch) <= 1 {
				continue
			}
			i, err := strconv.ParseInt(subMatch[1], 10, 64)
			if err != nil {
				return nil, minYear, maxYear, err
			}
			eventTime := time.UnixMilli(i)
			rate, err := strconv.ParseFloat(event.Rate, 64)
			if err != nil {
				return nil, minYear, maxYear, err
			}
			if maxYear == 0 || minYear > eventTime.Year() {
				minYear = eventTime.Year()
			}
			if maxYear == 0 || maxYear < eventTime.Year() {
				maxYear = eventTime.Year()
			}
			if div, ok := yearDiv[eventTime.Year()]; ok {
				yearDiv[eventTime.Year()] = div + rate/100*10000
			} else {
				yearDiv[eventTime.Year()] = rate / 100 * 10000
			}
		}
	}
	return yearDiv, minYear, maxYear, nil
}

func getAllNewStocks(db *sql.DB, vs contract.Vietstock) error {
	trdStocks := middlewareRetryRequest(requestAllTradingStocks, vs).([]*model.TradingStock)
	for _, trdStock := range trdStocks {
		newStock := ""
		err := middewareTransaction(db, storeTradeStock, trdStock, newStock)
		if err != nil {
			return err
		}
	}
	return nil
}

func calculateFinancialRatio(db *sql.DB, finAnalytic *analysis.Processor) error {
	// Calculate ratio
	cursor := ""
	for {
		stock, err := getStockCursor(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return err
		}
		cursor = stock.ID
		err = finAnalytic.CalculateFinancialRatioOfStock(stock)
		if err != nil {
			return err
		}
	}
	return nil
}

func getStockCursor(db *sql.DB, prevCursor string) (*model.Stock, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	stmt, err := db.PrepareContext(ctx, `SELECT id, price, years, dividend, data_source FROM stocks WHERE id > $1 ORDER BY id LIMIT 1`)
	if err != nil {
		return nil, err
	}
	stock := new(model.Stock)
	if err = stmt.QueryRowContext(ctx, prevCursor).Scan(&stock.ID, &stock.Price, &stock.YearFields, &stock.Dividend, &stock.DataSource); err != nil {
		return nil, err
	}

	return stock, nil
}

func getOutdatedStockCursor(db *sql.DB, prevCursor string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	stmt, err := db.PrepareContext(ctx, `SELECT id FROM stocks WHERE updated_at + interval '1 hour' <= NOW() AND id > $1 ORDER BY id LIMIT 1`)
	if err != nil {
		return "", err
	}
	var stockCode string
	if err = stmt.QueryRowContext(ctx, prevCursor).Scan(&stockCode); err != nil {
		return "", err
	}

	return stockCode, nil
}

func getNullStockSharesCursor(db *sql.DB, prevCursor string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	stmt, err := db.PrepareContext(ctx, `SELECT id FROM stocks WHERE shares IS NULL AND id > $1 ORDER BY id LIMIT 1`)
	if err != nil {
		return "", err
	}
	var stockCode string
	if err = stmt.QueryRowContext(ctx, prevCursor).Scan(&stockCode); err != nil {
		return "", err
	}

	return stockCode, nil
}

func updatePriceTradeStock(ctx context.Context, params ...interface{}) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)

	stock := params[0].(*model.StockTrade)
	var stmt *sql.Stmt
	stmt, err := tx.Prepare("SELECT id FROM stocks WHERE id = $1")
	if err != nil {
		return err
	}
	r := stmt.QueryRowContext(ctx, stock.StockCode)
	var id string

	if err = r.Scan(&id); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			fmt.Printf("%s No Rows. Inserting\n", stock.StockCode)

			stmt, err = tx.Prepare("INSERT INTO stocks(id,price,market_cap,shares,eps,pe,pb,dividend,updated_at) VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9)")
			if err != nil {
				return err
			}
			_, err = stmt.ExecContext(ctx, stock.StockCode, stock.AvrPrice, stock.MarketCapital/1000000000, stock.Klcpny, stock.Eps, stock.Pe, stock.Pb, stock.Dividend, time.Now())
			if err != nil {
				return err
			}
			return nil
		} else if _, ok := err.(query.Error); ok {
			return err
		} else {
			return err
		}
	} else {
		fmt.Printf("Updating %s\n", stock.StockCode)
		stmt, err = tx.Prepare("UPDATE stocks SET price = $1, market_cap = $2, shares = $3, eps = $4, pe = $5, pb = $6, dividend = $7, updated_at = $8 WHERE id = $9")
		if err != nil {
			return err
		}
		_, err = stmt.ExecContext(ctx, stock.AvrPrice, stock.MarketCapital/1000000000, stock.Klcpny, stock.Eps, stock.Pe, stock.Pb, stock.Dividend, time.Now(), stock.StockCode)
		if err != nil {
			return err
		}
	}
	return nil
}

func storeTradeStock(ctx context.Context, params ...interface{}) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)

	stock := params[0].(*model.TradingStock)
	var stmt *sql.Stmt
	stmt, err := tx.Prepare("SELECT id, price FROM stocks WHERE id = $1")
	if err != nil {
		return err
	}
	r := stmt.QueryRowContext(ctx, stock.Sc)
	var (
		id    string
		price float64
	)
	if err = r.Scan(&id, &price); err != nil {
		if err == sql.ErrNoRows {
			fmt.Printf("%s No Rows. Inserting\n", stock.Sc)

			stmt, err = tx.Prepare("INSERT INTO stocks(id,price,dividend,data_source,updated_at) VALUES($1,$2,$3,$4,$5)")
			if err != nil {
				return err
			}
			_, err = stmt.ExecContext(ctx, stock.Sc, stock.Cp, stock.Diviend, constants.Datasources.SSI, time.Now())
			if err != nil {
				return err
			}
			params[1] = stock.Sc
			return nil
		} else if csvqerr, ok := err.(query.Error); ok {
			return fmt.Errorf("unexpected error: number: %d  message: %s", csvqerr.Number(), csvqerr.Message())
		} else {
			return err
		}
	} else {
		stmt, err = tx.Prepare("UPDATE stocks SET price = $1 WHERE id = $2")
		if err != nil {
			return err
		}
		_, err = stmt.ExecContext(ctx, stock.Cp, stock.Sc)
		if err != nil {
			return err
		}
	}
	return nil
}

func updateDividendCrawler(ctx context.Context, params ...interface{}) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	stockCode := params[0].(string)
	mDividend := params[1].(float64)
	stmt, err := tx.Prepare("UPDATE stocks SET mean_dividend=$1, updated_at=NOW() WHERE id=$2")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, mDividend, stockCode)
	if err != nil {
		return err
	}
	return nil
}

func updateNewStockInformation(db *sql.DB, vs contract.Vietstock) error {
	// Get current price stocks
	cursor := ""
	for {
		stockCode, err := getNullStockSharesCursor(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return err
		}
		cursor = stockCode

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		stockPrice, err := vs.FetchPriceStock(ctx, stockCode)
		if err != nil {
			fmt.Println(err)
			continue
		}
		err = middewareTransaction(db, updatePriceTradeStock, stockPrice)
		if err != nil {
			return err
		}
	}
	return nil
}
