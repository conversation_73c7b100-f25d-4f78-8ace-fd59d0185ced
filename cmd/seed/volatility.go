package main

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/007lock/simon-homestead/internal/constants"
	"math"
	"time"
)

func calculateExpectedValuePrices(db *sql.DB, dbStockPrice *sql.DB) error {
	cursor := ""

	now := time.Now()
	later52w := now.AddDate(-1, 0, 0)

	for {
		stock, err := getStockCursor(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return err
		}
		cursor = stock.ID
		prices, err := getClosedPrices(dbStockPrice, stock.ID, later52w.Unix(), now.Unix())
		if err != nil {
			fmt.Println(err)
		}

		increases := countIncreases(prices)
		losses := countLosses(prices)
		total := increases + losses

		maxProfit := maxProfitFinding(prices)
		increasesProportion := float64(increases) / float64(total)

		maxLoss := maxLossFinding(prices)
		lossesProportion := float64(losses) / float64(total)

		expectedValue := maxProfit*increasesProportion*(1-0.01) - maxLoss*lossesProportion*(1+0.01)
		fmt.Println("Expected value of", stock.ID, ":", expectedValue)
		err = middewareTransaction(db, updateExpectedValue, stock.ID, expectedValue)
		if err != nil {
			fmt.Println(err)
		}
	}
	return nil
}

func updateExpectedValue(ctx context.Context, params ...interface{}) error {
	tx := ctx.Value(constants.ContextTransactionKey).(*sql.Tx)
	stockCode := fmt.Sprintf("%s", params[0])
	expectedValue := params[1].(float64)
	stmt, err := tx.Prepare("UPDATE stocks SET expected_value_1y = $1, updated_at=NOW() WHERE id = $2")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, expectedValue, stockCode)
	if err != nil {
		return err
	}

	return nil
}

func getClosedPrices(dbStockPrice *sql.DB, stockCode string, from int64, to int64) ([]float64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	stmt, err := dbStockPrice.PrepareContext(ctx, "SELECT `close` FROM `"+stockCode+".csv` WHERE date >= ? AND date <= ? ORDER BY date ASC")
	if err != nil {
		return nil, err
	}

	rs, err := stmt.QueryContext(ctx, from, to)
	if err != nil {
		return nil, err
	}
	defer rs.Close()
	var (
		closePrices []float64
		closePrice  float64
	)
	for rs.Next() {
		if err = rs.Scan(&closePrice); err != nil {
			return nil, err
		}
		closePrices = append(closePrices, closePrice)
	}
	return closePrices, nil
}

func maxProfitFinding(prices []float64) float64 {
	if len(prices) == 0 {
		return 0
	}

	minPrice := math.MaxFloat64
	maxProfit := 0.0

	for _, price := range prices {
		// Keep track of the minimum price to buy
		if price < minPrice {
			minPrice = price
		}

		// Calculate potential profit and update maxProfit if it's higher
		profit := price - minPrice
		if profit > maxProfit {
			maxProfit = profit
		}
	}

	return maxProfit
}

func countIncreases(prices []float64) int {
	count := 0

	for i := 1; i < len(prices); i++ {
		if prices[i] > prices[i-1] {
			count++
		}
	}

	return count
}

func maxLossFinding(prices []float64) float64 {
	if len(prices) == 0 {
		return 0
	}

	maxPrice := -math.MaxFloat64
	maxLoss := 0.0

	for _, price := range prices {
		// Keep track of the maximum price to sell
		if price > maxPrice {
			maxPrice = price
		}

		// Calculate potential loss and update maxLoss if it's higher
		loss := maxPrice - price
		if loss > maxLoss {
			maxLoss = loss
		}
	}

	return maxLoss
}

func countLosses(prices []float64) int {
	count := 0

	for i := 1; i < len(prices); i++ {
		if prices[i] < prices[i-1] {
			count++
		}
	}

	return count
}
