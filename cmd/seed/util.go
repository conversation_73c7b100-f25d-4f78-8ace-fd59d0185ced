package main

import (
	"context"
	"database/sql"
	"fmt"
	"time"
)

func updateAllNullYears(db *sql.DB, year int) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	stmt, err := db.PrepareContext(ctx, "UPDATE stocks SET years=NULL WHERE years NOT LIKE $1")
	if err != nil {
		return err
	}

	_, err = stmt.ExecContext(ctx, fmt.Sprintf(`%%%d%%`, year))
	if err != nil {
		return err
	}
	return nil
}
