package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"github.com/007lock/simon-homestead/external/cafef"
	"net/http"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/pkg/config"
	_ "github.com/lib/pq"
	_ "github.com/mithrandie/csvq-driver"
	"github.com/spf13/viper"
)

func maincafefbank() {
	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err := viper.Unmarshal(&cfg)
	if err != nil {
		panic(err)
	}
	fmt.Println(cfg.PrettyPrint())

	trans := &http.Transport{
		MaxIdleConns:        10,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     10 * time.Second,
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
		// 	Dial: dialer.Dial,
	}
	cff := cafef.NewCafefService(cfg, trans)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	interest, err := cff.FetchBankInterest(ctx, 12)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%+v\n", interest)
}
