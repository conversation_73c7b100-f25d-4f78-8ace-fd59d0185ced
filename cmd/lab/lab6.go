package main

import (
	"context"
	"crypto/tls"
	"database/sql"
	"github.com/007lock/simon-homestead/external/fireant"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/spf13/viper"
)

func mainfireant() {
	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err := viper.Unmarshal(&cfg)
	if err != nil {
		panic(err)
	}
	// Database homestead
	parsedURL, err := url.Parse(cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	db, err := sql.Open(parsedURL.Scheme, cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := db.Close(); err != nil {
			panic(err)
		}
	}()

	trans := &http.Transport{
		MaxIdleConns:        10,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     10 * time.Second,
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
		// 	Dial: dialer.Dial,
	}
	fa := fireant.NewFireAntService(cfg, trans)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	page := 0
	for page < 13 {
		err = fa.FetchVNIStockPrices(ctx, page)
		if err != nil {
			panic(err)
		}
		page++
	}
}
