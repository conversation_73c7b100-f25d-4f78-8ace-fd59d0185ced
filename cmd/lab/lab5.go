package main

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/spf13/viper"
	"math"
	"net/url"
	"strings"
	"time"
)

func mainpnl() {
	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err := viper.Unmarshal(&cfg)
	if err != nil {
		panic(err)
	}
	// Database homestead
	parsedURL, err := url.Parse(cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	db, err := sql.Open(parsedURL.Scheme, cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := db.Close(); err != nil {
			panic(err)
		}
	}()

	// Database stock prices history
	dbStockPrice, err := sql.Open("csvq", cfg.StoragePath.StockPrices)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbStockPrice.Close(); err != nil {
			panic(err)
		}
	}()

	now := time.Now()
	later52w := now.AddDate(-1, 0, 0)

	prices, err := getClosePrices(dbStockPrice, "VNM", later52w.Unix(), now.Unix())
	if err != nil {
		fmt.Println(err)
	}

	increases := countIncreases(prices)
	losses := countLosses(prices)
	total := increases + losses

	maxProfit := maxProfitFinding(prices)
	increasesProportion := float64(increases) / float64(total)
	fmt.Println("Maximum profit", maxProfit, "Increases ratio", increasesProportion)

	maxLoss := maxLossFinding(prices)
	lossesProportion := float64(losses) / float64(total)
	fmt.Println("Maximum loss", maxLoss, "Losses ratio", lossesProportion)

	expectedValue := maxProfit*increasesProportion - maxLoss*lossesProportion
	fmt.Println("Expected value", expectedValue)
}

func getClosePrices(dbStockPrice *sql.DB, stockCode string, from int64, to int64) ([]float64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	stmt, err := dbStockPrice.PrepareContext(ctx, "SELECT `close` FROM `"+stockCode+".csv` WHERE date >= ? AND date <= ? ORDER BY date ASC")
	if err != nil {
		return nil, err
	}

	rs, err := stmt.QueryContext(ctx, from, to)
	if err != nil {
		return nil, err
	}
	defer rs.Close()
	var (
		closePrices []float64
		closePrice  float64
	)
	for rs.Next() {
		if err = rs.Scan(&closePrice); err != nil {
			return nil, err
		}
		closePrices = append(closePrices, closePrice)
	}
	return closePrices, nil
}

func maxProfitFinding(prices []float64) float64 {
	if len(prices) == 0 {
		return 0
	}

	minPrice := math.MaxFloat64
	maxProfit := 0.0

	for _, price := range prices {
		// Keep track of the minimum price to buy
		if price < minPrice {
			minPrice = price
		}

		// Calculate potential profit and update maxProfit if it's higher
		profit := price - minPrice
		if profit > maxProfit {
			maxProfit = profit
		}
	}

	return maxProfit
}

func countIncreases(prices []float64) int {
	count := 0

	for i := 1; i < len(prices); i++ {
		if prices[i] > prices[i-1] {
			count++
		}
	}

	return count
}

func maxLossFinding(prices []float64) float64 {
	if len(prices) == 0 {
		return 0
	}

	maxPrice := -math.MaxFloat64
	maxLoss := 0.0

	for _, price := range prices {
		// Keep track of the maximum price to sell
		if price > maxPrice {
			maxPrice = price
		}

		// Calculate potential loss and update maxLoss if it's higher
		loss := maxPrice - price
		if loss > maxLoss {
			maxLoss = loss
		}
	}

	return maxLoss
}

func countLosses(prices []float64) int {
	count := 0

	for i := 1; i < len(prices); i++ {
		if prices[i] < prices[i-1] {
			count++
		}
	}

	return count
}
