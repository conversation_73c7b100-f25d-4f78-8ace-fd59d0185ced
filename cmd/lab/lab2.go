package main

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"gonum.org/v1/gonum/stat"
	"net/url"
	"sort"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/util"
	"github.com/spf13/viper"
)

func mainmean() {
	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err := viper.Unmarshal(&cfg)
	if err != nil {
		panic(err)
	}
	// Database homestead
	parsedURL, err := url.Parse(cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	db, err := sql.Open(parsedURL.Scheme, cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := db.Close(); err != nil {
			panic(err)
		}
	}()
	// Database financial report
	dbPrice, err := sql.Open("csvq", cfg.StoragePath.StockPrices)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbPrice.Close(); err != nil {
			panic(err)
		}
	}()

	// get 52 weeks
	now := time.Now()
	later52w := now.AddDate(-1, 0, 0)
	var values []float64
	//bins := 100
	// load from cached
	cursor := ""
	for {
		stockCode, err := getStockCursor(db, cursor)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			panic(err)
		}
		cursor = stockCode

		t2Movement, err := getT2PriceMovement(dbPrice, stockCode, later52w.Unix(), now.Unix())
		if err != nil {
			fmt.Println(err)
		}

		fmt.Println("Add stock", stockCode, len(t2Movement))
		for _, price := range t2Movement {
			values = append(values, price)
		}
	}
	//err = util.HistogramPlot("T2 price movement distribution", values, bins)
	//if err != nil {
	//	panic(err)
	//}

	sort.Float64s(values)
	mean, stdDev, _ := util.CalSlopeSigma(values, false)

	rightCDF := 0.02
	cdf := 1 - stat.CDF(rightCDF, stat.Empirical, values, nil)
	fmt.Println("Mean:", mean, "StdDev:", stdDev, "CDF of", rightCDF*100, "%:", cdf)
}

func getStockCursor(db *sql.DB, prevID string) (id string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	stmt, err := db.PrepareContext(ctx, `SELECT id FROM stocks WHERE id > $1 ORDER BY id LIMIT 1`)
	if err != nil {
		return "", err
	}
	err = stmt.QueryRowContext(ctx, prevID).Scan(&id)
	if err != nil {
		return "", err
	}

	return id, nil
}

func getT2PriceMovement(dbStockPrice *sql.DB, stockCode string, from int64, to int64) ([]float64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	stmt, err := dbStockPrice.PrepareContext(ctx, "SELECT `date`, `open`, `close` FROM `"+stockCode+".csv` WHERE date >= ? AND date <= ? ORDER BY date ASC")
	if err != nil {
		return nil, err
	}

	rs, err := stmt.QueryContext(ctx, from, to)
	if err != nil {
		return nil, err
	}
	defer rs.Close()
	tradeDayPrices := make(map[int64]float64)
	var t2MovementDist []float64
	var (
		tradeDay   int64
		openPrice  float64
		closePrice float64
	)
	for rs.Next() {
		if err = rs.Scan(&tradeDay, &openPrice, &closePrice); err != nil {
			return nil, err
		}
		t := time.Unix(tradeDay, 0)
		tradeDayPrices[tradeDay] = openPrice
		t2Ealier := t.AddDate(0, 0, -2)
		if t2EarlyPrice, ok := tradeDayPrices[t2Ealier.Unix()]; ok {
			t2MovementDist = append(t2MovementDist, closePrice/t2EarlyPrice-1)
		}

	}
	return t2MovementDist, nil
}
