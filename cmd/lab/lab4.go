package main

import (
	"context"
	"crypto/tls"
	"database/sql"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/external/cafef"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/spf13/viper"
)

func maincafef() {
	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err := viper.Unmarshal(&cfg)
	if err != nil {
		panic(err)
	}
	// Database homestead
	parsedURL, err := url.Parse(cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	db, err := sql.Open(parsedURL.Scheme, cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := db.Close(); err != nil {
			panic(err)
		}
	}()
	// Database financial report
	dbFinCafeF, err := sql.Open("csvq", cfg.StoragePath.CafFin)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbFinCafeF.Close(); err != nil {
			panic(err)
		}
	}()
	trans := &http.Transport{
		MaxIdleConns:        10,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     10 * time.Second,
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
		// 	Dial: dialer.Dial,
	}
	cff := cafef.NewCafefService(cfg, trans)

	numberOnlyReg, err := regexp.Compile(`\d+`)
	if err != nil {
		panic(err)
	}
	negativeReg, err := regexp.Compile(`\(.+?\)`)
	if err != nil {
		panic(err)
	}
	err = cafef.DownloadFinReportCache(cfg, db, cff, numberOnlyReg, negativeReg, time.Now().Year()-1)
	if err != nil {
		panic(err)
	}
	err = cafef.CafefExtractFinancialData(cfg, db, dbFinCafeF, numberOnlyReg, negativeReg, 0)
	if err != nil {
		panic(err)
	}

	offset := 0
	for {
		stock, err := getNullYearStocks(db, offset)
		if err != nil {
			if err == sql.ErrNoRows {
				break
			}
			panic(err)
		}
		offset++
		err = removeStock(cfg, db, stock)
		if err != nil {
			panic(err)
		}
	}

}

func removeStock(cfg *config.Config, db *sql.DB, stock string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Remove file
	var files []string
	files, err := filepath.Glob(fmt.Sprintf("%s/*_%s-*", cfg.StoragePath.CafefData, stock))
	if err != nil {
		return err
	}
	for _, f := range files {
		if err = os.Remove(f); err != nil {
			return err
		}
	}
	files, err = filepath.Glob(fmt.Sprintf("%s/%s.*", cfg.StoragePath.CafFin, stock))
	if err != nil {
		return err
	}
	for _, f := range files {
		if err = os.Remove(f); err != nil {
			return err
		}
	}
	files, err = filepath.Glob(fmt.Sprintf("%s/%s.*", cfg.StoragePath.StockPrices, stock))
	if err != nil {
		return err
	}
	for _, f := range files {
		if err = os.Remove(f); err != nil {
			return err
		}
	}
	stmt, err := db.PrepareContext(ctx, "DELETE FROM stocks WHERE id = $1")
	if err != nil {
		return err
	}
	_, err = stmt.ExecContext(ctx, stock)
	if err != nil {
		return err
	}
	return nil
}

func getNullYearStocks(db *sql.DB, offset int) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	var (
		id string
	)
	err := db.QueryRowContext(ctx, fmt.Sprintf("SELECT id FROM stocks WHERE years is NULL ORDER BY id ASC LIMIT 1 OFFSET %d", offset)).Scan(&id)
	if err != nil {
		return "", err
	}

	return id, nil
}
