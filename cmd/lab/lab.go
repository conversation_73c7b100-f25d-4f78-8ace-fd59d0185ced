package main

import (
	"database/sql"
	"encoding/json"
	"github.com/007lock/simon-homestead/pkg/analysis"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	_ "github.com/lib/pq"
	_ "github.com/mithrandie/csvq-driver"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"net/url"
	"strings"
)

func main() {
	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err := viper.Unmarshal(&cfg)
	if err != nil {
		panic(err)
	}
	// fmt.Println(cfg.PrettyPrint())
	// Logger
	rawJSON := []byte(`{
		"level": "debug",
		"encoding": "json",
		"outputPaths": ["stdout"],
		"errorOutputPaths": ["stderr"],
		"encoderConfig": {
		  "messageKey": "message",
		  "levelKey": "level",
		  "levelEncoder": "lowercase"
		}
	  }`)
	var zapCfg zap.Config
	if err := json.Unmarshal(rawJSON, &zapCfg); err != nil {
		panic(err)
	}
	zapLogger, err := zapCfg.Build()
	if err != nil {
		panic(err)
	}
	defer zapLogger.Sync()

	// Database homestead
	parsedURL, err := url.Parse(cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	db, err := sql.Open(parsedURL.Scheme, cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := db.Close(); err != nil {
			panic(err)
		}
	}()

	// Database stock prices history
	dbCafeFin, err := sql.Open("csvq", cfg.StoragePath.CafFin)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbCafeFin.Close(); err != nil {
			panic(err)
		}
	}()

	dbSSIFin, err := sql.Open("csvq", cfg.StoragePath.SSIFin)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbSSIFin.Close(); err != nil {
			panic(err)
		}
	}()

	finAnalytic := analysis.NewAnalysis(zapLogger, db, dbSSIFin, dbCafeFin)
	stmt, err := db.Prepare(`SELECT id, price, years, dividend, data_source FROM stocks WHERE id = $1`)
	if err != nil {
		panic(err)
	}
	stock := new(model.Stock)
	if err = stmt.QueryRow("VNM").Scan(&stock.ID, &stock.Price, &stock.YearFields, &stock.Dividend, &stock.DataSource); err != nil {
		panic(err)
	}
	err = finAnalytic.CalculateFinancialRatioOfStock(stock)
	if err != nil {
		panic(err)
	}
}
