package main

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/007lock/simon-homestead/pkg/chatbot"
	"github.com/007lock/simon-homestead/pkg/config"
	_ "github.com/mithrandie/csvq-driver"
	"github.com/spf13/viper"
)

func main() {
	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err := viper.Unmarshal(&cfg)
	if err != nil {
		panic(err)
	}
	// fmt.Println(cfg.PrettyPrint())

	// // Database homestead
	// parsedURL, err := url.Parse(cfg.Database.URL)
	// if err != nil {
	// 	panic(err)
	// }
	// db, err := sql.Open(parsedURL.Scheme, cfg.Database.URL)
	// if err != nil {
	// 	panic(err)
	// }
	// defer func() {
	// 	if err := db.Close(); err != nil {
	// 		panic(err)
	// 	}
	// }()

	// Database financial report
	dbTestModel, err := sql.Open("csvq", cfg.TemplatePath.PretrainPath)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbTestModel.Close(); err != nil {
			panic(err)
		}
	}()

	// // Pacman
	// pacman, err := filejob.NewPacman(cfg, db)
	// if err != nil {
	// 	panic(err)
	// }
	// err = pacman.ReleaseTheJobEater()
	// if err != nil {
	// 	panic(err)
	// }

	// // Chatbot
	// cb, err := chatbot.NewChatbot(cfg, pacman)
	// if err != nil {
	// 	panic(err)
	// }

	dict, err := chatbot.NewDict(cfg)
	if err != nil {
		panic(err)
	}

	// Print the result using the formatter.
	// fe := mat.Formatted(e, mat.Prefix("    "), mat.Squeeze())
	// fmt.Printf("e = %v\n", fe)

	// Hmm train all scopes
	err = chatbot.TrainAllScopes(cfg, dict)
	if err != nil {
		panic(err)
	}

	lg, err := chatbot.NewLGModel(cfg, dict)
	if err != nil {
		panic(err)
	}

	// Test accuracy
	ctxTimeout, cancelStock := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancelStock()

	rs, err := dbTestModel.QueryContext(ctxTimeout, "SELECT sample, category FROM `test-model.csv`")
	if err != nil {
		panic(err)
	}
	total := 0.0
	accuracy := 0.0
	for rs.Next() {
		var (
			sample   string
			category string
		)
		if err = rs.Scan(&sample, &category); err != nil {
			panic(err)
		}
		total++
		classPredicted, score, err := lg.PredictWithScore(sample)
		if err != nil {
			panic(err)
		}
		realPredicted := classPredicted
		if score < 0.15 {
			realPredicted = ""
		}
		if realPredicted == strings.Trim(category, " ") {
			accuracy++
		} else {
			// Debug
			fmt.Printf("%s: %s(%f) != %s\n", sample, classPredicted, score, category)
		}
	}
	err = rs.Close()
	if err != nil {
		panic(err)
	}
	fmt.Printf("Accuracy: %v\n", accuracy/total)

	// predict := "bổ xung 1000 cổ phiếu gmx"
	// classPredicted, err := nb.Predict(predict)
	// if err != nil {
	// 	panic(err)
	// }
	// fmt.Printf("Final conclusion: %s\n", classPredicted)
	// if classPredicted != "" {
	// 	// Then using hmm to detect entities
	// 	hmm, err := chatbot.NewHMM(cfg, classPredicted, dict)
	// 	if err != nil {
	// 		panic(err)
	// 	}
	// 	entities := hmm.Entities(predict)
	// 	for tag, entity := range entities {
	// 		fmt.Println(tag, entity)
	// 	}
	// }
}
