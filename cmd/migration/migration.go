package main

import (
	"encoding/json"
	"fmt"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/golang-migrate/migrate/v4"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"strings"

	_ "github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
)

func main() {
	// Logger
	rawJSON := []byte(`{
		"level": "debug",
		"encoding": "json",
		"outputPaths": ["stdout"],
		"errorOutputPaths": ["stderr"],
		"encoderConfig": {
		  "messageKey": "message",
		  "levelKey": "level",
		  "levelEncoder": "lowercase"
		}
	  }`)
	var zapCfg zap.Config
	if err := json.Unmarshal(rawJSON, &zapCfg); err != nil {
		panic(err)
	}

	zapLogger, err := zapCfg.Build()
	if err != nil {
		panic(err)
	}
	defer zapLogger.Sync()

	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		zapLogger.Info("Error reading config file from env.yaml")
		panic(err)
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err = viper.Unmarshal(&cfg)
	if err != nil {
		zapLogger.Info("Error init viper")
		panic(err)
	}
	fmt.Println(cfg.PrettyPrint())

	zapLogger.Info("Migration started")
	// Migration
	m, err := migrate.New(
		cfg.Database.Migration,
		cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	err = m.Up()
	if err != nil && err != migrate.ErrNoChange {
		panic(err)
	}
	m.Close()
	zapLogger.Info("Migration successfully")
}
