FROM golang:alpine AS builder
RUN apk add --no-cache \
    && rm -vrf /var/cache/apk/*

WORKDIR /go/src/app
# ENV GO111MODULE=on
# Cache go mod
COPY ./go.mod ./
COPY ./go.sum ./
RUN go mod download
#then copy source files
COPY . .
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -o server main.go
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -o migration cmd/migration/*.go

FROM alpine:3

RUN apk add --no-cache --update ca-certificates curl openssh bash tzdata
#ENV TZ=Asia/Ho_Chi_Minh
RUN rm /bin/sh && ln -s /bin/bash /bin/sh

COPY --from=builder /go/src/app/env-example.yaml /env.yaml
COPY --from=builder /go/src/app/server /server
COPY --from=builder /go/src/app/migration /migration
COPY --from=builder /go/src/app/template /template
COPY --from=builder /go/src/app/precache/bot /precache/bot
COPY --from=builder /go/src/app/precache/stockprices /precache/stockprices
COPY --from=builder /go/src/app/precache/distribution /precache/distribution
COPY --from=builder /go/src/app/migrations /migrations
RUN mkdir -p /precache/download

WORKDIR /
CMD ["/server"]